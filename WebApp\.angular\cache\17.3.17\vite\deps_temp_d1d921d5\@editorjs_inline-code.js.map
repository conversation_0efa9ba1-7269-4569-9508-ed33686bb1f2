{"version": 3, "sources": ["../../../../../node_modules/@editorjs/inline-code/dist/inline-code.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(\".inline-code{background:rgba(250,239,240,.78);color:#b44437;padding:3px 4px;border-radius:5px;margin:0 1px;font-family:inherit;font-size:.86em;font-weight:500;letter-spacing:.3px}\")),document.head.appendChild(e)}}catch(n){console.error(\"vite-plugin-css-injected-by-js\",n)}})();\nconst a = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 8L5 12L9 16\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 8L19 12L15 16\"/></svg>';\nclass s {\n  constructor({ api: t }) {\n    this.tag = \"CODE\", this.api = t, this.button = null, this.iconClasses = {\n      base: this.api.styles.inlineToolButton,\n      active: this.api.styles.inlineToolButtonActive\n    };\n  }\n  /**\n   * Class name for term-tag\n   *\n   * @type {string}\n   */\n  static get CSS() {\n    return \"inline-code\";\n  }\n  /**\n   * Specifies Tool as Inline Toolbar Tool\n   *\n   * @return {boolean}\n   */\n  static get isInline() {\n    return !0;\n  }\n  /**\n   * Create button element for Toolbar\n   *\n   * @return {HTMLElement}\n   */\n  render() {\n    return this.button = document.createElement(\"button\"), this.button.type = \"button\", this.button.classList.add(this.iconClasses.base), this.button.innerHTML = this.toolboxIcon, this.button;\n  }\n  /**\n   * Wrap/Unwrap selected fragment\n   *\n   * @param {Range} range - selected fragment\n   */\n  surround(t) {\n    var n;\n    if (!t)\n      return;\n    let e = this.api.selection.findParentTag(this.tag, s.CSS);\n    e ? this.unwrap(e) : (n = t.commonAncestorContainer.parentElement) != null && n.querySelector(this.tag) || this.wrap(t);\n  }\n  /**\n  * Wrap selection with term-tag\n  *\n  * @param {Range} range - selected fragment\n  */\n  wrap(t) {\n    let e = document.createElement(this.tag);\n    e.classList.add(s.CSS), e.appendChild(t.extractContents()), t.insertNode(e), this.api.selection.expandToTag(e);\n  }\n  /**\n   * Unwrap term-tag\n   *\n   * @param {HTMLElement} termWrapper - term wrapper tag\n   */\n  unwrap(t) {\n    var o;\n    this.api.selection.expandToTag(t);\n    const e = window.getSelection();\n    if (!e)\n      return;\n    const n = e.getRangeAt(0), i = n.extractContents();\n    (o = t.parentNode) == null || o.removeChild(t), n.insertNode(i), e.removeAllRanges(), e.addRange(n);\n  }\n  /**\n   * Check and change Term's state for current selection\n   * \n   * @return {boolean}\n   */\n  checkState() {\n    const t = this.api.selection.findParentTag(this.tag, s.CSS);\n    return this.button && this.button.classList.toggle(this.iconClasses.active, !!t), !!t;\n  }\n  /**\n   * Get Tool icon's SVG\n   * @return {string}\n   */\n  get toolboxIcon() {\n    return a;\n  }\n  /**\n   * Sanitizer rule\n   * @return {SanitizerConfig}\n   */\n  static get sanitize() {\n    return {\n      code: {\n        class: s.CSS\n      }\n    };\n  }\n}\nexport {\n  s as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,qLAAqL,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AACrZ,IAAM,IAAI;AACV,IAAM,IAAN,MAAM,GAAE;AAAA,EACN,YAAY,EAAE,KAAK,EAAE,GAAG;AACtB,SAAK,MAAM,QAAQ,KAAK,MAAM,GAAG,KAAK,SAAS,MAAM,KAAK,cAAc;AAAA,MACtE,MAAM,KAAK,IAAI,OAAO;AAAA,MACtB,QAAQ,KAAK,IAAI,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,WAAW;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,SAAS,SAAS,cAAc,QAAQ,GAAG,KAAK,OAAO,OAAO,UAAU,KAAK,OAAO,UAAU,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,OAAO,YAAY,KAAK,aAAa,KAAK;AAAA,EACvL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG;AACV,QAAI;AACJ,QAAI,CAAC;AACH;AACF,QAAI,IAAI,KAAK,IAAI,UAAU,cAAc,KAAK,KAAK,GAAE,GAAG;AACxD,QAAI,KAAK,OAAO,CAAC,KAAK,IAAI,EAAE,wBAAwB,kBAAkB,QAAQ,EAAE,cAAc,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC;AAAA,EACxH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,GAAG;AACN,QAAI,IAAI,SAAS,cAAc,KAAK,GAAG;AACvC,MAAE,UAAU,IAAI,GAAE,GAAG,GAAG,EAAE,YAAY,EAAE,gBAAgB,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,KAAK,IAAI,UAAU,YAAY,CAAC;AAAA,EAC/G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG;AACR,QAAI;AACJ,SAAK,IAAI,UAAU,YAAY,CAAC;AAChC,UAAM,IAAI,OAAO,aAAa;AAC9B,QAAI,CAAC;AACH;AACF,UAAM,IAAI,EAAE,WAAW,CAAC,GAAG,IAAI,EAAE,gBAAgB;AACjD,KAAC,IAAI,EAAE,eAAe,QAAQ,EAAE,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,EAAE,gBAAgB,GAAG,EAAE,SAAS,CAAC;AAAA,EACpG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,UAAM,IAAI,KAAK,IAAI,UAAU,cAAc,KAAK,KAAK,GAAE,GAAG;AAC1D,WAAO,KAAK,UAAU,KAAK,OAAO,UAAU,OAAO,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,OAAO,GAAE;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;", "names": []}