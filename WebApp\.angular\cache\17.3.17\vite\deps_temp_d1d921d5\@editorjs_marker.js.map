{"version": 3, "sources": ["../../../../../node_modules/@editorjs/marker/dist/marker.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(\".cdx-marker{background:rgba(245,235,111,.29);padding:3px 0}\")),document.head.appendChild(e)}}catch(d){console.error(\"vite-plugin-css-injected-by-js\",d)}})();\nconst o = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M11.3536 9.31802L12.7678 7.90381C13.5488 7.12276 14.8151 7.12276 15.5962 7.90381C16.3772 8.68486 16.3772 9.95119 15.5962 10.7322L14.182 12.1464M11.3536 9.31802L7.96729 12.7043C7.40889 13.2627 7.02827 13.9739 6.8734 14.7482L6.69798 15.6253C6.55804 16.325 7.17496 16.942 7.87468 16.802L8.75176 16.6266C9.52612 16.4717 10.2373 16.0911 10.7957 15.5327L14.182 12.1464M11.3536 9.31802L14.182 12.1464\"/><line x1=\"15\" x2=\"19\" y1=\"17\" y2=\"17\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/></svg>';\nclass s {\n  /**\n   * Class name for term-tag\n   *\n   * @type {string}\n   */\n  static get CSS() {\n    return \"cdx-marker\";\n  }\n  /**\n   * @param {{api: object}}  - Editor.js API\n   */\n  constructor({ api: t }) {\n    this.api = t, this.button = null, this.tag = \"MARK\", this.iconClasses = {\n      base: this.api.styles.inlineToolButton,\n      active: this.api.styles.inlineToolButtonActive\n    };\n  }\n  /**\n   * Specifies Tool as Inline Toolbar Tool\n   *\n   * @return {boolean}\n   */\n  static get isInline() {\n    return !0;\n  }\n  /**\n   * Create button element for Toolbar\n   *\n   * @return {HTMLElement}\n   */\n  render() {\n    return this.button = document.createElement(\"button\"), this.button.type = \"button\", this.button.classList.add(this.iconClasses.base), this.button.innerHTML = this.toolboxIcon, this.button;\n  }\n  /**\n   * Wrap/Unwrap selected fragment\n   *\n   * @param {Range} range - selected fragment\n   */\n  surround(t) {\n    if (!t)\n      return;\n    let e = this.api.selection.findParentTag(this.tag, s.CSS);\n    e ? this.unwrap(e) : this.wrap(t);\n  }\n  /**\n   * Wrap selection with term-tag\n   *\n   * @param {Range} range - selected fragment\n   */\n  wrap(t) {\n    let e = document.createElement(this.tag);\n    e.classList.add(s.CSS), e.appendChild(t.extractContents()), t.insertNode(e), this.api.selection.expandToTag(e);\n  }\n  /**\n   * Unwrap term-tag\n   *\n   * @param {HTMLElement} termWrapper - term wrapper tag\n   */\n  unwrap(t) {\n    this.api.selection.expandToTag(t);\n    let e = window.getSelection(), n = e.getRangeAt(0), i = n.extractContents();\n    t.parentNode.removeChild(t), n.insertNode(i), e.removeAllRanges(), e.addRange(n);\n  }\n  /**\n   * Check and change Term's state for current selection\n   */\n  checkState() {\n    const t = this.api.selection.findParentTag(this.tag, s.CSS);\n    this.button.classList.toggle(this.iconClasses.active, !!t);\n  }\n  /**\n   * Get Tool icon's SVG\n   * @return {string}\n   */\n  get toolboxIcon() {\n    return o;\n  }\n  /**\n   * Sanitizer rule\n   * @return {{mark: {class: string}}}\n   */\n  static get sanitize() {\n    return {\n      mark: {\n        class: s.CSS\n      }\n    };\n  }\n}\nexport {\n  s as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,6DAA6D,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AAC7R,IAAM,IAAI;AACV,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMN,WAAW,MAAM;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,EAAE,KAAK,EAAE,GAAG;AACtB,SAAK,MAAM,GAAG,KAAK,SAAS,MAAM,KAAK,MAAM,QAAQ,KAAK,cAAc;AAAA,MACtE,MAAM,KAAK,IAAI,OAAO;AAAA,MACtB,QAAQ,KAAK,IAAI,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,WAAW;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,SAAS,SAAS,cAAc,QAAQ,GAAG,KAAK,OAAO,OAAO,UAAU,KAAK,OAAO,UAAU,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,OAAO,YAAY,KAAK,aAAa,KAAK;AAAA,EACvL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG;AACV,QAAI,CAAC;AACH;AACF,QAAI,IAAI,KAAK,IAAI,UAAU,cAAc,KAAK,KAAK,GAAE,GAAG;AACxD,QAAI,KAAK,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,GAAG;AACN,QAAI,IAAI,SAAS,cAAc,KAAK,GAAG;AACvC,MAAE,UAAU,IAAI,GAAE,GAAG,GAAG,EAAE,YAAY,EAAE,gBAAgB,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,KAAK,IAAI,UAAU,YAAY,CAAC;AAAA,EAC/G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG;AACR,SAAK,IAAI,UAAU,YAAY,CAAC;AAChC,QAAI,IAAI,OAAO,aAAa,GAAG,IAAI,EAAE,WAAW,CAAC,GAAG,IAAI,EAAE,gBAAgB;AAC1E,MAAE,WAAW,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,EAAE,gBAAgB,GAAG,EAAE,SAAS,CAAC;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,UAAM,IAAI,KAAK,IAAI,UAAU,cAAc,KAAK,KAAK,GAAE,GAAG;AAC1D,SAAK,OAAO,UAAU,OAAO,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,OAAO,GAAE;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;", "names": []}