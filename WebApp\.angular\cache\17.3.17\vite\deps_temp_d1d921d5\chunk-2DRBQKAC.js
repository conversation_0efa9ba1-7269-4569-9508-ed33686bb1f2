import {
  ANIMATION_MODULE_TYPE,
  Directive,
  Input,
  InputFlags,
  NgModule,
  booleanAttribute,
  inject,
  setClassMetadata,
  ɵɵInputTransformsFeature,
  ɵɵclassProp,
  ɵɵdefineDirective,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-7VXZRWVL.js";

// node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-no-animation.mjs
var NzNoAnimationDirective = class _NzNoAnimationDirective {
  constructor() {
    this.animationType = inject(ANIMATION_MODULE_TYPE, {
      optional: true
    });
    this.nzNoAnimation = false;
  }
  static {
    this.ɵfac = function NzNoAnimationDirective_Factory(t) {
      return new (t || _NzNoAnimationDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _NzNoAnimationDirective,
      selectors: [["", "nzNoAnimation", ""]],
      hostVars: 2,
      hostBindings: function NzNoAnimationDirective_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("nz-animate-disabled", ctx.nzNoAnimation || ctx.animationType === "NoopAnimations");
        }
      },
      inputs: {
        nzNoAnimation: [InputFlags.HasDecoratorInputTransform, "nzNoAnimation", "nzNoAnimation", booleanAttribute]
      },
      exportAs: ["nzNoAnimation"],
      standalone: true,
      features: [ɵɵInputTransformsFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzNoAnimationDirective, [{
    type: Directive,
    args: [{
      selector: "[nzNoAnimation]",
      exportAs: "nzNoAnimation",
      standalone: true,
      host: {
        "[class.nz-animate-disabled]": `nzNoAnimation || animationType === 'NoopAnimations'`
      }
    }]
  }], null, {
    nzNoAnimation: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }]
  });
})();
var NzNoAnimationModule = class _NzNoAnimationModule {
  static {
    this.ɵfac = function NzNoAnimationModule_Factory(t) {
      return new (t || _NzNoAnimationModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _NzNoAnimationModule,
      imports: [NzNoAnimationDirective],
      exports: [NzNoAnimationDirective]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NzNoAnimationModule, [{
    type: NgModule,
    args: [{
      imports: [NzNoAnimationDirective],
      exports: [NzNoAnimationDirective]
    }]
  }], null, null);
})();

export {
  NzNoAnimationDirective
};
//# sourceMappingURL=chunk-2DRBQKAC.js.map
