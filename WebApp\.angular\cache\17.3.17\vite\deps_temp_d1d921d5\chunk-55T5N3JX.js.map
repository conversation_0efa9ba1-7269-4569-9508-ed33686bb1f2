{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-spin.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Optional, Input, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, ReplaySubject, timer } from 'rxjs';\nimport { startWith, distinctUntilChanged, switchMap, debounce, takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { InputNumber, InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nfunction NzSpinComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵelement(1, \"i\", 4)(2, \"i\", 4)(3, \"i\", 4)(4, \"i\", 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzSpinComponent_div_2_ng_template_2_Template(rf, ctx) {}\nfunction NzSpinComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTip);\n  }\n}\nfunction NzSpinComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 5);\n    i0.ɵɵtemplate(2, NzSpinComponent_div_2_ng_template_2_Template, 0, 0, \"ng-template\", 6)(3, NzSpinComponent_div_2_div_3_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const defaultTemplate_r2 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"ant-spin-rtl\", ctx_r0.dir === \"rtl\")(\"ant-spin-spinning\", ctx_r0.isLoading)(\"ant-spin-lg\", ctx_r0.nzSize === \"large\")(\"ant-spin-sm\", ctx_r0.nzSize === \"small\")(\"ant-spin-show-text\", ctx_r0.nzTip);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzIndicator || defaultTemplate_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nzTip);\n  }\n}\nfunction NzSpinComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-spin-blur\", ctx_r0.isLoading);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME = 'spin';\nclass NzSpinComponent {\n  constructor(nzConfigService, cdr, directionality) {\n    this.nzConfigService = nzConfigService;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzIndicator = null;\n    this.nzSize = 'default';\n    this.nzTip = null;\n    this.nzDelay = 0;\n    this.nzSimple = false;\n    this.nzSpinning = true;\n    this.destroy$ = new Subject();\n    this.spinning$ = new BehaviorSubject(this.nzSpinning);\n    this.delay$ = new ReplaySubject(1);\n    this.isLoading = false;\n    this.dir = 'ltr';\n  }\n  ngOnInit() {\n    const loading$ = this.delay$.pipe(startWith(this.nzDelay), distinctUntilChanged(), switchMap(delay => {\n      if (delay === 0) {\n        return this.spinning$;\n      }\n      return this.spinning$.pipe(debounce(spinning => timer(spinning ? delay : 0)));\n    }), takeUntil(this.destroy$));\n    loading$.subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.markForCheck();\n    });\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSpinning,\n      nzDelay\n    } = changes;\n    if (nzSpinning) {\n      this.spinning$.next(this.nzSpinning);\n    }\n    if (nzDelay) {\n      this.delay$.next(this.nzDelay);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSpinComponent_Factory(t) {\n      return new (t || NzSpinComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSpinComponent,\n      selectors: [[\"nz-spin\"]],\n      hostVars: 2,\n      hostBindings: function NzSpinComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-spin-nested-loading\", !ctx.nzSimple);\n        }\n      },\n      inputs: {\n        nzIndicator: \"nzIndicator\",\n        nzSize: \"nzSize\",\n        nzTip: \"nzTip\",\n        nzDelay: \"nzDelay\",\n        nzSimple: \"nzSimple\",\n        nzSpinning: \"nzSpinning\"\n      },\n      exportAs: [\"nzSpin\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 2,\n      consts: [[\"defaultTemplate\", \"\"], [4, \"ngIf\"], [\"class\", \"ant-spin-container\", 3, \"ant-spin-blur\", 4, \"ngIf\"], [1, \"ant-spin-dot\", \"ant-spin-dot-spin\"], [1, \"ant-spin-dot-item\"], [1, \"ant-spin\"], [3, \"ngTemplateOutlet\"], [\"class\", \"ant-spin-text\", 4, \"ngIf\"], [1, \"ant-spin-text\"], [1, \"ant-spin-container\"]],\n      template: function NzSpinComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzSpinComponent_ng_template_0_Template, 5, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzSpinComponent_div_2_Template, 4, 12, \"div\", 1)(3, NzSpinComponent_div_3_Template, 2, 2, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.nzSimple);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n}\n__decorate([WithConfig()], NzSpinComponent.prototype, \"nzIndicator\", void 0);\n__decorate([InputNumber()], NzSpinComponent.prototype, \"nzDelay\", void 0);\n__decorate([InputBoolean()], NzSpinComponent.prototype, \"nzSimple\", void 0);\n__decorate([InputBoolean()], NzSpinComponent.prototype, \"nzSpinning\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpinComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-spin',\n      exportAs: 'nzSpin',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-template #defaultTemplate>\n      <span class=\"ant-spin-dot ant-spin-dot-spin\">\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n      </span>\n    </ng-template>\n    <div *ngIf=\"isLoading\">\n      <div\n        class=\"ant-spin\"\n        [class.ant-spin-rtl]=\"dir === 'rtl'\"\n        [class.ant-spin-spinning]=\"isLoading\"\n        [class.ant-spin-lg]=\"nzSize === 'large'\"\n        [class.ant-spin-sm]=\"nzSize === 'small'\"\n        [class.ant-spin-show-text]=\"nzTip\"\n      >\n        <ng-template [ngTemplateOutlet]=\"nzIndicator || defaultTemplate\"></ng-template>\n        <div class=\"ant-spin-text\" *ngIf=\"nzTip\">{{ nzTip }}</div>\n      </div>\n    </div>\n    <div *ngIf=\"!nzSimple\" class=\"ant-spin-container\" [class.ant-spin-blur]=\"isLoading\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        '[class.ant-spin-nested-loading]': '!nzSimple'\n      },\n      imports: [NgIf, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzIndicator: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzTip: [{\n      type: Input\n    }],\n    nzDelay: [{\n      type: Input\n    }],\n    nzSimple: [{\n      type: Input\n    }],\n    nzSpinning: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSpinModule {\n  static {\n    this.ɵfac = function NzSpinModule_Factory(t) {\n      return new (t || NzSpinModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSpinModule,\n      imports: [NzSpinComponent],\n      exports: [NzSpinComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpinModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSpinComponent],\n      exports: [NzSpinComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSpinComponent, NzSpinModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,UAAU,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC;AACvD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAAC;AAChE,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,IAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,sCAAsC,GAAG,GAAG,OAAO,CAAC;AAC9I,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,UAAU;AACb,IAAG,YAAY,gBAAgB,OAAO,QAAQ,KAAK,EAAE,qBAAqB,OAAO,SAAS,EAAE,eAAe,OAAO,WAAW,OAAO,EAAE,eAAe,OAAO,WAAW,OAAO,EAAE,sBAAsB,OAAO,KAAK;AAClN,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,eAAe,kBAAkB;AAC1E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK;AAAA,EACpC;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,iBAAiB,OAAO,SAAS;AAAA,EAClD;AACF;AACA,IAAM,wBAAwB;AAC9B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,iBAAiB,KAAK,gBAAgB;AAChD,SAAK,kBAAkB;AACvB,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,YAAY,IAAI,gBAAgB,KAAK,UAAU;AACpD,SAAK,SAAS,IAAI,cAAc,CAAC;AACjC,SAAK,YAAY;AACjB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AACT,UAAM,WAAW,KAAK,OAAO,KAAK,UAAU,KAAK,OAAO,GAAG,qBAAqB,GAAG,UAAU,WAAS;AACpG,UAAI,UAAU,GAAG;AACf,eAAO,KAAK;AAAA,MACd;AACA,aAAO,KAAK,UAAU,KAAK,SAAS,cAAY,MAAM,WAAW,QAAQ,CAAC,CAAC,CAAC;AAAA,IAC9E,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAC5B,aAAS,UAAU,aAAW;AAC5B,WAAK,YAAY;AACjB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,gBAAgB,iCAAiC,qBAAqB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AACnJ,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY;AACd,WAAK,UAAU,KAAK,KAAK,UAAU;AAAA,IACrC;AACA,QAAI,SAAS;AACX,WAAK,OAAO,KAAK,KAAK,OAAO;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,kBAAqB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACpK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,MACvB,UAAU;AAAA,MACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,2BAA2B,CAAC,IAAI,QAAQ;AAAA,QACzD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,QAAQ;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,sBAAsB,GAAG,iBAAiB,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,mBAAmB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,iBAAiB,GAAG,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,MACnT,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,gCAAgC,GAAG,IAAI,OAAO,CAAC,EAAE,GAAG,gCAAgC,GAAG,GAAG,OAAO,CAAC;AAAA,QACzN;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,SAAS;AACnC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAC,IAAI,QAAQ;AAAA,QACrC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,gBAAgB;AAAA,MACrC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,CAAC,GAAG,gBAAgB,WAAW,eAAe,MAAM;AAC3E,WAAW,CAAC,YAAY,CAAC,GAAG,gBAAgB,WAAW,WAAW,MAAM;AACxE,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,YAAY,MAAM;AAC1E,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,cAAc,MAAM;AAAA,CAC3E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0BV,MAAM;AAAA,QACJ,mCAAmC;AAAA,MACrC;AAAA,MACA,SAAS,CAAC,MAAM,gBAAgB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,eAAe;AAAA,MACzB,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe;AAAA,MACzB,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}