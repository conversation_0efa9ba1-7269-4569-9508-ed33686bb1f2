{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-progress.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { NgClass, NgTemplateOutlet, NgStyle } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { isNotNil, InputNumber } from 'ng-zorro-antd/core/util';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"nzType\", ctx_r0.icon);\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formatter_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", formatter_r2(ctx_r0.nzPercent), \" \");\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_ng_container_0_Template, 2, 1, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.formatter)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.nzPercent));\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtemplate(1, NzProgressComponent_ng_template_0_Conditional_0_Conditional_1_Template, 1, 1, \"span\", 4)(2, NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_Template, 1, 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, (ctx_r0.status === \"exception\" || ctx_r0.status === \"success\") && !ctx_r0.nzFormat ? 1 : 2);\n  }\n}\nfunction NzProgressComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Conditional_0_Template, 3, 1, \"span\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r0.nzShowInfo ? 0 : -1);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_1_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const step_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngStyle\", step_r3);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_1_ng_template_3_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵrepeaterCreate(1, NzProgressComponent_Conditional_3_Conditional_1_For_2_Template, 1, 1, \"div\", 7, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Conditional_1_ng_template_3_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.steps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzSuccessPercent, \"%\")(\"border-radius\", ctx_r0.nzStrokeLinecap === \"round\" ? \"100px\" : \"0\")(\"height\", ctx_r0.strokeWidth, \"px\");\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_2_ng_template_4_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_3_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n    i0.ɵɵelement(2, \"div\", 11);\n    i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Conditional_2_Conditional_3_Template, 1, 6, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, NzProgressComponent_Conditional_3_Conditional_2_ng_template_4_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzPercent, \"%\")(\"border-radius\", ctx_r0.nzStrokeLinecap === \"round\" ? \"100px\" : \"0\")(\"background\", !ctx_r0.isGradient ? ctx_r0.nzStrokeColor : null)(\"background-image\", ctx_r0.isGradient ? ctx_r0.lineGradient : null)(\"height\", ctx_r0.strokeWidth, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, ctx_r0.nzSuccessPercent || ctx_r0.nzSuccessPercent === 0 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NzProgressComponent_Conditional_3_Conditional_1_Template, 4, 1, \"div\", 6)(2, NzProgressComponent_Conditional_3_Conditional_2_Template, 5, 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.isSteps ? 1 : 2);\n  }\n}\nfunction NzProgressComponent_Conditional_4_Conditional_2_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"stop\");\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.$implicit;\n    i0.ɵɵattribute(\"offset\", i_r5.offset)(\"stop-color\", i_r5.color);\n  }\n}\nfunction NzProgressComponent_Conditional_4_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"defs\")(1, \"linearGradient\", 17);\n    i0.ɵɵrepeaterCreate(2, NzProgressComponent_Conditional_4_Conditional_2_For_3_Template, 1, 2, \":svg:stop\", null, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"gradient-\" + ctx_r0.gradientId);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.circleGradient);\n  }\n}\nfunction NzProgressComponent_Conditional_4_For_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 16);\n  }\n  if (rf & 2) {\n    const p_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", p_r6.strokePathStyle);\n    i0.ɵɵattribute(\"d\", ctx_r0.pathString)(\"stroke-linecap\", ctx_r0.nzStrokeLinecap)(\"stroke\", p_r6.stroke)(\"stroke-width\", ctx_r0.nzPercent ? ctx_r0.strokeWidth : 0);\n  }\n}\nfunction NzProgressComponent_Conditional_4_ng_template_6_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 14);\n    i0.ɵɵtemplate(2, NzProgressComponent_Conditional_4_Conditional_2_Template, 4, 1, \":svg:defs\");\n    i0.ɵɵelement(3, \"path\", 15);\n    i0.ɵɵrepeaterCreate(4, NzProgressComponent_Conditional_4_For_5_Template, 1, 5, \":svg:path\", 16, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, NzProgressComponent_Conditional_4_ng_template_6_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzWidth, \"px\")(\"height\", ctx_r0.nzWidth, \"px\")(\"font-size\", ctx_r0.nzWidth * 0.15 + 6, \"px\");\n    i0.ɵɵclassProp(\"ant-progress-circle-gradient\", ctx_r0.isGradient);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, ctx_r0.isGradient ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.trailPathStyle);\n    i0.ɵɵattribute(\"stroke-width\", ctx_r0.strokeWidth)(\"d\", ctx_r0.pathString);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.progressCirclePath);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction stripPercentToNumber(percent) {\n  return +percent.replace('%', '');\n}\nconst sortGradient = gradients => {\n  let tempArr = [];\n  Object.keys(gradients).forEach(key => {\n    const value = gradients[key];\n    const formatKey = stripPercentToNumber(key);\n    if (!isNaN(formatKey)) {\n      tempArr.push({\n        key: formatKey,\n        value\n      });\n    }\n  });\n  tempArr = tempArr.sort((a, b) => a.key - b.key);\n  return tempArr;\n};\nconst handleCircleGradient = strokeColor => sortGradient(strokeColor).map(({\n  key,\n  value\n}) => ({\n  offset: `${key}%`,\n  color: value\n}));\nconst handleLinearGradient = strokeColor => {\n  const {\n    from = '#1890ff',\n    to = '#1890ff',\n    direction = 'to right',\n    ...rest\n  } = strokeColor;\n  if (Object.keys(rest).length !== 0) {\n    const sortedGradients = sortGradient(rest).map(({\n      key,\n      value\n    }) => `${value} ${key}%`).join(', ');\n    return `linear-gradient(${direction}, ${sortedGradients})`;\n  }\n  return `linear-gradient(${direction}, ${from}, ${to})`;\n};\nlet gradientIdSeed = 0;\nconst NZ_CONFIG_MODULE_NAME = 'progress';\nconst statusIconNameMap = new Map([['success', 'check'], ['exception', 'close']]);\nconst statusColorMap = new Map([['normal', '#108ee9'], ['exception', '#ff5500'], ['success', '#87d068']]);\nconst defaultFormatter = p => `${p}%`;\nclass NzProgressComponent {\n  get formatter() {\n    return this.nzFormat || defaultFormatter;\n  }\n  get status() {\n    return this.nzStatus || this.inferredStatus;\n  }\n  get strokeWidth() {\n    return this.nzStrokeWidth || (this.nzType === 'line' && this.nzSize !== 'small' ? 8 : 6);\n  }\n  get isCircleStyle() {\n    return this.nzType === 'circle' || this.nzType === 'dashboard';\n  }\n  constructor(cdr, nzConfigService, directionality) {\n    this.cdr = cdr;\n    this.nzConfigService = nzConfigService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzShowInfo = true;\n    this.nzWidth = 132;\n    this.nzStrokeColor = undefined;\n    this.nzSize = 'default';\n    this.nzPercent = 0;\n    this.nzStrokeWidth = undefined;\n    this.nzGapDegree = undefined;\n    this.nzType = 'line';\n    this.nzGapPosition = 'top';\n    this.nzStrokeLinecap = 'round';\n    this.nzSteps = 0;\n    this.steps = [];\n    /** Gradient style when `nzType` is `line`. */\n    this.lineGradient = null;\n    /** If user uses gradient color. */\n    this.isGradient = false;\n    /** If the linear progress is a step progress. */\n    this.isSteps = false;\n    /**\n     * Each progress whose `nzType` is circle or dashboard should have unique id to\n     * define `<linearGradient>`.\n     */\n    this.gradientId = gradientIdSeed++;\n    /** Paths to rendered in the template. */\n    this.progressCirclePath = [];\n    this.trailPathStyle = null;\n    this.dir = 'ltr';\n    this.cachedStatus = 'normal';\n    this.inferredStatus = 'normal';\n    this.destroy$ = new Subject();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSteps,\n      nzGapPosition,\n      nzStrokeLinecap,\n      nzStrokeColor,\n      nzGapDegree,\n      nzType,\n      nzStatus,\n      nzPercent,\n      nzSuccessPercent,\n      nzStrokeWidth\n    } = changes;\n    if (nzStatus) {\n      this.cachedStatus = this.nzStatus || this.cachedStatus;\n    }\n    if (nzPercent || nzSuccessPercent) {\n      const fillAll = parseInt(this.nzPercent.toString(), 10) >= 100;\n      if (fillAll) {\n        if (isNotNil(this.nzSuccessPercent) && this.nzSuccessPercent >= 100 || this.nzSuccessPercent === undefined) {\n          this.inferredStatus = 'success';\n        }\n      } else {\n        this.inferredStatus = this.cachedStatus;\n      }\n    }\n    if (nzStatus || nzPercent || nzSuccessPercent || nzStrokeColor) {\n      this.updateIcon();\n    }\n    if (nzStrokeColor) {\n      this.setStrokeColor();\n    }\n    if (nzGapPosition || nzStrokeLinecap || nzGapDegree || nzType || nzPercent || nzStrokeColor || nzStrokeColor) {\n      this.getCirclePaths();\n    }\n    if (nzPercent || nzSteps || nzStrokeWidth) {\n      this.isSteps = this.nzSteps > 0;\n      if (this.isSteps) {\n        this.getSteps();\n      }\n    }\n  }\n  ngOnInit() {\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateIcon();\n      this.setStrokeColor();\n      this.getCirclePaths();\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  updateIcon() {\n    const ret = statusIconNameMap.get(this.status);\n    this.icon = ret ? ret + (this.isCircleStyle ? '-o' : '-circle-fill') : '';\n  }\n  /**\n   * Calculate step render configs.\n   */\n  getSteps() {\n    const current = Math.floor(this.nzSteps * (this.nzPercent / 100));\n    const stepWidth = this.nzSize === 'small' ? 2 : 14;\n    const steps = [];\n    for (let i = 0; i < this.nzSteps; i++) {\n      let color;\n      if (i <= current - 1) {\n        color = this.nzStrokeColor;\n      }\n      const stepStyle = {\n        backgroundColor: `${color}`,\n        width: `${stepWidth}px`,\n        height: `${this.strokeWidth}px`\n      };\n      steps.push(stepStyle);\n    }\n    this.steps = steps;\n  }\n  /**\n   * Calculate paths when the type is circle or dashboard.\n   */\n  getCirclePaths() {\n    if (!this.isCircleStyle) {\n      return;\n    }\n    const values = isNotNil(this.nzSuccessPercent) ? [this.nzSuccessPercent, this.nzPercent] : [this.nzPercent];\n    // Calculate shared styles.\n    const radius = 50 - this.strokeWidth / 2;\n    const gapPosition = this.nzGapPosition || (this.nzType === 'circle' ? 'top' : 'bottom');\n    const len = Math.PI * 2 * radius;\n    const gapDegree = this.nzGapDegree || (this.nzType === 'circle' ? 0 : 75);\n    let beginPositionX = 0;\n    let beginPositionY = -radius;\n    let endPositionX = 0;\n    let endPositionY = radius * -2;\n    switch (gapPosition) {\n      case 'left':\n        beginPositionX = -radius;\n        beginPositionY = 0;\n        endPositionX = radius * 2;\n        endPositionY = 0;\n        break;\n      case 'right':\n        beginPositionX = radius;\n        beginPositionY = 0;\n        endPositionX = radius * -2;\n        endPositionY = 0;\n        break;\n      case 'bottom':\n        beginPositionY = radius;\n        endPositionY = radius * 2;\n        break;\n      default:\n    }\n    this.pathString = `M 50,50 m ${beginPositionX},${beginPositionY}\n       a ${radius},${radius} 0 1 1 ${endPositionX},${-endPositionY}\n       a ${radius},${radius} 0 1 1 ${-endPositionX},${endPositionY}`;\n    this.trailPathStyle = {\n      strokeDasharray: `${len - gapDegree}px ${len}px`,\n      strokeDashoffset: `-${gapDegree / 2}px`,\n      transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s'\n    };\n    // Calculate styles for each path.\n    this.progressCirclePath = values.map((value, index) => {\n      const isSuccessPercent = values.length === 2 && index === 0;\n      return {\n        stroke: this.isGradient && !isSuccessPercent ? `url(#gradient-${this.gradientId})` : null,\n        strokePathStyle: {\n          stroke: !this.isGradient ? isSuccessPercent ? statusColorMap.get('success') : this.nzStrokeColor : null,\n          transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s',\n          strokeDasharray: `${(value || 0) / 100 * (len - gapDegree)}px ${len}px`,\n          strokeDashoffset: `-${gapDegree / 2}px`\n        }\n      };\n    }).reverse();\n  }\n  setStrokeColor() {\n    const color = this.nzStrokeColor;\n    const isGradient = this.isGradient = !!color && typeof color !== 'string';\n    if (isGradient && !this.isCircleStyle) {\n      this.lineGradient = handleLinearGradient(color);\n    } else if (isGradient && this.isCircleStyle) {\n      this.circleGradient = handleCircleGradient(this.nzStrokeColor);\n    } else {\n      this.lineGradient = null;\n      this.circleGradient = [];\n    }\n  }\n  static {\n    this.ɵfac = function NzProgressComponent_Factory(t) {\n      return new (t || NzProgressComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzProgressComponent,\n      selectors: [[\"nz-progress\"]],\n      inputs: {\n        nzShowInfo: \"nzShowInfo\",\n        nzWidth: \"nzWidth\",\n        nzStrokeColor: \"nzStrokeColor\",\n        nzSize: \"nzSize\",\n        nzFormat: \"nzFormat\",\n        nzSuccessPercent: \"nzSuccessPercent\",\n        nzPercent: \"nzPercent\",\n        nzStrokeWidth: \"nzStrokeWidth\",\n        nzGapDegree: \"nzGapDegree\",\n        nzStatus: \"nzStatus\",\n        nzType: \"nzType\",\n        nzGapPosition: \"nzGapPosition\",\n        nzStrokeLinecap: \"nzStrokeLinecap\",\n        nzSteps: \"nzSteps\"\n      },\n      exportAs: [\"nzProgress\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 17,\n      consts: [[\"progressInfoTemplate\", \"\"], [3, \"ngClass\"], [1, \"ant-progress-inner\", 3, \"width\", \"height\", \"fontSize\", \"ant-progress-circle-gradient\"], [1, \"ant-progress-text\"], [\"nz-icon\", \"\", 3, \"nzType\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [1, \"ant-progress-steps-outer\"], [1, \"ant-progress-steps-item\", 3, \"ngStyle\"], [3, \"ngTemplateOutlet\"], [1, \"ant-progress-outer\"], [1, \"ant-progress-inner\"], [1, \"ant-progress-bg\"], [1, \"ant-progress-success-bg\", 3, \"width\", \"border-radius\", \"height\"], [1, \"ant-progress-success-bg\"], [\"viewBox\", \"0 0 100 100\", 1, \"ant-progress-circle\"], [\"stroke\", \"#f3f3f3\", \"fill-opacity\", \"0\", 1, \"ant-progress-circle-trail\", 3, \"ngStyle\"], [\"fill-opacity\", \"0\", 1, \"ant-progress-circle-path\", 3, \"ngStyle\"], [\"x1\", \"100%\", \"y1\", \"0%\", \"x2\", \"0%\", \"y2\", \"0%\", 3, \"id\"]],\n      template: function NzProgressComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Template, 3, 1, \"div\")(4, NzProgressComponent_Conditional_4_Template, 7, 13, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"ant-progress-line\", ctx.nzType === \"line\")(\"ant-progress-small\", ctx.nzSize === \"small\")(\"ant-progress-default\", ctx.nzSize === \"default\")(\"ant-progress-show-info\", ctx.nzShowInfo)(\"ant-progress-circle\", ctx.isCircleStyle)(\"ant-progress-steps\", ctx.isSteps)(\"ant-progress-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵproperty(\"ngClass\", \"ant-progress ant-progress-status-\" + ctx.status);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx.nzType === \"line\" ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.isCircleStyle ? 4 : -1);\n        }\n      },\n      dependencies: [NzIconModule, i3.NzIconDirective, NzOutletModule, i4.NzStringTemplateOutletDirective, NgClass, NgTemplateOutlet, NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzShowInfo\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzStrokeColor\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzSize\", void 0);\n__decorate([InputNumber()], NzProgressComponent.prototype, \"nzSuccessPercent\", void 0);\n__decorate([InputNumber()], NzProgressComponent.prototype, \"nzPercent\", void 0);\n__decorate([WithConfig(), InputNumber()], NzProgressComponent.prototype, \"nzStrokeWidth\", void 0);\n__decorate([WithConfig(), InputNumber()], NzProgressComponent.prototype, \"nzGapDegree\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzGapPosition\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzStrokeLinecap\", void 0);\n__decorate([InputNumber()], NzProgressComponent.prototype, \"nzSteps\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzProgressComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-progress',\n      exportAs: 'nzProgress',\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [NzIconModule, NzOutletModule, NgClass, NgTemplateOutlet, NgStyle],\n      template: `\n    <ng-template #progressInfoTemplate>\n      @if (nzShowInfo) {\n        <span class=\"ant-progress-text\">\n          @if ((status === 'exception' || status === 'success') && !nzFormat) {\n            <span nz-icon [nzType]=\"icon\"></span>\n          } @else {\n            <ng-container *nzStringTemplateOutlet=\"formatter; context: { $implicit: nzPercent }; let formatter\">\n              {{ formatter(nzPercent) }}\n            </ng-container>\n          }\n        </span>\n      }\n    </ng-template>\n\n    <div\n      [ngClass]=\"'ant-progress ant-progress-status-' + status\"\n      [class.ant-progress-line]=\"nzType === 'line'\"\n      [class.ant-progress-small]=\"nzSize === 'small'\"\n      [class.ant-progress-default]=\"nzSize === 'default'\"\n      [class.ant-progress-show-info]=\"nzShowInfo\"\n      [class.ant-progress-circle]=\"isCircleStyle\"\n      [class.ant-progress-steps]=\"isSteps\"\n      [class.ant-progress-rtl]=\"dir === 'rtl'\"\n    >\n      @if (nzType === 'line') {\n        <div>\n          <!-- normal line style -->\n          @if (isSteps) {\n            <div class=\"ant-progress-steps-outer\">\n              @for (step of steps; track step) {\n                <div class=\"ant-progress-steps-item\" [ngStyle]=\"step\"></div>\n              }\n              <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n            </div>\n          } @else {\n            <div class=\"ant-progress-outer\">\n              <div class=\"ant-progress-inner\">\n                <div\n                  class=\"ant-progress-bg\"\n                  [style.width.%]=\"nzPercent\"\n                  [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                  [style.background]=\"!isGradient ? nzStrokeColor : null\"\n                  [style.background-image]=\"isGradient ? lineGradient : null\"\n                  [style.height.px]=\"strokeWidth\"\n                ></div>\n                @if (nzSuccessPercent || nzSuccessPercent === 0) {\n                  <div\n                    class=\"ant-progress-success-bg\"\n                    [style.width.%]=\"nzSuccessPercent\"\n                    [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                    [style.height.px]=\"strokeWidth\"\n                  ></div>\n                }\n              </div>\n            </div>\n            <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n          }\n        </div>\n      }\n      <!-- line progress -->\n\n      <!-- circle / dashboard progress -->\n\n      @if (isCircleStyle) {\n        <div\n          [style.width.px]=\"this.nzWidth\"\n          [style.height.px]=\"this.nzWidth\"\n          [style.fontSize.px]=\"this.nzWidth * 0.15 + 6\"\n          class=\"ant-progress-inner\"\n          [class.ant-progress-circle-gradient]=\"isGradient\"\n        >\n          <svg class=\"ant-progress-circle \" viewBox=\"0 0 100 100\">\n            @if (isGradient) {\n              <defs>\n                <linearGradient [id]=\"'gradient-' + gradientId\" x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\">\n                  @for (i of circleGradient; track i) {\n                    <stop [attr.offset]=\"i.offset\" [attr.stop-color]=\"i.color\"></stop>\n                  }\n                </linearGradient>\n              </defs>\n            }\n\n            <path\n              class=\"ant-progress-circle-trail\"\n              stroke=\"#f3f3f3\"\n              fill-opacity=\"0\"\n              [attr.stroke-width]=\"strokeWidth\"\n              [attr.d]=\"pathString\"\n              [ngStyle]=\"trailPathStyle\"\n            ></path>\n            @for (p of progressCirclePath; track p) {\n              <path\n                class=\"ant-progress-circle-path\"\n                fill-opacity=\"0\"\n                [attr.d]=\"pathString\"\n                [attr.stroke-linecap]=\"nzStrokeLinecap\"\n                [attr.stroke]=\"p.stroke\"\n                [attr.stroke-width]=\"nzPercent ? strokeWidth : 0\"\n                [ngStyle]=\"p.strokePathStyle\"\n              ></path>\n            }\n          </svg>\n          <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n        </div>\n      }\n    </div>\n  `\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzConfigService\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzShowInfo: [{\n      type: Input\n    }],\n    nzWidth: [{\n      type: Input\n    }],\n    nzStrokeColor: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzFormat: [{\n      type: Input\n    }],\n    nzSuccessPercent: [{\n      type: Input\n    }],\n    nzPercent: [{\n      type: Input\n    }],\n    nzStrokeWidth: [{\n      type: Input\n    }],\n    nzGapDegree: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzGapPosition: [{\n      type: Input\n    }],\n    nzStrokeLinecap: [{\n      type: Input\n    }],\n    nzSteps: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzProgressModule {\n  static {\n    this.ɵfac = function NzProgressModule_Factory(t) {\n      return new (t || NzProgressModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzProgressModule,\n      imports: [NzProgressComponent],\n      exports: [NzProgressComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzProgressComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzProgressModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzProgressComponent],\n      exports: [NzProgressComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzProgressComponent, NzProgressModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,IAAI;AAAA,EACrC;AACF;AACA,SAAS,sFAAsF,IAAI,KAAK;AACtG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,aAAa,OAAO,SAAS,GAAG,GAAG;AAAA,EAChE;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uFAAuF,GAAG,GAAG,gBAAgB,CAAC;AAAA,EACjI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,0BAA0B,OAAO,SAAS,EAAE,iCAAoC,gBAAgB,GAAG,KAAK,OAAO,SAAS,CAAC;AAAA,EACzI;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,wEAAwE,GAAG,CAAC;AACzL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,cAAc,IAAI,OAAO,WAAW,eAAe,OAAO,WAAW,cAAc,CAAC,OAAO,WAAW,IAAI,CAAC;AAAA,EAChH;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC5F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,GAAG,OAAO,aAAa,IAAI,EAAE;AAAA,EAChD;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,WAAW,OAAO;AAAA,EAClC;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AAAC;AAC1F,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,iBAAiB,GAAG,gEAAgE,GAAG,GAAG,OAAO,GAAM,yBAAyB;AACnI,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,eAAe,CAAC;AAC/G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,0BAA6B,YAAY,CAAC;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,KAAK;AAC1B,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,uBAAuB;AAAA,EAC3D;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,kBAAkB,GAAG,EAAE,iBAAiB,OAAO,oBAAoB,UAAU,UAAU,GAAG,EAAE,UAAU,OAAO,aAAa,IAAI;AAAA,EAC/J;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AAAC;AAC1F,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,EAAE;AAC3C,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,OAAO,EAAE;AACxG,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,eAAe,CAAC;AAAA,EACjH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,0BAA6B,YAAY,CAAC;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,SAAS,OAAO,WAAW,GAAG,EAAE,iBAAiB,OAAO,oBAAoB,UAAU,UAAU,GAAG,EAAE,cAAc,CAAC,OAAO,aAAa,OAAO,gBAAgB,IAAI,EAAE,oBAAoB,OAAO,aAAa,OAAO,eAAe,IAAI,EAAE,UAAU,OAAO,aAAa,IAAI;AAC1R,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,oBAAoB,OAAO,qBAAqB,IAAI,IAAI,EAAE;AACrF,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,uBAAuB;AAAA,EAC3D;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,0DAA0D,GAAG,EAAE;AAC7J,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,UAAU,IAAI,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,UAAU,GAAG,MAAM;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,IAAG,YAAY,UAAU,KAAK,MAAM,EAAE,cAAc,KAAK,KAAK;AAAA,EAChE;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,MAAM,EAAE,GAAG,kBAAkB,EAAE;AACpD,IAAG,iBAAiB,GAAG,gEAAgE,GAAG,GAAG,aAAa,MAAS,yBAAyB;AAC5I,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,MAAM,cAAc,OAAO,UAAU;AACnD,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,cAAc;AAAA,EACrC;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,KAAK,eAAe;AAC7C,IAAG,YAAY,KAAK,OAAO,UAAU,EAAE,kBAAkB,OAAO,eAAe,EAAE,UAAU,KAAK,MAAM,EAAE,gBAAgB,OAAO,YAAY,OAAO,cAAc,CAAC;AAAA,EACnK;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,WAAW;AAC5F,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,iBAAiB,GAAG,kDAAkD,GAAG,GAAG,aAAa,IAAO,yBAAyB;AAC5H,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,0BAA6B,YAAY,CAAC;AAChD,IAAG,YAAY,SAAS,OAAO,SAAS,IAAI,EAAE,UAAU,OAAO,SAAS,IAAI,EAAE,aAAa,OAAO,UAAU,OAAO,GAAG,IAAI;AAC1H,IAAG,YAAY,gCAAgC,OAAO,UAAU;AAChE,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,GAAG,OAAO,aAAa,IAAI,EAAE;AAC9C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,cAAc;AAC9C,IAAG,YAAY,gBAAgB,OAAO,WAAW,EAAE,KAAK,OAAO,UAAU;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,kBAAkB;AACvC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,uBAAuB;AAAA,EAC3D;AACF;AACA,SAAS,qBAAqB,SAAS;AACrC,SAAO,CAAC,QAAQ,QAAQ,KAAK,EAAE;AACjC;AACA,IAAM,eAAe,eAAa;AAChC,MAAI,UAAU,CAAC;AACf,SAAO,KAAK,SAAS,EAAE,QAAQ,SAAO;AACpC,UAAM,QAAQ,UAAU,GAAG;AAC3B,UAAM,YAAY,qBAAqB,GAAG;AAC1C,QAAI,CAAC,MAAM,SAAS,GAAG;AACrB,cAAQ,KAAK;AAAA,QACX,KAAK;AAAA,QACL;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,YAAU,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG;AAC9C,SAAO;AACT;AACA,IAAM,uBAAuB,iBAAe,aAAa,WAAW,EAAE,IAAI,CAAC;AAAA,EACzE;AAAA,EACA;AACF,OAAO;AAAA,EACL,QAAQ,GAAG,GAAG;AAAA,EACd,OAAO;AACT,EAAE;AACF,IAAM,uBAAuB,iBAAe;AAC1C,QAKI,kBAJF;AAAA,WAAO;AAAA,IACP,KAAK;AAAA,IACL,YAAY;AAAA,EA5OhB,IA8OM,IADC,iBACD,IADC;AAAA,IAHH;AAAA,IACA;AAAA,IACA;AAAA;AAGF,MAAI,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AAClC,UAAM,kBAAkB,aAAa,IAAI,EAAE,IAAI,CAAC;AAAA,MAC9C;AAAA,MACA;AAAA,IACF,MAAM,GAAG,KAAK,IAAI,GAAG,GAAG,EAAE,KAAK,IAAI;AACnC,WAAO,mBAAmB,SAAS,KAAK,eAAe;AAAA,EACzD;AACA,SAAO,mBAAmB,SAAS,KAAK,IAAI,KAAK,EAAE;AACrD;AACA,IAAI,iBAAiB;AACrB,IAAM,wBAAwB;AAC9B,IAAM,oBAAoB,oBAAI,IAAI,CAAC,CAAC,WAAW,OAAO,GAAG,CAAC,aAAa,OAAO,CAAC,CAAC;AAChF,IAAM,iBAAiB,oBAAI,IAAI,CAAC,CAAC,UAAU,SAAS,GAAG,CAAC,aAAa,SAAS,GAAG,CAAC,WAAW,SAAS,CAAC,CAAC;AACxG,IAAM,mBAAmB,OAAK,GAAG,CAAC;AAClC,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,IAAI,YAAY;AACd,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,kBAAkB,KAAK,WAAW,UAAU,KAAK,WAAW,UAAU,IAAI;AAAA,EACxF;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,WAAW,YAAY,KAAK,WAAW;AAAA,EACrD;AAAA,EACA,YAAY,KAAK,iBAAiB,gBAAgB;AAChD,SAAK,MAAM;AACX,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,QAAQ,CAAC;AAEd,SAAK,eAAe;AAEpB,SAAK,aAAa;AAElB,SAAK,UAAU;AAKf,SAAK,aAAa;AAElB,SAAK,qBAAqB,CAAC;AAC3B,SAAK,iBAAiB;AACtB,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU;AACZ,WAAK,eAAe,KAAK,YAAY,KAAK;AAAA,IAC5C;AACA,QAAI,aAAa,kBAAkB;AACjC,YAAM,UAAU,SAAS,KAAK,UAAU,SAAS,GAAG,EAAE,KAAK;AAC3D,UAAI,SAAS;AACX,YAAI,SAAS,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,OAAO,KAAK,qBAAqB,QAAW;AAC1G,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF,OAAO;AACL,aAAK,iBAAiB,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,YAAY,aAAa,oBAAoB,eAAe;AAC9D,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,eAAe;AACjB,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,iBAAiB,mBAAmB,eAAe,UAAU,aAAa,iBAAiB,eAAe;AAC5G,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,aAAa,WAAW,eAAe;AACzC,WAAK,UAAU,KAAK,UAAU;AAC9B,UAAI,KAAK,SAAS;AAChB,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,iCAAiC,qBAAqB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1H,WAAK,WAAW;AAChB,WAAK,eAAe;AACpB,WAAK,eAAe;AAAA,IACtB,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,aAAa;AACX,UAAM,MAAM,kBAAkB,IAAI,KAAK,MAAM;AAC7C,SAAK,OAAO,MAAM,OAAO,KAAK,gBAAgB,OAAO,kBAAkB;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,UAAM,UAAU,KAAK,MAAM,KAAK,WAAW,KAAK,YAAY,IAAI;AAChE,UAAM,YAAY,KAAK,WAAW,UAAU,IAAI;AAChD,UAAM,QAAQ,CAAC;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,KAAK;AACrC,UAAI;AACJ,UAAI,KAAK,UAAU,GAAG;AACpB,gBAAQ,KAAK;AAAA,MACf;AACA,YAAM,YAAY;AAAA,QAChB,iBAAiB,GAAG,KAAK;AAAA,QACzB,OAAO,GAAG,SAAS;AAAA,QACnB,QAAQ,GAAG,KAAK,WAAW;AAAA,MAC7B;AACA,YAAM,KAAK,SAAS;AAAA,IACtB;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,CAAC,KAAK,eAAe;AACvB;AAAA,IACF;AACA,UAAM,SAAS,SAAS,KAAK,gBAAgB,IAAI,CAAC,KAAK,kBAAkB,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS;AAE1G,UAAM,SAAS,KAAK,KAAK,cAAc;AACvC,UAAM,cAAc,KAAK,kBAAkB,KAAK,WAAW,WAAW,QAAQ;AAC9E,UAAM,MAAM,KAAK,KAAK,IAAI;AAC1B,UAAM,YAAY,KAAK,gBAAgB,KAAK,WAAW,WAAW,IAAI;AACtE,QAAI,iBAAiB;AACrB,QAAI,iBAAiB,CAAC;AACtB,QAAI,eAAe;AACnB,QAAI,eAAe,SAAS;AAC5B,YAAQ,aAAa;AAAA,MACnB,KAAK;AACH,yBAAiB,CAAC;AAClB,yBAAiB;AACjB,uBAAe,SAAS;AACxB,uBAAe;AACf;AAAA,MACF,KAAK;AACH,yBAAiB;AACjB,yBAAiB;AACjB,uBAAe,SAAS;AACxB,uBAAe;AACf;AAAA,MACF,KAAK;AACH,yBAAiB;AACjB,uBAAe,SAAS;AACxB;AAAA,MACF;AAAA,IACF;AACA,SAAK,aAAa,aAAa,cAAc,IAAI,cAAc;AAAA,WACxD,MAAM,IAAI,MAAM,UAAU,YAAY,IAAI,CAAC,YAAY;AAAA,WACvD,MAAM,IAAI,MAAM,UAAU,CAAC,YAAY,IAAI,YAAY;AAC9D,SAAK,iBAAiB;AAAA,MACpB,iBAAiB,GAAG,MAAM,SAAS,MAAM,GAAG;AAAA,MAC5C,kBAAkB,IAAI,YAAY,CAAC;AAAA,MACnC,YAAY;AAAA,IACd;AAEA,SAAK,qBAAqB,OAAO,IAAI,CAAC,OAAO,UAAU;AACrD,YAAM,mBAAmB,OAAO,WAAW,KAAK,UAAU;AAC1D,aAAO;AAAA,QACL,QAAQ,KAAK,cAAc,CAAC,mBAAmB,iBAAiB,KAAK,UAAU,MAAM;AAAA,QACrF,iBAAiB;AAAA,UACf,QAAQ,CAAC,KAAK,aAAa,mBAAmB,eAAe,IAAI,SAAS,IAAI,KAAK,gBAAgB;AAAA,UACnG,YAAY;AAAA,UACZ,iBAAiB,IAAI,SAAS,KAAK,OAAO,MAAM,UAAU,MAAM,GAAG;AAAA,UACnE,kBAAkB,IAAI,YAAY,CAAC;AAAA,QACrC;AAAA,MACF;AAAA,IACF,CAAC,EAAE,QAAQ;AAAA,EACb;AAAA,EACA,iBAAiB;AACf,UAAM,QAAQ,KAAK;AACnB,UAAM,aAAa,KAAK,aAAa,CAAC,CAAC,SAAS,OAAO,UAAU;AACjE,QAAI,cAAc,CAAC,KAAK,eAAe;AACrC,WAAK,eAAe,qBAAqB,KAAK;AAAA,IAChD,WAAW,cAAc,KAAK,eAAe;AAC3C,WAAK,iBAAiB,qBAAqB,KAAK,aAAa;AAAA,IAC/D,OAAO;AACL,WAAK,eAAe;AACpB,WAAK,iBAAiB,CAAC;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAqB,iBAAiB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACxK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,MAC3B,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,aAAa;AAAA,QACb,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,sBAAsB,GAAG,SAAS,UAAU,YAAY,8BAA8B,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,WAAW,IAAI,GAAG,QAAQ,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,2BAA2B,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,2BAA2B,GAAG,SAAS,iBAAiB,QAAQ,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,WAAW,eAAe,GAAG,qBAAqB,GAAG,CAAC,UAAU,WAAW,gBAAgB,KAAK,GAAG,6BAA6B,GAAG,SAAS,GAAG,CAAC,gBAAgB,KAAK,GAAG,4BAA4B,GAAG,SAAS,GAAG,CAAC,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC;AAAA,MACxzB,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpH,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,KAAK,EAAE,GAAG,4CAA4C,GAAG,IAAI,OAAO,CAAC;AACxI,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,qBAAqB,IAAI,WAAW,MAAM,EAAE,sBAAsB,IAAI,WAAW,OAAO,EAAE,wBAAwB,IAAI,WAAW,SAAS,EAAE,0BAA0B,IAAI,UAAU,EAAE,uBAAuB,IAAI,aAAa,EAAE,sBAAsB,IAAI,OAAO,EAAE,oBAAoB,IAAI,QAAQ,KAAK;AACvT,UAAG,WAAW,WAAW,sCAAsC,IAAI,MAAM;AACzE,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,WAAW,SAAS,IAAI,EAAE;AAClD,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,gBAAgB,IAAI,EAAE;AAAA,QAChD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAmB,iCAAiC,SAAS,kBAAkB,OAAO;AAAA,MACvI,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,CAAC,GAAG,oBAAoB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,WAAW,CAAC,GAAG,oBAAoB,WAAW,iBAAiB,MAAM;AACjF,WAAW,CAAC,WAAW,CAAC,GAAG,oBAAoB,WAAW,UAAU,MAAM;AAC1E,WAAW,CAAC,YAAY,CAAC,GAAG,oBAAoB,WAAW,oBAAoB,MAAM;AACrF,WAAW,CAAC,YAAY,CAAC,GAAG,oBAAoB,WAAW,aAAa,MAAM;AAC9E,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,oBAAoB,WAAW,iBAAiB,MAAM;AAChG,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,oBAAoB,WAAW,eAAe,MAAM;AAC9F,WAAW,CAAC,WAAW,CAAC,GAAG,oBAAoB,WAAW,iBAAiB,MAAM;AACjF,WAAW,CAAC,WAAW,CAAC,GAAG,oBAAoB,WAAW,mBAAmB,MAAM;AACnF,WAAW,CAAC,YAAY,CAAC,GAAG,oBAAoB,WAAW,WAAW,MAAM;AAAA,CAC3E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,gBAAgB,SAAS,kBAAkB,OAAO;AAAA,MAC1E,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA4GZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB;AAAA,MAC7B,SAAS,CAAC,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB;AAAA,MAC7B,SAAS,CAAC,mBAAmB;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}