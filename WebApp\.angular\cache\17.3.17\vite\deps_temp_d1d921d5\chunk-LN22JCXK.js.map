{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-environments.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-logger.mjs", "../../../../../node_modules/@angular/cdk/fesm2022/coercion.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-util.mjs"], "sourcesContent": ["/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst environment = {\n    isTestMode: false\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { environment };\n\n", "import { isDevMode } from '@angular/core';\nimport { environment } from 'ng-zorro-antd/core/environments';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst record = {};\nconst PREFIX = '[NG-ZORRO]:';\nfunction notRecorded(...args) {\n    const asRecord = args.reduce((acc, c) => acc + c.toString(), '');\n    if (record[asRecord]) {\n        return false;\n    }\n    else {\n        record[asRecord] = true;\n        return true;\n    }\n}\nfunction consoleCommonBehavior(consoleFunc, ...args) {\n    if (environment.isTestMode || (isDevMode() && notRecorded(...args))) {\n        consoleFunc(...args);\n    }\n}\n// Warning should only be printed in dev mode and only once.\nconst warn = (...args) => consoleCommonBehavior((...arg) => console.warn(PREFIX, ...arg), ...args);\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nconst warnDeprecation = (...args) => {\n    if (!environment.isTestMode) {\n        const stack = new Error().stack;\n        return consoleCommonBehavior((...arg) => console.warn(PREFIX, 'deprecated:', ...arg, stack), ...args);\n    }\n    else {\n        return () => { };\n    }\n};\n// Log should only be printed in dev mode.\nconst log = (...args) => {\n    if (isDevMode()) {\n        console.log(PREFIX, ...args);\n    }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PREFIX, log, warn, warnDeprecation };\n\n", "import { ElementRef } from '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n    return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n    // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n    // and other non-number values as NaN, where Number just uses 0) but it considers the string\n    // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n    return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\nfunction coerceArray(value) {\n    return Array.isArray(value) ? value : [value];\n}\n\n/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n    if (value == null) {\n        return '';\n    }\n    return typeof value === 'string' ? value : `${value}px`;\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n    return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n    const result = [];\n    if (value != null) {\n        const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n        for (const sourceValue of sourceValues) {\n            const trimmedString = `${sourceValue}`.trim();\n            if (trimmedString) {\n                result.push(trimmedString);\n            }\n        }\n    }\n    return result;\n}\n\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray };\n\n", "import { TemplateRef } from '@angular/core';\nimport { coerceBooleanProperty, _isNumberValue, coerceCssPixelValue } from '@angular/cdk/coercion';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { Subject, isObservable, from, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toArray(value) {\n    let ret;\n    if (value == null) {\n        ret = [];\n    }\n    else if (!Array.isArray(value)) {\n        ret = [value];\n    }\n    else {\n        ret = value;\n    }\n    return ret;\n}\nfunction arraysEqual(array1, array2) {\n    if (!array1 || !array2 || array1.length !== array2.length) {\n        return false;\n    }\n    const len = array1.length;\n    for (let i = 0; i < len; i++) {\n        if (array1[i] !== array2[i]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction shallowCopyArray(source) {\n    return source.slice();\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isNotNil(value) {\n    return typeof value !== 'undefined' && value !== null;\n}\nfunction isNil(value) {\n    return typeof value === 'undefined' || value === null;\n}\n/**\n * Examine if two objects are shallowly equaled.\n */\nfunction shallowEqual(objA, objB) {\n    if (objA === objB) {\n        return true;\n    }\n    if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n        return false;\n    }\n    const keysA = Object.keys(objA);\n    const keysB = Object.keys(objB);\n    if (keysA.length !== keysB.length) {\n        return false;\n    }\n    const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let idx = 0; idx < keysA.length; idx++) {\n        const key = keysA[idx];\n        if (!bHasOwnProperty(key)) {\n            return false;\n        }\n        if (objA[key] !== objB[key]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isNonEmptyString(value) {\n    return typeof value === 'string' && value !== '';\n}\nfunction isTemplateRef(value) {\n    return value instanceof TemplateRef;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toBoolean(value) {\n    return coerceBooleanProperty(value);\n}\nfunction toNumber(value, fallbackValue = 0) {\n    return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\nfunction toCssPixel(value) {\n    return coerceCssPixelValue(value);\n}\n// eslint-disable  no-invalid-this\n/**\n * Get the function-property type's value\n */\nfunction valueFunctionProp(prop, ...args) {\n    return typeof prop === 'function' ? prop(...args) : prop;\n}\nfunction propDecoratorFactory(name, fallback) {\n    function propDecorator(target, propName, originalDescriptor) {\n        const privatePropName = `$$__zorroPropDecorator__${propName}`;\n        if (Object.prototype.hasOwnProperty.call(target, privatePropName)) {\n            warn(`The prop \"${privatePropName}\" is already exist, it will be overrided by ${name} decorator.`);\n        }\n        Object.defineProperty(target, privatePropName, {\n            configurable: true,\n            writable: true\n        });\n        return {\n            get() {\n                return originalDescriptor && originalDescriptor.get\n                    ? originalDescriptor.get.bind(this)()\n                    : this[privatePropName];\n            },\n            set(value) {\n                if (originalDescriptor && originalDescriptor.set) {\n                    originalDescriptor.set.bind(this)(fallback(value));\n                }\n                this[privatePropName] = fallback(value);\n            }\n        };\n    }\n    return propDecorator;\n}\n/**\n * Input decorator that handle a prop to do get/set automatically with toBoolean\n *\n * Why not using @InputBoolean alone without @Input? AOT needs @Input to be visible\n *\n * @howToUse\n * ```\n * @Input() @InputBoolean() visible: boolean = false;\n *\n * // Act as below:\n * // @Input()\n * // get visible() { return this.__visible; }\n * // set visible(value) { this.__visible = value; }\n * // __visible = false;\n * ```\n */\nfunction InputBoolean() {\n    return propDecoratorFactory('InputBoolean', toBoolean);\n}\nfunction InputCssPixel() {\n    return propDecoratorFactory('InputCssPixel', toCssPixel);\n}\nfunction InputNumber(fallbackValue) {\n    return propDecoratorFactory('InputNumber', (value) => toNumber(value, fallbackValue));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Silent an event by stopping and preventing it.\n */\nfunction silentEvent(e) {\n    e.stopPropagation();\n    e.preventDefault();\n}\nfunction getElementOffset(elem) {\n    if (!elem.getClientRects().length) {\n        return { top: 0, left: 0 };\n    }\n    const rect = elem.getBoundingClientRect();\n    const win = elem.ownerDocument.defaultView;\n    return {\n        top: rect.top + win.pageYOffset,\n        left: rect.left + win.pageXOffset\n    };\n}\n/**\n * Investigate if an event is a `TouchEvent`.\n */\nfunction isTouchEvent(event) {\n    return event.type.startsWith('touch');\n}\nfunction getEventPosition(event) {\n    return isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getRegExp(prefix) {\n    const prefixArray = Array.isArray(prefix) ? prefix : [prefix];\n    let prefixToken = prefixArray.join('').replace(/(\\$|\\^)/g, '\\\\$1');\n    if (prefixArray.length > 1) {\n        prefixToken = `[${prefixToken}]`;\n    }\n    return new RegExp(`(\\\\s|^)(${prefixToken})[^\\\\s]*`, 'g');\n}\nfunction getMentions(value, prefix = '@') {\n    if (typeof value !== 'string') {\n        return [];\n    }\n    const regex = getRegExp(prefix);\n    const mentions = value.match(regex);\n    return mentions !== null ? mentions.map(e => e.trim()) : [];\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Much like lodash.\n */\nfunction padStart(toPad, length, element) {\n    if (toPad.length > length) {\n        return toPad;\n    }\n    const joined = `${getRepeatedElement(length, element)}${toPad}`;\n    return joined.slice(joined.length - length, joined.length);\n}\nfunction padEnd(toPad, length, element) {\n    const joined = `${toPad}${getRepeatedElement(length, element)}`;\n    return joined.slice(0, length);\n}\nfunction getRepeatedElement(length, element) {\n    return Array(length).fill(element).join('');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isPromise(obj) {\n    return !!obj && typeof obj.then === 'function' && typeof obj.catch === 'function';\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getPercent(min, max, value) {\n    return ((value - min) / (max - min)) * 100;\n}\nfunction getPrecision(num) {\n    const numStr = num.toString();\n    const dotIndex = numStr.indexOf('.');\n    return dotIndex >= 0 ? numStr.length - dotIndex - 1 : 0;\n}\nfunction ensureNumberInRange(num, min, max) {\n    if (isNaN(num) || num < min) {\n        return min;\n    }\n    else if (num > max) {\n        return max;\n    }\n    else {\n        return num;\n    }\n}\nfunction isNumberFinite(value) {\n    return typeof value === 'number' && isFinite(value);\n}\nfunction toDecimal(value, decimal) {\n    return Math.round(value * Math.pow(10, decimal)) / Math.pow(10, decimal);\n}\nfunction sum(input, initial = 0) {\n    return input.reduce((previous, current) => previous + current, initial);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction scrollIntoView(node) {\n    const nodeAsAny = node;\n    if (nodeAsAny.scrollIntoViewIfNeeded) {\n        /* eslint-disable-next-line @typescript-eslint/dot-notation */\n        nodeAsAny.scrollIntoViewIfNeeded(false);\n        return;\n    }\n    if (node.scrollIntoView) {\n        node.scrollIntoView(false);\n        return;\n    }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// from https://github.com/component/textarea-caret-position\n// We'll copy the properties below into the mirror div.\n// Note that some browsers, such as Firefox, do not concatenate properties\n// into their shorthand (e.g. padding-top, padding-bottom etc. -> padding),\n// so we have to list every single property explicitly.\nconst properties = [\n    'direction', // RTL support\n    'boxSizing',\n    'width', // on Chrome and IE, exclude the scrollbar, so the mirror div wraps exactly as the textarea does\n    'height',\n    'overflowX',\n    'overflowY', // copy the scrollbar for IE\n    'borderTopWidth',\n    'borderRightWidth',\n    'borderBottomWidth',\n    'borderLeftWidth',\n    'borderStyle',\n    'paddingTop',\n    'paddingRight',\n    'paddingBottom',\n    'paddingLeft',\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/font\n    'fontStyle',\n    'fontVariant',\n    'fontWeight',\n    'fontStretch',\n    'fontSize',\n    'fontSizeAdjust',\n    'lineHeight',\n    'fontFamily',\n    'textAlign',\n    'textTransform',\n    'textIndent',\n    'textDecoration', // might not make a difference, but better be safe\n    'letterSpacing',\n    'wordSpacing',\n    'tabSize',\n    'MozTabSize'\n];\nconst isBrowser = typeof window !== 'undefined';\nconst isFirefox = isBrowser && window.mozInnerScreenX != null;\nconst _parseInt = (str) => parseInt(str, 10);\nfunction getCaretCoordinates(element, position, options) {\n    if (!isBrowser) {\n        throw new Error('textarea-caret-position#getCaretCoordinates should only be called in a browser');\n    }\n    const debug = (options && options.debug) || false;\n    if (debug) {\n        const el = document.querySelector('#input-textarea-caret-position-mirror-div');\n        if (el) {\n            el.parentNode.removeChild(el);\n        }\n    }\n    // The mirror div will replicate the textarea's style\n    const div = document.createElement('div');\n    div.id = 'input-textarea-caret-position-mirror-div';\n    document.body.appendChild(div);\n    const style = div.style;\n    const computed = window.getComputedStyle ? window.getComputedStyle(element) : element.currentStyle; // currentStyle for IE < 9\n    const isInput = element.nodeName === 'INPUT';\n    // Default textarea styles\n    style.whiteSpace = 'pre-wrap';\n    if (!isInput) {\n        style.wordWrap = 'break-word'; // only for textarea-s\n    }\n    // Position off-screen\n    style.position = 'absolute'; // required to return coordinates properly\n    if (!debug) {\n        style.visibility = 'hidden';\n    } // not 'display: none' because we want rendering\n    // Transfer the element's properties to the div\n    properties.forEach((prop) => {\n        if (isInput && prop === 'lineHeight') {\n            // Special case for <input>s because text is rendered centered and line height may be != height\n            style.lineHeight = computed.height;\n        }\n        else {\n            // @ts-ignore\n            style[prop] = computed[prop];\n        }\n    });\n    if (isFirefox) {\n        // Firefox lies about the overflow property for textareas: https://bugzilla.mozilla.org/show_bug.cgi?id=984275\n        if (element.scrollHeight > _parseInt(computed.height)) {\n            style.overflowY = 'scroll';\n        }\n    }\n    else {\n        style.overflow = 'hidden'; // for Chrome to not render a scrollbar; IE keeps overflowY = 'scroll'\n    }\n    div.textContent = element.value.substring(0, position);\n    // The second special handling for input type=\"text\" vs textarea:\n    // spaces need to be replaced with non-breaking spaces - http://stackoverflow.com/a/13402035/1269037\n    if (isInput) {\n        div.textContent = div.textContent.replace(/\\s/g, '\\u00a0');\n    }\n    const span = document.createElement('span');\n    // Wrapping must be replicated *exactly*, including when a long word gets\n    // onto the next line, with whitespace at the end of the line before (#7).\n    // The  *only* reliable way to do that is to copy the *entire* rest of the\n    // textarea's content into the <span> created at the caret position.\n    // For inputs, just '.' would be enough, but no need to bother.\n    span.textContent = element.value.substring(position) || '.'; // || because a completely empty faux span doesn't render at all\n    div.appendChild(span);\n    const coordinates = {\n        top: span.offsetTop + _parseInt(computed.borderTopWidth),\n        left: span.offsetLeft + _parseInt(computed.borderLeftWidth),\n        height: _parseInt(computed.lineHeight)\n    };\n    if (debug) {\n        span.style.backgroundColor = '#eee';\n        createDebugEle(element, coordinates);\n    }\n    else {\n        document.body.removeChild(div);\n    }\n    return coordinates;\n}\nfunction createDebugEle(element, coordinates) {\n    const fontSize = getComputedStyle(element).getPropertyValue('font-size');\n    const rect = document.querySelector('#DEBUG') || document.createElement('div');\n    document.body.appendChild(rect);\n    rect.id = 'DEBUG';\n    rect.style.position = 'absolute';\n    rect.style.backgroundColor = 'red';\n    rect.style.height = fontSize;\n    rect.style.width = '1px';\n    rect.style.top = `${element.getBoundingClientRect().top - element.scrollTop + window.pageYOffset + coordinates.top}px`;\n    rect.style.left = `${element.getBoundingClientRect().left - element.scrollLeft + window.pageXOffset + coordinates.left}px`;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isStyleSupport(styleName) {\n    if (typeof window !== 'undefined' && window.document && window.document.documentElement) {\n        const styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n        const { documentElement } = window.document;\n        return styleNameList.some(name => name in documentElement.style);\n    }\n    return false;\n}\nfunction getStyleAsText(styles) {\n    if (!styles) {\n        return '';\n    }\n    return Object.keys(styles)\n        .map(key => {\n        const val = styles[key];\n        return `${key}:${typeof val === 'string' ? val : `${val}px`}`;\n    })\n        .join(';');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// We only handle element & text node.\nconst ELEMENT_NODE = 1;\nconst TEXT_NODE = 3;\nconst COMMENT_NODE = 8;\nlet ellipsisContainer;\nconst wrapperStyle = {\n    padding: '0',\n    margin: '0',\n    display: 'inline',\n    lineHeight: 'inherit'\n};\nfunction pxToNumber(value) {\n    if (!value) {\n        return 0;\n    }\n    const match = value.match(/^\\d*(\\.\\d*)?/);\n    return match ? Number(match[0]) : 0;\n}\nfunction styleToString(style) {\n    // There are some different behavior between Firefox & Chrome.\n    // We have to handle this ourself.\n    const styleNames = Array.prototype.slice.apply(style);\n    return styleNames.map(name => `${name}: ${style.getPropertyValue(name)};`).join('');\n}\nfunction mergeChildren(children) {\n    const childList = [];\n    children.forEach((child) => {\n        const prevChild = childList[childList.length - 1];\n        if (prevChild && child.nodeType === TEXT_NODE && prevChild.nodeType === TEXT_NODE) {\n            prevChild.data += child.data;\n        }\n        else {\n            childList.push(child);\n        }\n    });\n    return childList;\n}\nfunction measure(originEle, rows, contentNodes, fixedContent, ellipsisStr, suffixStr = '') {\n    if (!ellipsisContainer) {\n        ellipsisContainer = document.createElement('div');\n        ellipsisContainer.setAttribute('aria-hidden', 'true');\n        document.body.appendChild(ellipsisContainer);\n    }\n    // Get origin style\n    const originStyle = window.getComputedStyle(originEle);\n    const originCSS = styleToString(originStyle);\n    const lineHeight = pxToNumber(originStyle.lineHeight);\n    const maxHeight = Math.round(lineHeight * (rows + 1) + pxToNumber(originStyle.paddingTop) + pxToNumber(originStyle.paddingBottom));\n    // Set shadow\n    ellipsisContainer.setAttribute('style', originCSS);\n    ellipsisContainer.style.position = 'fixed';\n    ellipsisContainer.style.left = '0';\n    ellipsisContainer.style.height = 'auto';\n    ellipsisContainer.style.minHeight = 'auto';\n    ellipsisContainer.style.maxHeight = 'auto';\n    ellipsisContainer.style.top = '-999999px';\n    ellipsisContainer.style.zIndex = '-1000';\n    // clean up css overflow\n    ellipsisContainer.style.textOverflow = 'clip';\n    ellipsisContainer.style.whiteSpace = 'normal';\n    ellipsisContainer.style.webkitLineClamp = 'none';\n    const contentList = mergeChildren(contentNodes);\n    const container = document.createElement('div');\n    const contentContainer = document.createElement('span');\n    const suffixContainer = document.createTextNode(suffixStr);\n    const fixedContainer = document.createElement('span');\n    // Add styles in container\n    Object.assign(container.style, wrapperStyle);\n    Object.assign(contentContainer.style, wrapperStyle);\n    Object.assign(fixedContainer.style, wrapperStyle);\n    contentList.forEach(n => {\n        contentContainer.appendChild(n);\n    });\n    contentContainer.appendChild(suffixContainer);\n    fixedContent.forEach(node => {\n        fixedContainer.appendChild(node.cloneNode(true));\n    });\n    container.appendChild(contentContainer);\n    container.appendChild(fixedContainer);\n    // Render in the fake container\n    ellipsisContainer.appendChild(container);\n    // Check if ellipsis in measure div is height enough for content\n    function inRange() {\n        return ellipsisContainer.offsetHeight < maxHeight;\n    }\n    if (inRange()) {\n        const text = ellipsisContainer.innerHTML;\n        ellipsisContainer.removeChild(container);\n        return { contentNodes, text, ellipsis: false };\n    }\n    // We should clone the childNode since they're controlled by React and we can't reuse it without warning\n    const childNodes = Array.prototype.slice\n        .apply(ellipsisContainer.childNodes[0].childNodes[0].cloneNode(true).childNodes)\n        .filter(({ nodeType }) => nodeType !== COMMENT_NODE);\n    const fixedNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[1].cloneNode(true).childNodes);\n    ellipsisContainer.removeChild(container);\n    // ========================= Find match ellipsis content =========================\n    ellipsisContainer.innerHTML = '';\n    // Create origin content holder\n    const ellipsisContentHolder = document.createElement('span');\n    ellipsisContainer.appendChild(ellipsisContentHolder);\n    const ellipsisTextNode = document.createTextNode(ellipsisStr + suffixStr);\n    ellipsisContentHolder.appendChild(ellipsisTextNode);\n    fixedNodes.forEach(childNode => {\n        ellipsisContainer.appendChild(childNode);\n    });\n    // Append before fixed nodes\n    function appendChildNode(node) {\n        ellipsisContentHolder.insertBefore(node, ellipsisTextNode);\n    }\n    // Get maximum text\n    function measureText(textNode, fullText, startLoc = 0, endLoc = fullText.length, lastSuccessLoc = 0) {\n        const midLoc = Math.floor((startLoc + endLoc) / 2);\n        textNode.textContent = fullText.slice(0, midLoc);\n        if (startLoc >= endLoc - 1) {\n            // Loop when step is small\n            for (let step = endLoc; step >= startLoc; step -= 1) {\n                const currentStepText = fullText.slice(0, step);\n                textNode.textContent = currentStepText;\n                if (inRange() || !currentStepText) {\n                    return step === fullText.length\n                        ? {\n                            finished: false,\n                            node: document.createTextNode(fullText)\n                        }\n                        : {\n                            finished: true,\n                            node: document.createTextNode(currentStepText)\n                        };\n                }\n            }\n        }\n        if (inRange()) {\n            return measureText(textNode, fullText, midLoc, endLoc, midLoc);\n        }\n        else {\n            return measureText(textNode, fullText, startLoc, midLoc, lastSuccessLoc);\n        }\n    }\n    function measureNode(childNode, index) {\n        const type = childNode.nodeType;\n        if (type === ELEMENT_NODE) {\n            // We don't split element, it will keep if whole element can be displayed.\n            // appendChildNode(childNode);\n            if (inRange()) {\n                return {\n                    finished: false,\n                    node: contentList[index]\n                };\n            }\n            // Clean up if can not pull in\n            ellipsisContentHolder.removeChild(childNode);\n            return {\n                finished: true,\n                node: null\n            };\n        }\n        else if (type === TEXT_NODE) {\n            const fullText = childNode.textContent || '';\n            const textNode = document.createTextNode(fullText);\n            appendChildNode(textNode);\n            return measureText(textNode, fullText);\n        }\n        // Not handle other type of content\n        // PS: This code should not be attached after react 16\n        return {\n            finished: false,\n            node: null\n        };\n    }\n    const ellipsisNodes = [];\n    childNodes.some((childNode, index) => {\n        const { finished, node } = measureNode(childNode, index);\n        if (node) {\n            ellipsisNodes.push(node);\n        }\n        return finished;\n    });\n    const result = {\n        contentNodes: ellipsisNodes,\n        text: ellipsisContainer.innerHTML,\n        ellipsis: true\n    };\n    while (ellipsisContainer.firstChild) {\n        ellipsisContainer.removeChild(ellipsisContainer.firstChild);\n    }\n    return result;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nlet scrollbarVerticalSize;\nlet scrollbarHorizontalSize;\n// Measure scrollbar width for padding body during modal show/hide\nconst scrollbarMeasure = {\n    position: 'absolute',\n    top: '-9999px',\n    width: '50px',\n    height: '50px'\n};\nfunction measureScrollbar(direction = 'vertical', prefix = 'ant') {\n    if (typeof document === 'undefined' || typeof window === 'undefined') {\n        return 0;\n    }\n    const isVertical = direction === 'vertical';\n    if (isVertical && scrollbarVerticalSize) {\n        return scrollbarVerticalSize;\n    }\n    else if (!isVertical && scrollbarHorizontalSize) {\n        return scrollbarHorizontalSize;\n    }\n    const scrollDiv = document.createElement('div');\n    Object.keys(scrollbarMeasure).forEach(scrollProp => {\n        // @ts-ignore\n        scrollDiv.style[scrollProp] = scrollbarMeasure[scrollProp];\n    });\n    // apply hide scrollbar className ahead\n    scrollDiv.className = `${prefix}-hide-scrollbar scroll-div-append-to-body`;\n    // Append related overflow style\n    if (isVertical) {\n        scrollDiv.style.overflowY = 'scroll';\n    }\n    else {\n        scrollDiv.style.overflowX = 'scroll';\n    }\n    document.body.appendChild(scrollDiv);\n    let size = 0;\n    if (isVertical) {\n        size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n        scrollbarVerticalSize = size;\n    }\n    else {\n        size = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n        scrollbarHorizontalSize = size;\n    }\n    document.body.removeChild(scrollDiv);\n    return size;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction ensureInBounds(value, boundValue) {\n    return value ? (value < boundValue ? value : boundValue) : boundValue;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction inNextTick() {\n    const timer = new Subject();\n    Promise.resolve().then(() => timer.next());\n    return timer.pipe(take(1));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction wrapIntoObservable(value) {\n    if (isObservable(value)) {\n        return value;\n    }\n    if (isPromise(value)) {\n        // Use `Promise.resolve()` to wrap promise-like instances.\n        return from(Promise.resolve(value));\n    }\n    return of(value);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nfunction canUseDom() {\n    return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nconst MARK_KEY = `rc-util-key`;\nfunction getMark({ mark } = {}) {\n    if (mark) {\n        return mark.startsWith('data-') ? mark : `data-${mark}`;\n    }\n    return MARK_KEY;\n}\nfunction getContainer(option) {\n    if (option.attachTo) {\n        return option.attachTo;\n    }\n    const head = document.querySelector('head');\n    return head || document.body;\n}\nfunction injectCSS(css, options = {}) {\n    if (!canUseDom()) {\n        return null;\n    }\n    const styleNode = document.createElement('style');\n    if (options.cspNonce) {\n        styleNode.nonce = options.cspNonce;\n    }\n    styleNode.innerHTML = css;\n    const container = getContainer(options);\n    const { firstChild } = container;\n    if (options.prepend && container.prepend) {\n        // Use `prepend` first\n        container.prepend(styleNode);\n    }\n    else if (options.prepend && firstChild) {\n        // Fallback to `insertBefore` like IE not support `prepend`\n        container.insertBefore(styleNode, firstChild);\n    }\n    else {\n        container.appendChild(styleNode);\n    }\n    return styleNode;\n}\nconst containerCache = new Map();\nfunction findExistNode(key, option = {}) {\n    const container = getContainer(option);\n    return Array.from(containerCache.get(container)?.children || []).find(node => node.tagName === 'STYLE' && node.getAttribute(getMark(option)) === key);\n}\nfunction removeCSS(key, option = {}) {\n    const existNode = findExistNode(key, option);\n    existNode?.parentNode?.removeChild(existNode);\n}\nfunction updateCSS(css, key, options = {}) {\n    const container = getContainer(options);\n    // Get real parent\n    if (!containerCache.has(container)) {\n        const placeholderStyle = injectCSS('', options);\n        // @ts-ignore\n        const { parentNode } = placeholderStyle;\n        containerCache.set(container, parentNode);\n        parentNode.removeChild(placeholderStyle);\n    }\n    const existNode = findExistNode(key, options);\n    if (existNode) {\n        if (options.cspNonce && existNode.nonce !== options.cspNonce) {\n            existNode.nonce = options.cspNonce;\n        }\n        if (existNode.innerHTML !== css) {\n            existNode.innerHTML = css;\n        }\n        return existNode;\n    }\n    const newNode = injectCSS(css, options);\n    newNode?.setAttribute(getMark(options), key);\n    return newNode;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getStatusClassNames(prefixCls, status, hasFeedback) {\n    return {\n        [`${prefixCls}-status-success`]: status === 'success',\n        [`${prefixCls}-status-warning`]: status === 'warning',\n        [`${prefixCls}-status-error`]: status === 'error',\n        [`${prefixCls}-status-validating`]: status === 'validating',\n        [`${prefixCls}-has-feedback`]: hasFeedback\n    };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputBoolean, InputCssPixel, InputNumber, arraysEqual, canUseDom, createDebugEle, ensureInBounds, ensureNumberInRange, getCaretCoordinates, getElementOffset, getEventPosition, getMentions, getPercent, getPrecision, getRegExp, getRepeatedElement, getStatusClassNames, getStyleAsText, inNextTick, injectCSS, isNil, isNonEmptyString, isNotNil, isNumberFinite, isPromise, isStyleSupport, isTemplateRef, isTouchEvent, measure, measureScrollbar, padEnd, padStart, properties, pxToNumber, removeCSS, scrollIntoView, shallowCopyArray, shallowEqual, silentEvent, sum, toArray, toBoolean, toCssPixel, toDecimal, toNumber, updateCSS, valueFunctionProp, wrapIntoObservable };\n\n"], "mappings": ";;;;;;;;;;;;;;AAIA,IAAM,cAAc;AAAA,EAChB,YAAY;AAChB;;;ACCA,IAAM,SAAS,CAAC;AAChB,IAAM,SAAS;AACf,SAAS,eAAe,MAAM;AAC1B,QAAM,WAAW,KAAK,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,SAAS,GAAG,EAAE;AAC/D,MAAI,OAAO,QAAQ,GAAG;AAClB,WAAO;AAAA,EACX,OACK;AACD,WAAO,QAAQ,IAAI;AACnB,WAAO;AAAA,EACX;AACJ;AACA,SAAS,sBAAsB,gBAAgB,MAAM;AACjD,MAAI,YAAY,cAAe,UAAU,KAAK,YAAY,GAAG,IAAI,GAAI;AACjE,gBAAY,GAAG,IAAI;AAAA,EACvB;AACJ;AAEA,IAAM,OAAO,IAAI,SAAS,sBAAsB,IAAI,QAAQ,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,IAAI;;;ACtBjG,SAAS,sBAAsB,OAAO;AAClC,SAAO,SAAS,QAAQ,GAAG,KAAK,OAAO;AAC3C;AAEA,SAAS,qBAAqB,OAAO,gBAAgB,GAAG;AACpD,SAAO,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI;AACnD;AAKA,SAAS,eAAe,OAAO;AAI3B,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAC5D;AAEA,SAAS,YAAY,OAAO;AACxB,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAChD;AAGA,SAAS,oBAAoB,OAAO;AAChC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,SAAO,OAAO,UAAU,WAAW,QAAQ,GAAG,KAAK;AACvD;AAMA,SAAS,cAAc,cAAc;AACjC,SAAO,wBAAwB,aAAa,aAAa,gBAAgB;AAC7E;;;AChBA,SAAS,YAAY,QAAQ,QAAQ;AACjC,MAAI,CAAC,UAAU,CAAC,UAAU,OAAO,WAAW,OAAO,QAAQ;AACvD,WAAO;AAAA,EACX;AACA,QAAM,MAAM,OAAO;AACnB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,QAAI,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG;AACzB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AASA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,eAAe,UAAU;AACrD;AACA,SAAS,MAAM,OAAO;AAClB,SAAO,OAAO,UAAU,eAAe,UAAU;AACrD;AA6BA,SAAS,iBAAiB,OAAO;AAC7B,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,iBAAiB;AAC5B;AAMA,SAAS,UAAU,OAAO;AACtB,SAAO,sBAAsB,KAAK;AACtC;AACA,SAAS,SAAS,OAAO,gBAAgB,GAAG;AACxC,SAAO,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI;AACnD;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,oBAAoB,KAAK;AACpC;AAKA,SAAS,kBAAkB,SAAS,MAAM;AACtC,SAAO,OAAO,SAAS,aAAa,KAAK,GAAG,IAAI,IAAI;AACxD;AACA,SAAS,qBAAqB,MAAM,UAAU;AAC1C,WAAS,cAAc,QAAQ,UAAU,oBAAoB;AACzD,UAAM,kBAAkB,2BAA2B,QAAQ;AAC3D,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,eAAe,GAAG;AAC/D,WAAK,aAAa,eAAe,+CAA+C,IAAI,aAAa;AAAA,IACrG;AACA,WAAO,eAAe,QAAQ,iBAAiB;AAAA,MAC3C,cAAc;AAAA,MACd,UAAU;AAAA,IACd,CAAC;AACD,WAAO;AAAA,MACH,MAAM;AACF,eAAO,sBAAsB,mBAAmB,MAC1C,mBAAmB,IAAI,KAAK,IAAI,EAAE,IAClC,KAAK,eAAe;AAAA,MAC9B;AAAA,MACA,IAAI,OAAO;AACP,YAAI,sBAAsB,mBAAmB,KAAK;AAC9C,6BAAmB,IAAI,KAAK,IAAI,EAAE,SAAS,KAAK,CAAC;AAAA,QACrD;AACA,aAAK,eAAe,IAAI,SAAS,KAAK;AAAA,MAC1C;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAiBA,SAAS,eAAe;AACpB,SAAO,qBAAqB,gBAAgB,SAAS;AACzD;AAIA,SAAS,YAAY,eAAe;AAChC,SAAO,qBAAqB,eAAe,CAAC,UAAU,SAAS,OAAO,aAAa,CAAC;AACxF;AASA,SAAS,YAAY,GAAG;AACpB,IAAE,gBAAgB;AAClB,IAAE,eAAe;AACrB;AACA,SAAS,iBAAiB,MAAM;AAC5B,MAAI,CAAC,KAAK,eAAe,EAAE,QAAQ;AAC/B,WAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AAAA,EAC7B;AACA,QAAM,OAAO,KAAK,sBAAsB;AACxC,QAAM,MAAM,KAAK,cAAc;AAC/B,SAAO;AAAA,IACH,KAAK,KAAK,MAAM,IAAI;AAAA,IACpB,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACJ;AAIA,SAAS,aAAa,OAAO;AACzB,SAAO,MAAM,KAAK,WAAW,OAAO;AACxC;AACA,SAAS,iBAAiB,OAAO;AAC7B,SAAO,aAAa,KAAK,IAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,eAAe,CAAC,IAAI;AAC/E;AA8BA,SAAS,SAAS,OAAO,QAAQ,SAAS;AACtC,MAAI,MAAM,SAAS,QAAQ;AACvB,WAAO;AAAA,EACX;AACA,QAAM,SAAS,GAAG,mBAAmB,QAAQ,OAAO,CAAC,GAAG,KAAK;AAC7D,SAAO,OAAO,MAAM,OAAO,SAAS,QAAQ,OAAO,MAAM;AAC7D;AAKA,SAAS,mBAAmB,QAAQ,SAAS;AACzC,SAAO,MAAM,MAAM,EAAE,KAAK,OAAO,EAAE,KAAK,EAAE;AAC9C;AAMA,SAAS,UAAU,KAAK;AACpB,SAAO,CAAC,CAAC,OAAO,OAAO,IAAI,SAAS,cAAc,OAAO,IAAI,UAAU;AAC3E;AAMA,SAAS,WAAW,KAAK,KAAK,OAAO;AACjC,UAAS,QAAQ,QAAQ,MAAM,OAAQ;AAC3C;AACA,SAAS,aAAa,KAAK;AACvB,QAAM,SAAS,IAAI,SAAS;AAC5B,QAAM,WAAW,OAAO,QAAQ,GAAG;AACnC,SAAO,YAAY,IAAI,OAAO,SAAS,WAAW,IAAI;AAC1D;AACA,SAAS,oBAAoB,KAAK,KAAK,KAAK;AACxC,MAAI,MAAM,GAAG,KAAK,MAAM,KAAK;AACzB,WAAO;AAAA,EACX,WACS,MAAM,KAAK;AAChB,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK;AACtD;AACA,SAAS,UAAU,OAAO,SAAS;AAC/B,SAAO,KAAK,MAAM,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,IAAI,OAAO;AAC3E;AACA,SAAS,IAAI,OAAO,UAAU,GAAG;AAC7B,SAAO,MAAM,OAAO,CAAC,UAAU,YAAY,WAAW,SAAS,OAAO;AAC1E;AAMA,SAAS,eAAe,MAAM;AAC1B,QAAM,YAAY;AAClB,MAAI,UAAU,wBAAwB;AAElC,cAAU,uBAAuB,KAAK;AACtC;AAAA,EACJ;AACA,MAAI,KAAK,gBAAgB;AACrB,SAAK,eAAe,KAAK;AACzB;AAAA,EACJ;AACJ;AA6CA,IAAM,YAAY,OAAO,WAAW;AACpC,IAAM,YAAY,aAAa,OAAO,mBAAmB;AAwTzD,IAAI;AACJ,IAAI;AAEJ,IAAM,mBAAmB;AAAA,EACrB,UAAU;AAAA,EACV,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AACZ;AACA,SAAS,iBAAiB,YAAY,YAAY,SAAS,OAAO;AAC9D,MAAI,OAAO,aAAa,eAAe,OAAO,WAAW,aAAa;AAClE,WAAO;AAAA,EACX;AACA,QAAM,aAAa,cAAc;AACjC,MAAI,cAAc,uBAAuB;AACrC,WAAO;AAAA,EACX,WACS,CAAC,cAAc,yBAAyB;AAC7C,WAAO;AAAA,EACX;AACA,QAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,SAAO,KAAK,gBAAgB,EAAE,QAAQ,gBAAc;AAEhD,cAAU,MAAM,UAAU,IAAI,iBAAiB,UAAU;AAAA,EAC7D,CAAC;AAED,YAAU,YAAY,GAAG,MAAM;AAE/B,MAAI,YAAY;AACZ,cAAU,MAAM,YAAY;AAAA,EAChC,OACK;AACD,cAAU,MAAM,YAAY;AAAA,EAChC;AACA,WAAS,KAAK,YAAY,SAAS;AACnC,MAAI,OAAO;AACX,MAAI,YAAY;AACZ,WAAO,UAAU,cAAc,UAAU;AACzC,4BAAwB;AAAA,EAC5B,OACK;AACD,WAAO,UAAU,eAAe,UAAU;AAC1C,8BAA0B;AAAA,EAC9B;AACA,WAAS,KAAK,YAAY,SAAS;AACnC,SAAO;AACX;AAwBA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,aAAa,KAAK,GAAG;AACrB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,KAAK,GAAG;AAElB,WAAO,KAAK,QAAQ,QAAQ,KAAK,CAAC;AAAA,EACtC;AACA,SAAO,GAAG,KAAK;AACnB;AASA,SAAS,YAAY;AACjB,SAAO,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AAClF;AASA,IAAM,WAAW;AACjB,SAAS,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG;AAC5B,MAAI,MAAM;AACN,WAAO,KAAK,WAAW,OAAO,IAAI,OAAO,QAAQ,IAAI;AAAA,EACzD;AACA,SAAO;AACX;AACA,SAAS,aAAa,QAAQ;AAC1B,MAAI,OAAO,UAAU;AACjB,WAAO,OAAO;AAAA,EAClB;AACA,QAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,SAAO,QAAQ,SAAS;AAC5B;AACA,SAAS,UAAU,KAAK,UAAU,CAAC,GAAG;AAClC,MAAI,CAAC,UAAU,GAAG;AACd,WAAO;AAAA,EACX;AACA,QAAM,YAAY,SAAS,cAAc,OAAO;AAChD,MAAI,QAAQ,UAAU;AAClB,cAAU,QAAQ,QAAQ;AAAA,EAC9B;AACA,YAAU,YAAY;AACtB,QAAM,YAAY,aAAa,OAAO;AACtC,QAAM,EAAE,WAAW,IAAI;AACvB,MAAI,QAAQ,WAAW,UAAU,SAAS;AAEtC,cAAU,QAAQ,SAAS;AAAA,EAC/B,WACS,QAAQ,WAAW,YAAY;AAEpC,cAAU,aAAa,WAAW,UAAU;AAAA,EAChD,OACK;AACD,cAAU,YAAY,SAAS;AAAA,EACnC;AACA,SAAO;AACX;AACA,IAAM,iBAAiB,oBAAI,IAAI;AAC/B,SAAS,cAAc,KAAK,SAAS,CAAC,GAAG;AACrC,QAAM,YAAY,aAAa,MAAM;AACrC,SAAO,MAAM,KAAK,eAAe,IAAI,SAAS,GAAG,YAAY,CAAC,CAAC,EAAE,KAAK,UAAQ,KAAK,YAAY,WAAW,KAAK,aAAa,QAAQ,MAAM,CAAC,MAAM,GAAG;AACxJ;AAKA,SAAS,UAAU,KAAK,KAAK,UAAU,CAAC,GAAG;AACvC,QAAM,YAAY,aAAa,OAAO;AAEtC,MAAI,CAAC,eAAe,IAAI,SAAS,GAAG;AAChC,UAAM,mBAAmB,UAAU,IAAI,OAAO;AAE9C,UAAM,EAAE,WAAW,IAAI;AACvB,mBAAe,IAAI,WAAW,UAAU;AACxC,eAAW,YAAY,gBAAgB;AAAA,EAC3C;AACA,QAAM,YAAY,cAAc,KAAK,OAAO;AAC5C,MAAI,WAAW;AACX,QAAI,QAAQ,YAAY,UAAU,UAAU,QAAQ,UAAU;AAC1D,gBAAU,QAAQ,QAAQ;AAAA,IAC9B;AACA,QAAI,UAAU,cAAc,KAAK;AAC7B,gBAAU,YAAY;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,QAAM,UAAU,UAAU,KAAK,OAAO;AACtC,WAAS,aAAa,QAAQ,OAAO,GAAG,GAAG;AAC3C,SAAO;AACX;AAMA,SAAS,oBAAoB,WAAW,QAAQ,aAAa;AACzD,SAAO;AAAA,IACH,CAAC,GAAG,SAAS,iBAAiB,GAAG,WAAW;AAAA,IAC5C,CAAC,GAAG,SAAS,iBAAiB,GAAG,WAAW;AAAA,IAC5C,CAAC,GAAG,SAAS,eAAe,GAAG,WAAW;AAAA,IAC1C,CAAC,GAAG,SAAS,oBAAoB,GAAG,WAAW;AAAA,IAC/C,CAAC,GAAG,SAAS,eAAe,GAAG;AAAA,EACnC;AACJ;", "names": []}