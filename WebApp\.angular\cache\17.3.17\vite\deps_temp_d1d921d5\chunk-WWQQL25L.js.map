{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-transition-patch.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * hack the bug\n * angular router change with unexpected transition trigger after calling applicationRef.attachView\n * https://github.com/angular/angular/issues/34718\n */\nclass NzTransitionPatchDirective {\n  setHiddenAttribute() {\n    if (this.hidden) {\n      if (typeof this.hidden === 'string') {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'hidden', this.hidden);\n      } else {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'hidden', '');\n      }\n    } else {\n      this.renderer.removeAttribute(this.elementRef.nativeElement, 'hidden');\n    }\n  }\n  constructor(elementRef, renderer) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.hidden = null;\n    this.renderer.setAttribute(this.elementRef.nativeElement, 'hidden', '');\n  }\n  ngOnChanges() {\n    this.setHiddenAttribute();\n  }\n  ngAfterViewInit() {\n    this.setHiddenAttribute();\n  }\n  static {\n    this.ɵfac = function NzTransitionPatchDirective_Factory(t) {\n      return new (t || NzTransitionPatchDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTransitionPatchDirective,\n      selectors: [[\"\", \"nz-button\", \"\"], [\"nz-button-group\"], [\"\", \"nz-icon\", \"\"], [\"\", \"nz-menu-item\", \"\"], [\"\", \"nz-submenu\", \"\"], [\"nz-select-top-control\"], [\"nz-select-placeholder\"], [\"nz-input-group\"]],\n      inputs: {\n        hidden: \"hidden\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTransitionPatchDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-button], nz-button-group, [nz-icon], [nz-menu-item], [nz-submenu], nz-select-top-control, nz-select-placeholder, nz-input-group',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }], {\n    hidden: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTransitionPatchModule {\n  static {\n    this.ɵfac = function NzTransitionPatchModule_Factory(t) {\n      return new (t || NzTransitionPatchModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzTransitionPatchModule,\n      imports: [NzTransitionPatchDirective],\n      exports: [NzTransitionPatchDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTransitionPatchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTransitionPatchDirective],\n      exports: [NzTransitionPatchDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzTransitionPatchDirective as ɵNzTransitionPatchDirective, NzTransitionPatchModule as ɵNzTransitionPatchModule };\n"], "mappings": ";;;;;;;;;;;;;;;AAYA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,qBAAqB;AACnB,QAAI,KAAK,QAAQ;AACf,UAAI,OAAO,KAAK,WAAW,UAAU;AACnC,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,UAAU,KAAK,MAAM;AAAA,MACjF,OAAO;AACL,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,UAAU,EAAE;AAAA,MACxE;AAAA,IACF,OAAO;AACL,WAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,QAAQ;AAAA,IACvE;AAAA,EACF;AAAA,EACA,YAAY,YAAY,UAAU;AAChC,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,SAAS,aAAa,KAAK,WAAW,eAAe,UAAU,EAAE;AAAA,EACxE;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA+B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,IACtH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,iBAAiB,GAAG,CAAC,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,uBAAuB,GAAG,CAAC,uBAAuB,GAAG,CAAC,gBAAgB,CAAC;AAAA,MACvM,QAAQ;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,0BAA0B;AAAA,MACpC,SAAS,CAAC,0BAA0B;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,0BAA0B;AAAA,MACpC,SAAS,CAAC,0BAA0B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}