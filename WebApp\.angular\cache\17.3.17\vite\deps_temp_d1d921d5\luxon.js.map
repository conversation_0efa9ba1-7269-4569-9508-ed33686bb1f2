{"version": 3, "sources": ["../../../../../node_modules/luxon/src/errors.js", "../../../../../node_modules/luxon/src/impl/formats.js", "../../../../../node_modules/luxon/src/zone.js", "../../../../../node_modules/luxon/src/zones/systemZone.js", "../../../../../node_modules/luxon/src/zones/IANAZone.js", "../../../../../node_modules/luxon/src/impl/locale.js", "../../../../../node_modules/luxon/src/zones/fixedOffsetZone.js", "../../../../../node_modules/luxon/src/zones/invalidZone.js", "../../../../../node_modules/luxon/src/impl/zoneUtil.js", "../../../../../node_modules/luxon/src/impl/digits.js", "../../../../../node_modules/luxon/src/settings.js", "../../../../../node_modules/luxon/src/impl/invalid.js", "../../../../../node_modules/luxon/src/impl/conversions.js", "../../../../../node_modules/luxon/src/impl/util.js", "../../../../../node_modules/luxon/src/impl/english.js", "../../../../../node_modules/luxon/src/impl/formatter.js", "../../../../../node_modules/luxon/src/impl/regexParser.js", "../../../../../node_modules/luxon/src/duration.js", "../../../../../node_modules/luxon/src/interval.js", "../../../../../node_modules/luxon/src/info.js", "../../../../../node_modules/luxon/src/impl/diff.js", "../../../../../node_modules/luxon/src/impl/tokenParser.js", "../../../../../node_modules/luxon/src/datetime.js", "../../../../../node_modules/luxon/src/luxon.js"], "sourcesContent": ["// these aren't really private, but nor are they really useful to document\n\n/**\n * @private\n */\nclass LuxonError extends Error {}\n\n/**\n * @private\n */\nexport class InvalidDateTimeError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid DateTime: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidIntervalError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Interval: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidDurationError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Duration: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class ConflictingSpecificationError extends LuxonError {}\n\n/**\n * @private\n */\nexport class InvalidUnitError extends LuxonError {\n  constructor(unit) {\n    super(`Invalid unit ${unit}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidArgumentError extends LuxonError {}\n\n/**\n * @private\n */\nexport class ZoneIsAbstractError extends LuxonError {\n  constructor() {\n    super(\"Zone is an abstract class\");\n  }\n}\n", "/**\n * @private\n */\n\nconst n = \"numeric\",\n  s = \"short\",\n  l = \"long\";\n\nexport const DATE_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n};\n\nexport const DATE_MED = {\n  year: n,\n  month: s,\n  day: n,\n};\n\nexport const DATE_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n};\n\nexport const DATE_FULL = {\n  year: n,\n  month: l,\n  day: n,\n};\n\nexport const DATE_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n};\n\nexport const TIME_SIMPLE = {\n  hour: n,\n  minute: n,\n};\n\nexport const TIME_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const TIME_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const TIME_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n\nexport const TIME_24_SIMPLE = {\n  hour: n,\n  minute: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: s,\n};\n\nexport const TIME_24_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: l,\n};\n\nexport const DATETIME_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_SHORT_WITH_SECONDS = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_MED_WITH_SECONDS = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_FULL = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_FULL_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  timeZoneName: l,\n};\n\nexport const DATETIME_HUGE_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n", "import { ZoneIsAbstractError } from \"./errors.js\";\n\n/**\n * @interface\n */\nexport default class Zone {\n  /**\n   * The type of zone\n   * @abstract\n   * @type {string}\n   */\n  get type() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The name of this zone.\n   * @abstract\n   * @type {string}\n   */\n  get name() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The IANA name of this zone.\n   * Defaults to `name` if not overwritten by a subclass.\n   * @abstract\n   * @type {string}\n   */\n  get ianaName() {\n    return this.name;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year.\n   * @abstract\n   * @type {boolean}\n   */\n  get isUniversal() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, opts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @abstract\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @abstract\n   * @type {boolean}\n   */\n  get isValid() {\n    throw new ZoneIsAbstractError();\n  }\n}\n", "import { formatOffset, parseZoneInfo } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * Represents the local zone for this JavaScript environment.\n * @implements {Zone}\n */\nexport default class SystemZone extends Zone {\n  /**\n   * Get a singleton instance of the local zone\n   * @return {SystemZone}\n   */\n  static get instance() {\n    if (singleton === null) {\n      singleton = new SystemZone();\n    }\n    return singleton;\n  }\n\n  /** @override **/\n  get type() {\n    return \"system\";\n  }\n\n  /** @override **/\n  get name() {\n    return new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale);\n  }\n\n  /** @override **/\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /** @override **/\n  offset(ts) {\n    return -new Date(ts).getTimezoneOffset();\n  }\n\n  /** @override **/\n  equals(otherZone) {\n    return otherZone.type === \"system\";\n  }\n\n  /** @override **/\n  get isValid() {\n    return true;\n  }\n}\n", "import { formatOffset, parseZoneInfo, isUndefined, objToLocalTS } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nconst dtfCache = new Map();\nfunction makeDTF(zoneName) {\n  let dtf = dtfCache.get(zoneName);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(\"en-US\", {\n      hour12: false,\n      timeZone: zoneName,\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      second: \"2-digit\",\n      era: \"short\",\n    });\n    dtfCache.set(zoneName, dtf);\n  }\n  return dtf;\n}\n\nconst typeToPos = {\n  year: 0,\n  month: 1,\n  day: 2,\n  era: 3,\n  hour: 4,\n  minute: 5,\n  second: 6,\n};\n\nfunction hackyOffset(dtf, date) {\n  const formatted = dtf.format(date).replace(/\\u200E/g, \"\"),\n    parsed = /(\\d+)\\/(\\d+)\\/(\\d+) (AD|BC),? (\\d+):(\\d+):(\\d+)/.exec(formatted),\n    [, fMonth, fDay, fYear, fadOrBc, fHour, fMinute, fSecond] = parsed;\n  return [fYear, fMonth, fDay, fadOrBc, fHour, fMinute, fSecond];\n}\n\nfunction partsOffset(dtf, date) {\n  const formatted = dtf.formatToParts(date);\n  const filled = [];\n  for (let i = 0; i < formatted.length; i++) {\n    const { type, value } = formatted[i];\n    const pos = typeToPos[type];\n\n    if (type === \"era\") {\n      filled[pos] = value;\n    } else if (!isUndefined(pos)) {\n      filled[pos] = parseInt(value, 10);\n    }\n  }\n  return filled;\n}\n\nconst ianaZoneCache = new Map();\n/**\n * A zone identified by an IANA identifier, like America/New_York\n * @implements {Zone}\n */\nexport default class IANAZone extends Zone {\n  /**\n   * @param {string} name - Zone name\n   * @return {IANAZone}\n   */\n  static create(name) {\n    let zone = ianaZoneCache.get(name);\n    if (zone === undefined) {\n      ianaZoneCache.set(name, (zone = new IANAZone(name)));\n    }\n    return zone;\n  }\n\n  /**\n   * Reset local caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCache() {\n    ianaZoneCache.clear();\n    dtfCache.clear();\n  }\n\n  /**\n   * Returns whether the provided string is a valid specifier. This only checks the string's format, not that the specifier identifies a known zone; see isValidZone for that.\n   * @param {string} s - The string to check validity on\n   * @example IANAZone.isValidSpecifier(\"America/New_York\") //=> true\n   * @example IANAZone.isValidSpecifier(\"Sport~~blorp\") //=> false\n   * @deprecated For backward compatibility, this forwards to isValidZone, better use `isValidZone()` directly instead.\n   * @return {boolean}\n   */\n  static isValidSpecifier(s) {\n    return this.isValidZone(s);\n  }\n\n  /**\n   * Returns whether the provided string identifies a real zone\n   * @param {string} zone - The string to check\n   * @example IANAZone.isValidZone(\"America/New_York\") //=> true\n   * @example IANAZone.isValidZone(\"Fantasia/Castle\") //=> false\n   * @example IANAZone.isValidZone(\"Sport~~blorp\") //=> false\n   * @return {boolean}\n   */\n  static isValidZone(zone) {\n    if (!zone) {\n      return false;\n    }\n    try {\n      new Intl.DateTimeFormat(\"en-US\", { timeZone: zone }).format();\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  constructor(name) {\n    super();\n    /** @private **/\n    this.zoneName = name;\n    /** @private **/\n    this.valid = IANAZone.isValidZone(name);\n  }\n\n  /**\n   * The type of zone. `iana` for all instances of `IANAZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"iana\";\n  }\n\n  /**\n   * The name of this zone (i.e. the IANA zone name).\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.zoneName;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns false for all IANA zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return false;\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale, this.name);\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    if (!this.valid) return NaN;\n    const date = new Date(ts);\n\n    if (isNaN(date)) return NaN;\n\n    const dtf = makeDTF(this.name);\n    let [year, month, day, adOrBc, hour, minute, second] = dtf.formatToParts\n      ? partsOffset(dtf, date)\n      : hackyOffset(dtf, date);\n\n    if (adOrBc === \"BC\") {\n      year = -Math.abs(year) + 1;\n    }\n\n    // because we're using hour12 and https://bugs.chromium.org/p/chromium/issues/detail?id=1025564&can=2&q=%2224%3A00%22%20datetimeformat\n    const adjustedHour = hour === 24 ? 0 : hour;\n\n    const asUTC = objToLocalTS({\n      year,\n      month,\n      day,\n      hour: adjustedHour,\n      minute,\n      second,\n      millisecond: 0,\n    });\n\n    let asTS = +date;\n    const over = asTS % 1000;\n    asTS -= over >= 0 ? over : 1000 + over;\n    return (asUTC - asTS) / (60 * 1000);\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"iana\" && otherZone.name === this.name;\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.valid;\n  }\n}\n", "import { hasLocaleWeekInfo, hasRelative, padStart, roundTo, validateWeekSettings } from \"./util.js\";\nimport * as English from \"./english.js\";\nimport Settings from \"../settings.js\";\nimport DateTime from \"../datetime.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n// todo - remap caching\n\nlet intlLFCache = {};\nfunction getCachedLF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlLFCache[key];\n  if (!dtf) {\n    dtf = new Intl.ListFormat(locString, opts);\n    intlLFCache[key] = dtf;\n  }\n  return dtf;\n}\n\nconst intlDTCache = new Map();\nfunction getCachedDTF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlDTCache.get(key);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(locString, opts);\n    intlDTCache.set(key, dtf);\n  }\n  return dtf;\n}\n\nconst intlNumCache = new Map();\nfunction getCachedINF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let inf = intlNumCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.NumberFormat(locString, opts);\n    intlNumCache.set(key, inf);\n  }\n  return inf;\n}\n\nconst intlRelCache = new Map();\nfunction getCachedRTF(locString, opts = {}) {\n  const { base, ...cacheKeyOpts } = opts; // exclude `base` from the options\n  const key = JSON.stringify([locString, cacheKeyOpts]);\n  let inf = intlRelCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.RelativeTimeFormat(locString, opts);\n    intlRelCache.set(key, inf);\n  }\n  return inf;\n}\n\nlet sysLocaleCache = null;\nfunction systemLocale() {\n  if (sysLocaleCache) {\n    return sysLocaleCache;\n  } else {\n    sysLocaleCache = new Intl.DateTimeFormat().resolvedOptions().locale;\n    return sysLocaleCache;\n  }\n}\n\nconst intlResolvedOptionsCache = new Map();\nfunction getCachedIntResolvedOptions(locString) {\n  let opts = intlResolvedOptionsCache.get(locString);\n  if (opts === undefined) {\n    opts = new Intl.DateTimeFormat(locString).resolvedOptions();\n    intlResolvedOptionsCache.set(locString, opts);\n  }\n  return opts;\n}\n\nconst weekInfoCache = new Map();\nfunction getCachedWeekInfo(locString) {\n  let data = weekInfoCache.get(locString);\n  if (!data) {\n    const locale = new Intl.Locale(locString);\n    // browsers currently implement this as a property, but spec says it should be a getter function\n    data = \"getWeekInfo\" in locale ? locale.getWeekInfo() : locale.weekInfo;\n    // minimalDays was removed from WeekInfo: https://github.com/tc39/proposal-intl-locale-info/issues/86\n    if (!(\"minimalDays\" in data)) {\n      data = { ...fallbackWeekSettings, ...data };\n    }\n    weekInfoCache.set(locString, data);\n  }\n  return data;\n}\n\nfunction parseLocaleString(localeStr) {\n  // I really want to avoid writing a BCP 47 parser\n  // see, e.g. https://github.com/wooorm/bcp-47\n  // Instead, we'll do this:\n\n  // a) if the string has no -u extensions, just leave it alone\n  // b) if it does, use Intl to resolve everything\n  // c) if Intl fails, try again without the -u\n\n  // private subtags and unicode subtags have ordering requirements,\n  // and we're not properly parsing this, so just strip out the\n  // private ones if they exist.\n  const xIndex = localeStr.indexOf(\"-x-\");\n  if (xIndex !== -1) {\n    localeStr = localeStr.substring(0, xIndex);\n  }\n\n  const uIndex = localeStr.indexOf(\"-u-\");\n  if (uIndex === -1) {\n    return [localeStr];\n  } else {\n    let options;\n    let selectedStr;\n    try {\n      options = getCachedDTF(localeStr).resolvedOptions();\n      selectedStr = localeStr;\n    } catch (e) {\n      const smaller = localeStr.substring(0, uIndex);\n      options = getCachedDTF(smaller).resolvedOptions();\n      selectedStr = smaller;\n    }\n\n    const { numberingSystem, calendar } = options;\n    return [selectedStr, numberingSystem, calendar];\n  }\n}\n\nfunction intlConfigString(localeStr, numberingSystem, outputCalendar) {\n  if (outputCalendar || numberingSystem) {\n    if (!localeStr.includes(\"-u-\")) {\n      localeStr += \"-u\";\n    }\n\n    if (outputCalendar) {\n      localeStr += `-ca-${outputCalendar}`;\n    }\n\n    if (numberingSystem) {\n      localeStr += `-nu-${numberingSystem}`;\n    }\n    return localeStr;\n  } else {\n    return localeStr;\n  }\n}\n\nfunction mapMonths(f) {\n  const ms = [];\n  for (let i = 1; i <= 12; i++) {\n    const dt = DateTime.utc(2009, i, 1);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction mapWeekdays(f) {\n  const ms = [];\n  for (let i = 1; i <= 7; i++) {\n    const dt = DateTime.utc(2016, 11, 13 + i);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction listStuff(loc, length, englishFn, intlFn) {\n  const mode = loc.listingMode();\n\n  if (mode === \"error\") {\n    return null;\n  } else if (mode === \"en\") {\n    return englishFn(length);\n  } else {\n    return intlFn(length);\n  }\n}\n\nfunction supportsFastNumbers(loc) {\n  if (loc.numberingSystem && loc.numberingSystem !== \"latn\") {\n    return false;\n  } else {\n    return (\n      loc.numberingSystem === \"latn\" ||\n      !loc.locale ||\n      loc.locale.startsWith(\"en\") ||\n      getCachedIntResolvedOptions(loc.locale).numberingSystem === \"latn\"\n    );\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyNumberFormatter {\n  constructor(intl, forceSimple, opts) {\n    this.padTo = opts.padTo || 0;\n    this.floor = opts.floor || false;\n\n    const { padTo, floor, ...otherOpts } = opts;\n\n    if (!forceSimple || Object.keys(otherOpts).length > 0) {\n      const intlOpts = { useGrouping: false, ...opts };\n      if (opts.padTo > 0) intlOpts.minimumIntegerDigits = opts.padTo;\n      this.inf = getCachedINF(intl, intlOpts);\n    }\n  }\n\n  format(i) {\n    if (this.inf) {\n      const fixed = this.floor ? Math.floor(i) : i;\n      return this.inf.format(fixed);\n    } else {\n      // to match the browser's numberformatter defaults\n      const fixed = this.floor ? Math.floor(i) : roundTo(i, 3);\n      return padStart(fixed, this.padTo);\n    }\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyDateFormatter {\n  constructor(dt, intl, opts) {\n    this.opts = opts;\n    this.originalZone = undefined;\n\n    let z = undefined;\n    if (this.opts.timeZone) {\n      // Don't apply any workarounds if a timeZone is explicitly provided in opts\n      this.dt = dt;\n    } else if (dt.zone.type === \"fixed\") {\n      // UTC-8 or Etc/UTC-8 are not part of tzdata, only Etc/GMT+8 and the like.\n      // That is why fixed-offset TZ is set to that unless it is:\n      // 1. Representing offset 0 when UTC is used to maintain previous behavior and does not become GMT.\n      // 2. Unsupported by the browser:\n      //    - some do not support Etc/\n      //    - < Etc/GMT-14, > Etc/GMT+12, and 30-minute or 45-minute offsets are not part of tzdata\n      const gmtOffset = -1 * (dt.offset / 60);\n      const offsetZ = gmtOffset >= 0 ? `Etc/GMT+${gmtOffset}` : `Etc/GMT${gmtOffset}`;\n      if (dt.offset !== 0 && IANAZone.create(offsetZ).valid) {\n        z = offsetZ;\n        this.dt = dt;\n      } else {\n        // Not all fixed-offset zones like Etc/+4:30 are present in tzdata so\n        // we manually apply the offset and substitute the zone as needed.\n        z = \"UTC\";\n        this.dt = dt.offset === 0 ? dt : dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n        this.originalZone = dt.zone;\n      }\n    } else if (dt.zone.type === \"system\") {\n      this.dt = dt;\n    } else if (dt.zone.type === \"iana\") {\n      this.dt = dt;\n      z = dt.zone.name;\n    } else {\n      // Custom zones can have any offset / offsetName so we just manually\n      // apply the offset and substitute the zone as needed.\n      z = \"UTC\";\n      this.dt = dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n      this.originalZone = dt.zone;\n    }\n\n    const intlOpts = { ...this.opts };\n    intlOpts.timeZone = intlOpts.timeZone || z;\n    this.dtf = getCachedDTF(intl, intlOpts);\n  }\n\n  format() {\n    if (this.originalZone) {\n      // If we have to substitute in the actual zone name, we have to use\n      // formatToParts so that the timezone can be replaced.\n      return this.formatToParts()\n        .map(({ value }) => value)\n        .join(\"\");\n    }\n    return this.dtf.format(this.dt.toJSDate());\n  }\n\n  formatToParts() {\n    const parts = this.dtf.formatToParts(this.dt.toJSDate());\n    if (this.originalZone) {\n      return parts.map((part) => {\n        if (part.type === \"timeZoneName\") {\n          const offsetName = this.originalZone.offsetName(this.dt.ts, {\n            locale: this.dt.locale,\n            format: this.opts.timeZoneName,\n          });\n          return {\n            ...part,\n            value: offsetName,\n          };\n        } else {\n          return part;\n        }\n      });\n    }\n    return parts;\n  }\n\n  resolvedOptions() {\n    return this.dtf.resolvedOptions();\n  }\n}\n\n/**\n * @private\n */\nclass PolyRelFormatter {\n  constructor(intl, isEnglish, opts) {\n    this.opts = { style: \"long\", ...opts };\n    if (!isEnglish && hasRelative()) {\n      this.rtf = getCachedRTF(intl, opts);\n    }\n  }\n\n  format(count, unit) {\n    if (this.rtf) {\n      return this.rtf.format(count, unit);\n    } else {\n      return English.formatRelativeTime(unit, count, this.opts.numeric, this.opts.style !== \"long\");\n    }\n  }\n\n  formatToParts(count, unit) {\n    if (this.rtf) {\n      return this.rtf.formatToParts(count, unit);\n    } else {\n      return [];\n    }\n  }\n}\n\nconst fallbackWeekSettings = {\n  firstDay: 1,\n  minimalDays: 4,\n  weekend: [6, 7],\n};\n\n/**\n * @private\n */\nexport default class Locale {\n  static fromOpts(opts) {\n    return Locale.create(\n      opts.locale,\n      opts.numberingSystem,\n      opts.outputCalendar,\n      opts.weekSettings,\n      opts.defaultToEN\n    );\n  }\n\n  static create(locale, numberingSystem, outputCalendar, weekSettings, defaultToEN = false) {\n    const specifiedLocale = locale || Settings.defaultLocale;\n    // the system locale is useful for human-readable strings but annoying for parsing/formatting known formats\n    const localeR = specifiedLocale || (defaultToEN ? \"en-US\" : systemLocale());\n    const numberingSystemR = numberingSystem || Settings.defaultNumberingSystem;\n    const outputCalendarR = outputCalendar || Settings.defaultOutputCalendar;\n    const weekSettingsR = validateWeekSettings(weekSettings) || Settings.defaultWeekSettings;\n    return new Locale(localeR, numberingSystemR, outputCalendarR, weekSettingsR, specifiedLocale);\n  }\n\n  static resetCache() {\n    sysLocaleCache = null;\n    intlDTCache.clear();\n    intlNumCache.clear();\n    intlRelCache.clear();\n    intlResolvedOptionsCache.clear();\n    weekInfoCache.clear();\n  }\n\n  static fromObject({ locale, numberingSystem, outputCalendar, weekSettings } = {}) {\n    return Locale.create(locale, numberingSystem, outputCalendar, weekSettings);\n  }\n\n  constructor(locale, numbering, outputCalendar, weekSettings, specifiedLocale) {\n    const [parsedLocale, parsedNumberingSystem, parsedOutputCalendar] = parseLocaleString(locale);\n\n    this.locale = parsedLocale;\n    this.numberingSystem = numbering || parsedNumberingSystem || null;\n    this.outputCalendar = outputCalendar || parsedOutputCalendar || null;\n    this.weekSettings = weekSettings;\n    this.intl = intlConfigString(this.locale, this.numberingSystem, this.outputCalendar);\n\n    this.weekdaysCache = { format: {}, standalone: {} };\n    this.monthsCache = { format: {}, standalone: {} };\n    this.meridiemCache = null;\n    this.eraCache = {};\n\n    this.specifiedLocale = specifiedLocale;\n    this.fastNumbersCached = null;\n  }\n\n  get fastNumbers() {\n    if (this.fastNumbersCached == null) {\n      this.fastNumbersCached = supportsFastNumbers(this);\n    }\n\n    return this.fastNumbersCached;\n  }\n\n  listingMode() {\n    const isActuallyEn = this.isEnglish();\n    const hasNoWeirdness =\n      (this.numberingSystem === null || this.numberingSystem === \"latn\") &&\n      (this.outputCalendar === null || this.outputCalendar === \"gregory\");\n    return isActuallyEn && hasNoWeirdness ? \"en\" : \"intl\";\n  }\n\n  clone(alts) {\n    if (!alts || Object.getOwnPropertyNames(alts).length === 0) {\n      return this;\n    } else {\n      return Locale.create(\n        alts.locale || this.specifiedLocale,\n        alts.numberingSystem || this.numberingSystem,\n        alts.outputCalendar || this.outputCalendar,\n        validateWeekSettings(alts.weekSettings) || this.weekSettings,\n        alts.defaultToEN || false\n      );\n    }\n  }\n\n  redefaultToEN(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: true });\n  }\n\n  redefaultToSystem(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: false });\n  }\n\n  months(length, format = false) {\n    return listStuff(this, length, English.months, () => {\n      // Workaround for \"ja\" locale: formatToParts does not label all parts of the month\n      // as \"month\" and for this locale there is no difference between \"format\" and \"non-format\".\n      // As such, just use format() instead of formatToParts() and take the whole string\n      const monthSpecialCase = this.intl === \"ja\" || this.intl.startsWith(\"ja-\");\n      format &= !monthSpecialCase;\n      const intl = format ? { month: length, day: \"numeric\" } : { month: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.monthsCache[formatStr][length]) {\n        const mapper = !monthSpecialCase\n          ? (dt) => this.extract(dt, intl, \"month\")\n          : (dt) => this.dtFormatter(dt, intl).format();\n        this.monthsCache[formatStr][length] = mapMonths(mapper);\n      }\n      return this.monthsCache[formatStr][length];\n    });\n  }\n\n  weekdays(length, format = false) {\n    return listStuff(this, length, English.weekdays, () => {\n      const intl = format\n          ? { weekday: length, year: \"numeric\", month: \"long\", day: \"numeric\" }\n          : { weekday: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.weekdaysCache[formatStr][length]) {\n        this.weekdaysCache[formatStr][length] = mapWeekdays((dt) =>\n          this.extract(dt, intl, \"weekday\")\n        );\n      }\n      return this.weekdaysCache[formatStr][length];\n    });\n  }\n\n  meridiems() {\n    return listStuff(\n      this,\n      undefined,\n      () => English.meridiems,\n      () => {\n        // In theory there could be aribitrary day periods. We're gonna assume there are exactly two\n        // for AM and PM. This is probably wrong, but it's makes parsing way easier.\n        if (!this.meridiemCache) {\n          const intl = { hour: \"numeric\", hourCycle: \"h12\" };\n          this.meridiemCache = [DateTime.utc(2016, 11, 13, 9), DateTime.utc(2016, 11, 13, 19)].map(\n            (dt) => this.extract(dt, intl, \"dayperiod\")\n          );\n        }\n\n        return this.meridiemCache;\n      }\n    );\n  }\n\n  eras(length) {\n    return listStuff(this, length, English.eras, () => {\n      const intl = { era: length };\n\n      // This is problematic. Different calendars are going to define eras totally differently. What I need is the minimum set of dates\n      // to definitely enumerate them.\n      if (!this.eraCache[length]) {\n        this.eraCache[length] = [DateTime.utc(-40, 1, 1), DateTime.utc(2017, 1, 1)].map((dt) =>\n          this.extract(dt, intl, \"era\")\n        );\n      }\n\n      return this.eraCache[length];\n    });\n  }\n\n  extract(dt, intlOpts, field) {\n    const df = this.dtFormatter(dt, intlOpts),\n      results = df.formatToParts(),\n      matching = results.find((m) => m.type.toLowerCase() === field);\n    return matching ? matching.value : null;\n  }\n\n  numberFormatter(opts = {}) {\n    // this forcesimple option is never used (the only caller short-circuits on it, but it seems safer to leave)\n    // (in contrast, the rest of the condition is used heavily)\n    return new PolyNumberFormatter(this.intl, opts.forceSimple || this.fastNumbers, opts);\n  }\n\n  dtFormatter(dt, intlOpts = {}) {\n    return new PolyDateFormatter(dt, this.intl, intlOpts);\n  }\n\n  relFormatter(opts = {}) {\n    return new PolyRelFormatter(this.intl, this.isEnglish(), opts);\n  }\n\n  listFormatter(opts = {}) {\n    return getCachedLF(this.intl, opts);\n  }\n\n  isEnglish() {\n    return (\n      this.locale === \"en\" ||\n      this.locale.toLowerCase() === \"en-us\" ||\n      getCachedIntResolvedOptions(this.intl).locale.startsWith(\"en-us\")\n    );\n  }\n\n  getWeekSettings() {\n    if (this.weekSettings) {\n      return this.weekSettings;\n    } else if (!hasLocaleWeekInfo()) {\n      return fallbackWeekSettings;\n    } else {\n      return getCachedWeekInfo(this.locale);\n    }\n  }\n\n  getStartOfWeek() {\n    return this.getWeekSettings().firstDay;\n  }\n\n  getMinDaysInFirstWeek() {\n    return this.getWeekSettings().minimalDays;\n  }\n\n  getWeekendDays() {\n    return this.getWeekSettings().weekend;\n  }\n\n  equals(other) {\n    return (\n      this.locale === other.locale &&\n      this.numberingSystem === other.numberingSystem &&\n      this.outputCalendar === other.outputCalendar\n    );\n  }\n\n  toString() {\n    return `Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`;\n  }\n}\n", "import { formatOffset, signedOffset } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * A zone with a fixed offset (meaning no DST)\n * @implements {Zone}\n */\nexport default class FixedOffsetZone extends Zone {\n  /**\n   * Get a singleton instance of UTC\n   * @return {FixedOffsetZone}\n   */\n  static get utcInstance() {\n    if (singleton === null) {\n      singleton = new FixedOffsetZone(0);\n    }\n    return singleton;\n  }\n\n  /**\n   * Get an instance with a specified offset\n   * @param {number} offset - The offset in minutes\n   * @return {FixedOffsetZone}\n   */\n  static instance(offset) {\n    return offset === 0 ? FixedOffsetZone.utcInstance : new FixedOffsetZone(offset);\n  }\n\n  /**\n   * Get an instance of FixedOffsetZone from a UTC offset string, like \"UTC+6\"\n   * @param {string} s - The offset string to parse\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+6\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+06\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC-6:00\")\n   * @return {FixedOffsetZone}\n   */\n  static parseSpecifier(s) {\n    if (s) {\n      const r = s.match(/^utc(?:([+-]\\d{1,2})(?::(\\d{2}))?)?$/i);\n      if (r) {\n        return new FixedOffsetZone(signedOffset(r[1], r[2]));\n      }\n    }\n    return null;\n  }\n\n  constructor(offset) {\n    super();\n    /** @private **/\n    this.fixed = offset;\n  }\n\n  /**\n   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"fixed\";\n  }\n\n  /**\n   * The name of this zone.\n   * All fixed zones' names always start with \"UTC\" (plus optional offset)\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.fixed === 0 ? \"UTC\" : `UTC${formatOffset(this.fixed, \"narrow\")}`;\n  }\n\n  /**\n   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`\n   *\n   * @override\n   * @type {string}\n   */\n  get ianaName() {\n    if (this.fixed === 0) {\n      return \"Etc/UTC\";\n    } else {\n      return `Etc/GMT${formatOffset(-this.fixed, \"narrow\")}`;\n    }\n  }\n\n  /**\n   * Returns the offset's common name at the specified timestamp.\n   *\n   * For fixed offset zones this equals to the zone name.\n   * @override\n   */\n  offsetName() {\n    return this.name;\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.fixed, format);\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns true for all fixed offset zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return true;\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   *\n   * For fixed offset zones, this is constant and does not depend on a timestamp.\n   * @override\n   * @return {number}\n   */\n  offset() {\n    return this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"fixed\" && otherZone.fixed === this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is valid:\n   * All fixed offset zones are valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return true;\n  }\n}\n", "import Zone from \"../zone.js\";\n\n/**\n * A zone that failed to parse. You should never need to instantiate this.\n * @implements {Zone}\n */\nexport default class InvalidZone extends Zone {\n  constructor(zoneName) {\n    super();\n    /**  @private */\n    this.zoneName = zoneName;\n  }\n\n  /** @override **/\n  get type() {\n    return \"invalid\";\n  }\n\n  /** @override **/\n  get name() {\n    return this.zoneName;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName() {\n    return null;\n  }\n\n  /** @override **/\n  formatOffset() {\n    return \"\";\n  }\n\n  /** @override **/\n  offset() {\n    return NaN;\n  }\n\n  /** @override **/\n  equals() {\n    return false;\n  }\n\n  /** @override **/\n  get isValid() {\n    return false;\n  }\n}\n", "/**\n * @private\n */\n\nimport Zone from \"../zone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport InvalidZone from \"../zones/invalidZone.js\";\n\nimport { isUndefined, isString, isNumber } from \"./util.js\";\nimport SystemZone from \"../zones/systemZone.js\";\n\nexport function normalizeZone(input, defaultZone) {\n  let offset;\n  if (isUndefined(input) || input === null) {\n    return defaultZone;\n  } else if (input instanceof Zone) {\n    return input;\n  } else if (isString(input)) {\n    const lowered = input.toLowerCase();\n    if (lowered === \"default\") return defaultZone;\n    else if (lowered === \"local\" || lowered === \"system\") return SystemZone.instance;\n    else if (lowered === \"utc\" || lowered === \"gmt\") return FixedOffsetZone.utcInstance;\n    else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);\n  } else if (isNumber(input)) {\n    return FixedOffsetZone.instance(input);\n  } else if (typeof input === \"object\" && \"offset\" in input && typeof input.offset === \"function\") {\n    // This is dumb, but the instanceof check above doesn't seem to really work\n    // so we're duck checking it\n    return input;\n  } else {\n    return new InvalidZone(input);\n  }\n}\n", "const numberingSystems = {\n  arab: \"[\\u0660-\\u0669]\",\n  arabext: \"[\\u06F0-\\u06F9]\",\n  bali: \"[\\u1B50-\\u1B59]\",\n  beng: \"[\\u09E6-\\u09EF]\",\n  deva: \"[\\u0966-\\u096F]\",\n  fullwide: \"[\\uFF10-\\uFF19]\",\n  gujr: \"[\\u0AE6-\\u0AEF]\",\n  hanidec: \"[〇|一|二|三|四|五|六|七|八|九]\",\n  khmr: \"[\\u17E0-\\u17E9]\",\n  knda: \"[\\u0CE6-\\u0CEF]\",\n  laoo: \"[\\u0ED0-\\u0ED9]\",\n  limb: \"[\\u1946-\\u194F]\",\n  mlym: \"[\\u0D66-\\u0D6F]\",\n  mong: \"[\\u1810-\\u1819]\",\n  mymr: \"[\\u1040-\\u1049]\",\n  orya: \"[\\u0B66-\\u0B6F]\",\n  tamldec: \"[\\u0BE6-\\u0BEF]\",\n  telu: \"[\\u0C66-\\u0C6F]\",\n  thai: \"[\\u0E50-\\u0E59]\",\n  tibt: \"[\\u0F20-\\u0F29]\",\n  latn: \"\\\\d\",\n};\n\nconst numberingSystemsUTF16 = {\n  arab: [1632, 1641],\n  arabext: [1776, 1785],\n  bali: [6992, 7001],\n  beng: [2534, 2543],\n  deva: [2406, 2415],\n  fullwide: [65296, 65303],\n  gujr: [2790, 2799],\n  khmr: [6112, 6121],\n  knda: [3302, 3311],\n  laoo: [3792, 3801],\n  limb: [6470, 6479],\n  mlym: [3430, 3439],\n  mong: [6160, 6169],\n  mymr: [4160, 4169],\n  orya: [2918, 2927],\n  tamldec: [3046, 3055],\n  telu: [3174, 3183],\n  thai: [3664, 3673],\n  tibt: [3872, 3881],\n};\n\nconst hanidecChars = numberingSystems.hanidec.replace(/[\\[|\\]]/g, \"\").split(\"\");\n\nexport function parseDigits(str) {\n  let value = parseInt(str, 10);\n  if (isNaN(value)) {\n    value = \"\";\n    for (let i = 0; i < str.length; i++) {\n      const code = str.charCodeAt(i);\n\n      if (str[i].search(numberingSystems.hanidec) !== -1) {\n        value += hanidecChars.indexOf(str[i]);\n      } else {\n        for (const key in numberingSystemsUTF16) {\n          const [min, max] = numberingSystemsUTF16[key];\n          if (code >= min && code <= max) {\n            value += code - min;\n          }\n        }\n      }\n    }\n    return parseInt(value, 10);\n  } else {\n    return value;\n  }\n}\n\n// cache of {numberingSystem: {append: regex}}\nconst digitRegexCache = new Map();\nexport function resetDigitRegexCache() {\n  digitRegexCache.clear();\n}\n\nexport function digitRegex({ numberingSystem }, append = \"\") {\n  const ns = numberingSystem || \"latn\";\n\n  let appendCache = digitRegexCache.get(ns);\n  if (appendCache === undefined) {\n    appendCache = new Map();\n    digitRegexCache.set(ns, appendCache);\n  }\n  let regex = appendCache.get(append);\n  if (regex === undefined) {\n    regex = new RegExp(`${numberingSystems[ns]}${append}`);\n    appendCache.set(append, regex);\n  }\n\n  return regex;\n}\n", "import SystemZone from \"./zones/systemZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport DateTime from \"./datetime.js\";\n\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport { validateWeekSettings } from \"./impl/util.js\";\nimport { resetDigitRegexCache } from \"./impl/digits.js\";\n\nlet now = () => Date.now(),\n  defaultZone = \"system\",\n  defaultLocale = null,\n  defaultNumberingSystem = null,\n  defaultOutputCalendar = null,\n  twoDigitCutoffYear = 60,\n  throwOnInvalid,\n  defaultWeekSettings = null;\n\n/**\n * Settings contains static getters and setters that control <PERSON><PERSON>'s overall behavior. Luxon is a simple library with few options, but the ones it does have live here.\n */\nexport default class Settings {\n  /**\n   * Get the callback for returning the current timestamp.\n   * @type {function}\n   */\n  static get now() {\n    return now;\n  }\n\n  /**\n   * Set the callback for returning the current timestamp.\n   * The function should return a number, which will be interpreted as an Epoch millisecond count\n   * @type {function}\n   * @example Settings.now = () => Date.now() + 3000 // pretend it is 3 seconds in the future\n   * @example Settings.now = () => 0 // always pretend it's Jan 1, 1970 at midnight in UTC time\n   */\n  static set now(n) {\n    now = n;\n  }\n\n  /**\n   * Set the default time zone to create DateTimes in. Does not affect existing instances.\n   * Use the value \"system\" to reset this value to the system's time zone.\n   * @type {string}\n   */\n  static set defaultZone(zone) {\n    defaultZone = zone;\n  }\n\n  /**\n   * Get the default time zone object currently used to create DateTimes. Does not affect existing instances.\n   * The default value is the system's time zone (the one set on the machine that runs this code).\n   * @type {Zone}\n   */\n  static get defaultZone() {\n    return normalizeZone(defaultZone, SystemZone.instance);\n  }\n\n  /**\n   * Get the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultLocale() {\n    return defaultLocale;\n  }\n\n  /**\n   * Set the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultLocale(locale) {\n    defaultLocale = locale;\n  }\n\n  /**\n   * Get the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultNumberingSystem() {\n    return defaultNumberingSystem;\n  }\n\n  /**\n   * Set the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultNumberingSystem(numberingSystem) {\n    defaultNumberingSystem = numberingSystem;\n  }\n\n  /**\n   * Get the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultOutputCalendar() {\n    return defaultOutputCalendar;\n  }\n\n  /**\n   * Set the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultOutputCalendar(outputCalendar) {\n    defaultOutputCalendar = outputCalendar;\n  }\n\n  /**\n   * @typedef {Object} WeekSettings\n   * @property {number} firstDay\n   * @property {number} minimalDays\n   * @property {number[]} weekend\n   */\n\n  /**\n   * @return {WeekSettings|null}\n   */\n  static get defaultWeekSettings() {\n    return defaultWeekSettings;\n  }\n\n  /**\n   * Allows overriding the default locale week settings, i.e. the start of the week, the weekend and\n   * how many days are required in the first week of a year.\n   * Does not affect existing instances.\n   *\n   * @param {WeekSettings|null} weekSettings\n   */\n  static set defaultWeekSettings(weekSettings) {\n    defaultWeekSettings = validateWeekSettings(weekSettings);\n  }\n\n  /**\n   * Get the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   */\n  static get twoDigitCutoffYear() {\n    return twoDigitCutoffYear;\n  }\n\n  /**\n   * Set the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   * @example Settings.twoDigitCutoffYear = 0 // all 'yy' are interpreted as 20th century\n   * @example Settings.twoDigitCutoffYear = 99 // all 'yy' are interpreted as 21st century\n   * @example Settings.twoDigitCutoffYear = 50 // '49' -> 2049; '50' -> 1950\n   * @example Settings.twoDigitCutoffYear = 1950 // interpreted as 50\n   * @example Settings.twoDigitCutoffYear = 2050 // ALSO interpreted as 50\n   */\n  static set twoDigitCutoffYear(cutoffYear) {\n    twoDigitCutoffYear = cutoffYear % 100;\n  }\n\n  /**\n   * Get whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static get throwOnInvalid() {\n    return throwOnInvalid;\n  }\n\n  /**\n   * Set whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static set throwOnInvalid(t) {\n    throwOnInvalid = t;\n  }\n\n  /**\n   * Reset Luxon's global caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCaches() {\n    Locale.resetCache();\n    IANAZone.resetCache();\n    DateTime.resetCache();\n    resetDigitRegexCache();\n  }\n}\n", "export default class Invalid {\n  constructor(reason, explanation) {\n    this.reason = reason;\n    this.explanation = explanation;\n  }\n\n  toMessage() {\n    if (this.explanation) {\n      return `${this.reason}: ${this.explanation}`;\n    } else {\n      return this.reason;\n    }\n  }\n}\n", "import {\n  integerBetween,\n  isLeapYear,\n  timeObject,\n  daysInYear,\n  daysInMonth,\n  weeksInWeekYear,\n  isInteger,\n  isUndefined,\n} from \"./util.js\";\nimport Invalid from \"./invalid.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],\n  leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];\n\nfunction unitOutOfRange(unit, value) {\n  return new Invalid(\n    \"unit out of range\",\n    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`\n  );\n}\n\nexport function dayOfWeek(year, month, day) {\n  const d = new Date(Date.UTC(year, month - 1, day));\n\n  if (year < 100 && year >= 0) {\n    d.setUTCFullYear(d.getUTCFullYear() - 1900);\n  }\n\n  const js = d.getUTCDay();\n\n  return js === 0 ? 7 : js;\n}\n\nfunction computeOrdinal(year, month, day) {\n  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];\n}\n\nfunction uncomputeOrdinal(year, ordinal) {\n  const table = isLeapYear(year) ? leapLadder : nonLeapLadder,\n    month0 = table.findIndex((i) => i < ordinal),\n    day = ordinal - table[month0];\n  return { month: month0 + 1, day };\n}\n\nexport function isoWeekdayToLocal(isoWeekday, startOfWeek) {\n  return ((isoWeekday - startOfWeek + 7) % 7) + 1;\n}\n\n/**\n * @private\n */\n\nexport function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { year, month, day } = gregObj,\n    ordinal = computeOrdinal(year, month, day),\n    weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);\n\n  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7),\n    weekYear;\n\n  if (weekNumber < 1) {\n    weekYear = year - 1;\n    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);\n  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {\n    weekYear = year + 1;\n    weekNumber = 1;\n  } else {\n    weekYear = year;\n  }\n\n  return { weekYear, weekNumber, weekday, ...timeObject(gregObj) };\n}\n\nexport function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { weekYear, weekNumber, weekday } = weekData,\n    weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek),\n    yearInDays = daysInYear(weekYear);\n\n  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek,\n    year;\n\n  if (ordinal < 1) {\n    year = weekYear - 1;\n    ordinal += daysInYear(year);\n  } else if (ordinal > yearInDays) {\n    year = weekYear + 1;\n    ordinal -= daysInYear(weekYear);\n  } else {\n    year = weekYear;\n  }\n\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(weekData) };\n}\n\nexport function gregorianToOrdinal(gregData) {\n  const { year, month, day } = gregData;\n  const ordinal = computeOrdinal(year, month, day);\n  return { year, ordinal, ...timeObject(gregData) };\n}\n\nexport function ordinalToGregorian(ordinalData) {\n  const { year, ordinal } = ordinalData;\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(ordinalData) };\n}\n\n/**\n * Check if local week units like localWeekday are used in obj.\n * If so, validates that they are not mixed with ISO week units and then copies them to the normal week unit properties.\n * Modifies obj in-place!\n * @param obj the object values\n */\nexport function usesLocalWeekValues(obj, loc) {\n  const hasLocaleWeekData =\n    !isUndefined(obj.localWeekday) ||\n    !isUndefined(obj.localWeekNumber) ||\n    !isUndefined(obj.localWeekYear);\n  if (hasLocaleWeekData) {\n    const hasIsoWeekData =\n      !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);\n\n    if (hasIsoWeekData) {\n      throw new ConflictingSpecificationError(\n        \"Cannot mix locale-based week fields with ISO-based week fields\"\n      );\n    }\n    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;\n    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;\n    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;\n    delete obj.localWeekday;\n    delete obj.localWeekNumber;\n    delete obj.localWeekYear;\n    return {\n      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),\n      startOfWeek: loc.getStartOfWeek(),\n    };\n  } else {\n    return { minDaysInFirstWeek: 4, startOfWeek: 1 };\n  }\n}\n\nexport function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const validYear = isInteger(obj.weekYear),\n    validWeek = integerBetween(\n      obj.weekNumber,\n      1,\n      weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)\n    ),\n    validWeekday = integerBetween(obj.weekday, 1, 7);\n\n  if (!validYear) {\n    return unitOutOfRange(\"weekYear\", obj.weekYear);\n  } else if (!validWeek) {\n    return unitOutOfRange(\"week\", obj.weekNumber);\n  } else if (!validWeekday) {\n    return unitOutOfRange(\"weekday\", obj.weekday);\n  } else return false;\n}\n\nexport function hasInvalidOrdinalData(obj) {\n  const validYear = isInteger(obj.year),\n    validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validOrdinal) {\n    return unitOutOfRange(\"ordinal\", obj.ordinal);\n  } else return false;\n}\n\nexport function hasInvalidGregorianData(obj) {\n  const validYear = isInteger(obj.year),\n    validMonth = integerBetween(obj.month, 1, 12),\n    validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validMonth) {\n    return unitOutOfRange(\"month\", obj.month);\n  } else if (!validDay) {\n    return unitOutOfRange(\"day\", obj.day);\n  } else return false;\n}\n\nexport function hasInvalidTimeData(obj) {\n  const { hour, minute, second, millisecond } = obj;\n  const validHour =\n      integerBetween(hour, 0, 23) ||\n      (hour === 24 && minute === 0 && second === 0 && millisecond === 0),\n    validMinute = integerBetween(minute, 0, 59),\n    validSecond = integerBetween(second, 0, 59),\n    validMillisecond = integerBetween(millisecond, 0, 999);\n\n  if (!validHour) {\n    return unitOutOfRange(\"hour\", hour);\n  } else if (!validMinute) {\n    return unitOutOfRange(\"minute\", minute);\n  } else if (!validSecond) {\n    return unitOutOfRange(\"second\", second);\n  } else if (!validMillisecond) {\n    return unitOutOfRange(\"millisecond\", millisecond);\n  } else return false;\n}\n", "/*\n  This is just a junk drawer, containing anything used across multiple classes.\n  Because <PERSON>xon is small(ish), this should stay small and we won't worry about splitting\n  it up into, say, parsingUtil.js and basicUtil.js and so on. But they are divided up by feature area.\n*/\n\nimport { InvalidArgumentError } from \"../errors.js\";\nimport Settings from \"../settings.js\";\nimport { dayOfWeek, isoWeekdayToLocal } from \"./conversions.js\";\n\n/**\n * @private\n */\n\n// TYPES\n\nexport function isUndefined(o) {\n  return typeof o === \"undefined\";\n}\n\nexport function isNumber(o) {\n  return typeof o === \"number\";\n}\n\nexport function isInteger(o) {\n  return typeof o === \"number\" && o % 1 === 0;\n}\n\nexport function isString(o) {\n  return typeof o === \"string\";\n}\n\nexport function isDate(o) {\n  return Object.prototype.toString.call(o) === \"[object Date]\";\n}\n\n// CAPABILITIES\n\nexport function hasRelative() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.RelativeTimeFormat;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function hasLocaleWeekInfo() {\n  try {\n    return (\n      typeof Intl !== \"undefined\" &&\n      !!Intl.Locale &&\n      (\"weekInfo\" in Intl.Locale.prototype || \"getWeekInfo\" in Intl.Locale.prototype)\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n// OBJECTS AND ARRAYS\n\nexport function maybeArray(thing) {\n  return Array.isArray(thing) ? thing : [thing];\n}\n\nexport function bestBy(arr, by, compare) {\n  if (arr.length === 0) {\n    return undefined;\n  }\n  return arr.reduce((best, next) => {\n    const pair = [by(next), next];\n    if (!best) {\n      return pair;\n    } else if (compare(best[0], pair[0]) === best[0]) {\n      return best;\n    } else {\n      return pair;\n    }\n  }, null)[1];\n}\n\nexport function pick(obj, keys) {\n  return keys.reduce((a, k) => {\n    a[k] = obj[k];\n    return a;\n  }, {});\n}\n\nexport function hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport function validateWeekSettings(settings) {\n  if (settings == null) {\n    return null;\n  } else if (typeof settings !== \"object\") {\n    throw new InvalidArgumentError(\"Week settings must be an object\");\n  } else {\n    if (\n      !integerBetween(settings.firstDay, 1, 7) ||\n      !integerBetween(settings.minimalDays, 1, 7) ||\n      !Array.isArray(settings.weekend) ||\n      settings.weekend.some((v) => !integerBetween(v, 1, 7))\n    ) {\n      throw new InvalidArgumentError(\"Invalid week settings\");\n    }\n    return {\n      firstDay: settings.firstDay,\n      minimalDays: settings.minimalDays,\n      weekend: Array.from(settings.weekend),\n    };\n  }\n}\n\n// NUMBERS AND STRINGS\n\nexport function integerBetween(thing, bottom, top) {\n  return isInteger(thing) && thing >= bottom && thing <= top;\n}\n\n// x % n but takes the sign of n instead of x\nexport function floorMod(x, n) {\n  return x - n * Math.floor(x / n);\n}\n\nexport function padStart(input, n = 2) {\n  const isNeg = input < 0;\n  let padded;\n  if (isNeg) {\n    padded = \"-\" + (\"\" + -input).padStart(n, \"0\");\n  } else {\n    padded = (\"\" + input).padStart(n, \"0\");\n  }\n  return padded;\n}\n\nexport function parseInteger(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseInt(string, 10);\n  }\n}\n\nexport function parseFloating(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseFloat(string);\n  }\n}\n\nexport function parseMillis(fraction) {\n  // Return undefined (instead of 0) in these cases, where fraction is not set\n  if (isUndefined(fraction) || fraction === null || fraction === \"\") {\n    return undefined;\n  } else {\n    const f = parseFloat(\"0.\" + fraction) * 1000;\n    return Math.floor(f);\n  }\n}\n\nexport function roundTo(number, digits, rounding = \"round\") {\n  const factor = 10 ** digits;\n  switch (rounding) {\n    case \"expand\":\n      return number > 0\n        ? Math.ceil(number * factor) / factor\n        : Math.floor(number * factor) / factor;\n    case \"trunc\":\n      return Math.trunc(number * factor) / factor;\n    case \"round\":\n      return Math.round(number * factor) / factor;\n    case \"floor\":\n      return Math.floor(number * factor) / factor;\n    case \"ceil\":\n      return Math.ceil(number * factor) / factor;\n    default:\n      throw new RangeError(`Value rounding ${rounding} is out of range`);\n  }\n}\n\n// DATE BASICS\n\nexport function isLeapYear(year) {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\n\nexport function daysInMonth(year, month) {\n  const modMonth = floorMod(month - 1, 12) + 1,\n    modYear = year + (month - modMonth) / 12;\n\n  if (modMonth === 2) {\n    return isLeapYear(modYear) ? 29 : 28;\n  } else {\n    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];\n  }\n}\n\n// convert a calendar object to a local timestamp (epoch, but with the offset baked in)\nexport function objToLocalTS(obj) {\n  let d = Date.UTC(\n    obj.year,\n    obj.month - 1,\n    obj.day,\n    obj.hour,\n    obj.minute,\n    obj.second,\n    obj.millisecond\n  );\n\n  // for legacy reasons, years between 0 and 99 are interpreted as 19XX; revert that\n  if (obj.year < 100 && obj.year >= 0) {\n    d = new Date(d);\n    // set the month and day again, this is necessary because year 2000 is a leap year, but year 100 is not\n    // so if obj.year is in 99, but obj.day makes it roll over into year 100,\n    // the calculations done by Date.UTC are using year 2000 - which is incorrect\n    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);\n  }\n  return +d;\n}\n\n// adapted from moment.js: https://github.com/moment/moment/blob/000ac1800e620f770f4eb31b5ae908f6167b0ab2/src/lib/units/week-calendar-utils.js\nfunction firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {\n  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);\n  return -fwdlw + minDaysInFirstWeek - 1;\n}\n\nexport function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);\n  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);\n  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;\n}\n\nexport function untruncateYear(year) {\n  if (year > 99) {\n    return year;\n  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2000 + year;\n}\n\n// PARSING\n\nexport function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {\n  const date = new Date(ts),\n    intlOpts = {\n      hourCycle: \"h23\",\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    };\n\n  if (timeZone) {\n    intlOpts.timeZone = timeZone;\n  }\n\n  const modified = { timeZoneName: offsetFormat, ...intlOpts };\n\n  const parsed = new Intl.DateTimeFormat(locale, modified)\n    .formatToParts(date)\n    .find((m) => m.type.toLowerCase() === \"timezonename\");\n  return parsed ? parsed.value : null;\n}\n\n// signedOffset('-5', '30') -> -330\nexport function signedOffset(offHourStr, offMinuteStr) {\n  let offHour = parseInt(offHourStr, 10);\n\n  // don't || this because we want to preserve -0\n  if (Number.isNaN(offHour)) {\n    offHour = 0;\n  }\n\n  const offMin = parseInt(offMinuteStr, 10) || 0,\n    offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;\n  return offHour * 60 + offMinSigned;\n}\n\n// COERCION\n\nexport function asNumber(value) {\n  const numericValue = Number(value);\n  if (typeof value === \"boolean\" || value === \"\" || !Number.isFinite(numericValue))\n    throw new InvalidArgumentError(`Invalid unit value ${value}`);\n  return numericValue;\n}\n\nexport function normalizeObject(obj, normalizer) {\n  const normalized = {};\n  for (const u in obj) {\n    if (hasOwnProperty(obj, u)) {\n      const v = obj[u];\n      if (v === undefined || v === null) continue;\n      normalized[normalizer(u)] = asNumber(v);\n    }\n  }\n  return normalized;\n}\n\n/**\n * Returns the offset's value as a string\n * @param {number} ts - Epoch milliseconds for which to get the offset\n * @param {string} format - What style of offset to return.\n *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n * @return {string}\n */\nexport function formatOffset(offset, format) {\n  const hours = Math.trunc(Math.abs(offset / 60)),\n    minutes = Math.trunc(Math.abs(offset % 60)),\n    sign = offset >= 0 ? \"+\" : \"-\";\n\n  switch (format) {\n    case \"short\":\n      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;\n    case \"narrow\":\n      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : \"\"}`;\n    case \"techie\":\n      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;\n    default:\n      throw new RangeError(`Value format ${format} is out of range for property format`);\n  }\n}\n\nexport function timeObject(obj) {\n  return pick(obj, [\"hour\", \"minute\", \"second\", \"millisecond\"]);\n}\n", "import * as Formats from \"./formats.js\";\nimport { pick } from \"./util.js\";\n\nfunction stringify(obj) {\n  return JSON.stringify(obj, Object.keys(obj).sort());\n}\n\n/**\n * @private\n */\n\nexport const monthsLong = [\n  \"January\",\n  \"February\",\n  \"March\",\n  \"April\",\n  \"May\",\n  \"June\",\n  \"July\",\n  \"August\",\n  \"September\",\n  \"October\",\n  \"November\",\n  \"December\",\n];\n\nexport const monthsShort = [\n  \"Jan\",\n  \"Feb\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"Jun\",\n  \"Jul\",\n  \"Aug\",\n  \"Sep\",\n  \"Oct\",\n  \"Nov\",\n  \"Dec\",\n];\n\nexport const monthsNarrow = [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"];\n\nexport function months(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...monthsNarrow];\n    case \"short\":\n      return [...monthsShort];\n    case \"long\":\n      return [...monthsLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"];\n    case \"2-digit\":\n      return [\"01\", \"02\", \"03\", \"04\", \"05\", \"06\", \"07\", \"08\", \"09\", \"10\", \"11\", \"12\"];\n    default:\n      return null;\n  }\n}\n\nexport const weekdaysLong = [\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\",\n  \"Sunday\",\n];\n\nexport const weekdaysShort = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"];\n\nexport const weekdaysNarrow = [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\n\nexport function weekdays(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...weekdaysNarrow];\n    case \"short\":\n      return [...weekdaysShort];\n    case \"long\":\n      return [...weekdaysLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    default:\n      return null;\n  }\n}\n\nexport const meridiems = [\"AM\", \"PM\"];\n\nexport const erasLong = [\"Before Christ\", \"Anno Domini\"];\n\nexport const erasShort = [\"BC\", \"AD\"];\n\nexport const erasNarrow = [\"B\", \"A\"];\n\nexport function eras(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...erasNarrow];\n    case \"short\":\n      return [...erasShort];\n    case \"long\":\n      return [...erasLong];\n    default:\n      return null;\n  }\n}\n\nexport function meridiemForDateTime(dt) {\n  return meridiems[dt.hour < 12 ? 0 : 1];\n}\n\nexport function weekdayForDateTime(dt, length) {\n  return weekdays(length)[dt.weekday - 1];\n}\n\nexport function monthForDateTime(dt, length) {\n  return months(length)[dt.month - 1];\n}\n\nexport function eraForDateTime(dt, length) {\n  return eras(length)[dt.year < 0 ? 0 : 1];\n}\n\nexport function formatRelativeTime(unit, count, numeric = \"always\", narrow = false) {\n  const units = {\n    years: [\"year\", \"yr.\"],\n    quarters: [\"quarter\", \"qtr.\"],\n    months: [\"month\", \"mo.\"],\n    weeks: [\"week\", \"wk.\"],\n    days: [\"day\", \"day\", \"days\"],\n    hours: [\"hour\", \"hr.\"],\n    minutes: [\"minute\", \"min.\"],\n    seconds: [\"second\", \"sec.\"],\n  };\n\n  const lastable = [\"hours\", \"minutes\", \"seconds\"].indexOf(unit) === -1;\n\n  if (numeric === \"auto\" && lastable) {\n    const isDay = unit === \"days\";\n    switch (count) {\n      case 1:\n        return isDay ? \"tomorrow\" : `next ${units[unit][0]}`;\n      case -1:\n        return isDay ? \"yesterday\" : `last ${units[unit][0]}`;\n      case 0:\n        return isDay ? \"today\" : `this ${units[unit][0]}`;\n      default: // fall through\n    }\n  }\n\n  const isInPast = Object.is(count, -0) || count < 0,\n    fmtValue = Math.abs(count),\n    singular = fmtValue === 1,\n    lilUnits = units[unit],\n    fmtUnit = narrow\n      ? singular\n        ? lilUnits[1]\n        : lilUnits[2] || lilUnits[1]\n      : singular\n      ? units[unit][0]\n      : unit;\n  return isInPast ? `${fmtValue} ${fmtUnit} ago` : `in ${fmtValue} ${fmtUnit}`;\n}\n\nexport function formatString(knownFormat) {\n  // these all have the offsets removed because we don't have access to them\n  // without all the intl stuff this is backfilling\n  const filtered = pick(knownFormat, [\n      \"weekday\",\n      \"era\",\n      \"year\",\n      \"month\",\n      \"day\",\n      \"hour\",\n      \"minute\",\n      \"second\",\n      \"timeZoneName\",\n      \"hourCycle\",\n    ]),\n    key = stringify(filtered),\n    dateTimeHuge = \"EEEE, LLLL d, yyyy, h:mm a\";\n  switch (key) {\n    case stringify(Formats.DATE_SHORT):\n      return \"M/d/yyyy\";\n    case stringify(Formats.DATE_MED):\n      return \"LLL d, yyyy\";\n    case stringify(Formats.DATE_MED_WITH_WEEKDAY):\n      return \"EEE, LLL d, yyyy\";\n    case stringify(Formats.DATE_FULL):\n      return \"LLLL d, yyyy\";\n    case stringify(Formats.DATE_HUGE):\n      return \"EEEE, LLLL d, yyyy\";\n    case stringify(Formats.TIME_SIMPLE):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_SECONDS):\n      return \"h:mm:ss a\";\n    case stringify(Formats.TIME_WITH_SHORT_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_LONG_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_24_SIMPLE):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_SECONDS):\n      return \"HH:mm:ss\";\n    case stringify(Formats.TIME_24_WITH_SHORT_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_LONG_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.DATETIME_SHORT):\n      return \"M/d/yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_MED):\n      return \"LLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL):\n      return \"LLLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_HUGE):\n      return dateTimeHuge;\n    case stringify(Formats.DATETIME_SHORT_WITH_SECONDS):\n      return \"M/d/yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_SECONDS):\n      return \"LLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_WEEKDAY):\n      return \"EEE, d LLL yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL_WITH_SECONDS):\n      return \"LLLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_HUGE_WITH_SECONDS):\n      return \"EEEE, LLLL d, yyyy, h:mm:ss a\";\n    default:\n      return dateTimeHuge;\n  }\n}\n", "import * as English from \"./english.js\";\nimport * as Formats from \"./formats.js\";\nimport { padStart } from \"./util.js\";\n\nfunction stringifyTokens(splits, tokenToString) {\n  let s = \"\";\n  for (const token of splits) {\n    if (token.literal) {\n      s += token.val;\n    } else {\n      s += tokenToString(token.val);\n    }\n  }\n  return s;\n}\n\nconst macroTokenToFormatOpts = {\n  D: Formats.DATE_SHORT,\n  DD: Formats.DATE_MED,\n  DDD: Formats.DATE_FULL,\n  DDDD: Formats.DATE_HUGE,\n  t: Formats.TIME_SIMPLE,\n  tt: Formats.TIME_WITH_SECONDS,\n  ttt: Formats.TIME_WITH_SHORT_OFFSET,\n  tttt: Formats.TIME_WITH_LONG_OFFSET,\n  T: Formats.TIME_24_SIMPLE,\n  TT: Formats.TIME_24_WITH_SECONDS,\n  TTT: Formats.TIME_24_WITH_SHORT_OFFSET,\n  TTTT: Formats.TIME_24_WITH_LONG_OFFSET,\n  f: Formats.DATETIME_SHORT,\n  ff: Formats.DATETIME_MED,\n  fff: Formats.DATETIME_FULL,\n  ffff: Formats.DATETIME_HUGE,\n  F: Formats.DATETIME_SHORT_WITH_SECONDS,\n  FF: Formats.DATETIME_MED_WITH_SECONDS,\n  FFF: Formats.DATETIME_FULL_WITH_SECONDS,\n  FFFF: Formats.DATETIME_HUGE_WITH_SECONDS,\n};\n\n/**\n * @private\n */\n\nexport default class Formatter {\n  static create(locale, opts = {}) {\n    return new Formatter(locale, opts);\n  }\n\n  static parseFormat(fmt) {\n    // white-space is always considered a literal in user-provided formats\n    // the \" \" token has a special meaning (see unitForToken)\n\n    let current = null,\n      currentFull = \"\",\n      bracketed = false;\n    const splits = [];\n    for (let i = 0; i < fmt.length; i++) {\n      const c = fmt.charAt(i);\n      if (c === \"'\") {\n        // turn '' into a literal signal quote instead of just skipping the empty literal\n        if (currentFull.length > 0 || bracketed) {\n          splits.push({\n            literal: bracketed || /^\\s+$/.test(currentFull),\n            val: currentFull === \"\" ? \"'\" : currentFull,\n          });\n        }\n        current = null;\n        currentFull = \"\";\n        bracketed = !bracketed;\n      } else if (bracketed) {\n        currentFull += c;\n      } else if (c === current) {\n        currentFull += c;\n      } else {\n        if (currentFull.length > 0) {\n          splits.push({ literal: /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        currentFull = c;\n        current = c;\n      }\n    }\n\n    if (currentFull.length > 0) {\n      splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n    }\n\n    return splits;\n  }\n\n  static macroTokenToFormatOpts(token) {\n    return macroTokenToFormatOpts[token];\n  }\n\n  constructor(locale, formatOpts) {\n    this.opts = formatOpts;\n    this.loc = locale;\n    this.systemLoc = null;\n  }\n\n  formatWithSystemDefault(dt, opts) {\n    if (this.systemLoc === null) {\n      this.systemLoc = this.loc.redefaultToSystem();\n    }\n    const df = this.systemLoc.dtFormatter(dt, { ...this.opts, ...opts });\n    return df.format();\n  }\n\n  dtFormatter(dt, opts = {}) {\n    return this.loc.dtFormatter(dt, { ...this.opts, ...opts });\n  }\n\n  formatDateTime(dt, opts) {\n    return this.dtFormatter(dt, opts).format();\n  }\n\n  formatDateTimeParts(dt, opts) {\n    return this.dtFormatter(dt, opts).formatToParts();\n  }\n\n  formatInterval(interval, opts) {\n    const df = this.dtFormatter(interval.start, opts);\n    return df.dtf.formatRange(interval.start.toJSDate(), interval.end.toJSDate());\n  }\n\n  resolvedOptions(dt, opts) {\n    return this.dtFormatter(dt, opts).resolvedOptions();\n  }\n\n  num(n, p = 0, signDisplay = undefined) {\n    // we get some perf out of doing this here, annoyingly\n    if (this.opts.forceSimple) {\n      return padStart(n, p);\n    }\n\n    const opts = { ...this.opts };\n\n    if (p > 0) {\n      opts.padTo = p;\n    }\n    if (signDisplay) {\n      opts.signDisplay = signDisplay;\n    }\n\n    return this.loc.numberFormatter(opts).format(n);\n  }\n\n  formatDateTimeFromString(dt, fmt) {\n    const knownEnglish = this.loc.listingMode() === \"en\",\n      useDateTimeFormatter = this.loc.outputCalendar && this.loc.outputCalendar !== \"gregory\",\n      string = (opts, extract) => this.loc.extract(dt, opts, extract),\n      formatOffset = (opts) => {\n        if (dt.isOffsetFixed && dt.offset === 0 && opts.allowZ) {\n          return \"Z\";\n        }\n\n        return dt.isValid ? dt.zone.formatOffset(dt.ts, opts.format) : \"\";\n      },\n      meridiem = () =>\n        knownEnglish\n          ? English.meridiemForDateTime(dt)\n          : string({ hour: \"numeric\", hourCycle: \"h12\" }, \"dayperiod\"),\n      month = (length, standalone) =>\n        knownEnglish\n          ? English.monthForDateTime(dt, length)\n          : string(standalone ? { month: length } : { month: length, day: \"numeric\" }, \"month\"),\n      weekday = (length, standalone) =>\n        knownEnglish\n          ? English.weekdayForDateTime(dt, length)\n          : string(\n              standalone ? { weekday: length } : { weekday: length, month: \"long\", day: \"numeric\" },\n              \"weekday\"\n            ),\n      maybeMacro = (token) => {\n        const formatOpts = Formatter.macroTokenToFormatOpts(token);\n        if (formatOpts) {\n          return this.formatWithSystemDefault(dt, formatOpts);\n        } else {\n          return token;\n        }\n      },\n      era = (length) =>\n        knownEnglish ? English.eraForDateTime(dt, length) : string({ era: length }, \"era\"),\n      tokenToString = (token) => {\n        // Where possible: https://cldr.unicode.org/translation/date-time/date-time-symbols\n        switch (token) {\n          // ms\n          case \"S\":\n            return this.num(dt.millisecond);\n          case \"u\":\n          // falls through\n          case \"SSS\":\n            return this.num(dt.millisecond, 3);\n          // seconds\n          case \"s\":\n            return this.num(dt.second);\n          case \"ss\":\n            return this.num(dt.second, 2);\n          // fractional seconds\n          case \"uu\":\n            return this.num(Math.floor(dt.millisecond / 10), 2);\n          case \"uuu\":\n            return this.num(Math.floor(dt.millisecond / 100));\n          // minutes\n          case \"m\":\n            return this.num(dt.minute);\n          case \"mm\":\n            return this.num(dt.minute, 2);\n          // hours\n          case \"h\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12);\n          case \"hh\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12, 2);\n          case \"H\":\n            return this.num(dt.hour);\n          case \"HH\":\n            return this.num(dt.hour, 2);\n          // offset\n          case \"Z\":\n            // like +6\n            return formatOffset({ format: \"narrow\", allowZ: this.opts.allowZ });\n          case \"ZZ\":\n            // like +06:00\n            return formatOffset({ format: \"short\", allowZ: this.opts.allowZ });\n          case \"ZZZ\":\n            // like +0600\n            return formatOffset({ format: \"techie\", allowZ: this.opts.allowZ });\n          case \"ZZZZ\":\n            // like EST\n            return dt.zone.offsetName(dt.ts, { format: \"short\", locale: this.loc.locale });\n          case \"ZZZZZ\":\n            // like Eastern Standard Time\n            return dt.zone.offsetName(dt.ts, { format: \"long\", locale: this.loc.locale });\n          // zone\n          case \"z\":\n            // like America/New_York\n            return dt.zoneName;\n          // meridiems\n          case \"a\":\n            return meridiem();\n          // dates\n          case \"d\":\n            return useDateTimeFormatter ? string({ day: \"numeric\" }, \"day\") : this.num(dt.day);\n          case \"dd\":\n            return useDateTimeFormatter ? string({ day: \"2-digit\" }, \"day\") : this.num(dt.day, 2);\n          // weekdays - standalone\n          case \"c\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"ccc\":\n            // like 'Tues'\n            return weekday(\"short\", true);\n          case \"cccc\":\n            // like 'Tuesday'\n            return weekday(\"long\", true);\n          case \"ccccc\":\n            // like 'T'\n            return weekday(\"narrow\", true);\n          // weekdays - format\n          case \"E\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"EEE\":\n            // like 'Tues'\n            return weekday(\"short\", false);\n          case \"EEEE\":\n            // like 'Tuesday'\n            return weekday(\"long\", false);\n          case \"EEEEE\":\n            // like 'T'\n            return weekday(\"narrow\", false);\n          // months - standalone\n          case \"L\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"LL\":\n            // like 01, doesn't seem to work\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"LLL\":\n            // like Jan\n            return month(\"short\", true);\n          case \"LLLL\":\n            // like January\n            return month(\"long\", true);\n          case \"LLLLL\":\n            // like J\n            return month(\"narrow\", true);\n          // months - format\n          case \"M\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"MM\":\n            // like 01\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"MMM\":\n            // like Jan\n            return month(\"short\", false);\n          case \"MMMM\":\n            // like January\n            return month(\"long\", false);\n          case \"MMMMM\":\n            // like J\n            return month(\"narrow\", false);\n          // years\n          case \"y\":\n            // like 2014\n            return useDateTimeFormatter ? string({ year: \"numeric\" }, \"year\") : this.num(dt.year);\n          case \"yy\":\n            // like 14\n            return useDateTimeFormatter\n              ? string({ year: \"2-digit\" }, \"year\")\n              : this.num(dt.year.toString().slice(-2), 2);\n          case \"yyyy\":\n            // like 0012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 4);\n          case \"yyyyyy\":\n            // like 000012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 6);\n          // eras\n          case \"G\":\n            // like AD\n            return era(\"short\");\n          case \"GG\":\n            // like Anno Domini\n            return era(\"long\");\n          case \"GGGGG\":\n            return era(\"narrow\");\n          case \"kk\":\n            return this.num(dt.weekYear.toString().slice(-2), 2);\n          case \"kkkk\":\n            return this.num(dt.weekYear, 4);\n          case \"W\":\n            return this.num(dt.weekNumber);\n          case \"WW\":\n            return this.num(dt.weekNumber, 2);\n          case \"n\":\n            return this.num(dt.localWeekNumber);\n          case \"nn\":\n            return this.num(dt.localWeekNumber, 2);\n          case \"ii\":\n            return this.num(dt.localWeekYear.toString().slice(-2), 2);\n          case \"iiii\":\n            return this.num(dt.localWeekYear, 4);\n          case \"o\":\n            return this.num(dt.ordinal);\n          case \"ooo\":\n            return this.num(dt.ordinal, 3);\n          case \"q\":\n            // like 1\n            return this.num(dt.quarter);\n          case \"qq\":\n            // like 01\n            return this.num(dt.quarter, 2);\n          case \"X\":\n            return this.num(Math.floor(dt.ts / 1000));\n          case \"x\":\n            return this.num(dt.ts);\n          default:\n            return maybeMacro(token);\n        }\n      };\n\n    return stringifyTokens(Formatter.parseFormat(fmt), tokenToString);\n  }\n\n  formatDurationFromString(dur, fmt) {\n    const invertLargest = this.opts.signMode === \"negativeLargestOnly\" ? -1 : 1;\n    const tokenToField = (token) => {\n        switch (token[0]) {\n          case \"S\":\n            return \"milliseconds\";\n          case \"s\":\n            return \"seconds\";\n          case \"m\":\n            return \"minutes\";\n          case \"h\":\n            return \"hours\";\n          case \"d\":\n            return \"days\";\n          case \"w\":\n            return \"weeks\";\n          case \"M\":\n            return \"months\";\n          case \"y\":\n            return \"years\";\n          default:\n            return null;\n        }\n      },\n      tokenToString = (lildur, info) => (token) => {\n        const mapped = tokenToField(token);\n        if (mapped) {\n          const inversionFactor =\n            info.isNegativeDuration && mapped !== info.largestUnit ? invertLargest : 1;\n          let signDisplay;\n          if (this.opts.signMode === \"negativeLargestOnly\" && mapped !== info.largestUnit) {\n            signDisplay = \"never\";\n          } else if (this.opts.signMode === \"all\") {\n            signDisplay = \"always\";\n          } else {\n            // \"auto\" and \"negative\" are the same, but \"auto\" has better support\n            signDisplay = \"auto\";\n          }\n          return this.num(lildur.get(mapped) * inversionFactor, token.length, signDisplay);\n        } else {\n          return token;\n        }\n      },\n      tokens = Formatter.parseFormat(fmt),\n      realTokens = tokens.reduce(\n        (found, { literal, val }) => (literal ? found : found.concat(val)),\n        []\n      ),\n      collapsed = dur.shiftTo(...realTokens.map(tokenToField).filter((t) => t)),\n      durationInfo = {\n        isNegativeDuration: collapsed < 0,\n        // this relies on \"collapsed\" being based on \"shiftTo\", which builds up the object\n        // in order\n        largestUnit: Object.keys(collapsed.values)[0],\n      };\n    return stringifyTokens(tokens, tokenToString(collapsed, durationInfo));\n  }\n}\n", "import {\n  untruncate<PERSON>ear,\n  signed<PERSON>ffset,\n  parseInteger,\n  parse<PERSON>illis,\n  isUndefined,\n  parseFloating,\n} from \"./util.js\";\nimport * as English from \"./english.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n/*\n * This file handles parsing for well-specified formats. Here's how it works:\n * Two things go into parsing: a regex to match with and an extractor to take apart the groups in the match.\n * An extractor is just a function that takes a regex match array and returns a { year: ..., month: ... } object\n * parse() does the work of executing the regex and applying the extractor. It takes multiple regex/extractor pairs to try in sequence.\n * Extractors can take a \"cursor\" representing the offset in the match to look at. This makes it easy to combine extractors.\n * combineExtractors() does the work of combining them, keeping track of the cursor through multiple extractions.\n * Some extractions are super dumb and simpleParse and fromStrings help DRY them.\n */\n\nconst ianaRegex = /[A-Za-z_+-]{1,256}(?::?\\/[A-Za-z0-9_+-]{1,256}(?:\\/[A-Za-z0-9_+-]{1,256})?)?/;\n\nfunction combineRegexes(...regexes) {\n  const full = regexes.reduce((f, r) => f + r.source, \"\");\n  return RegExp(`^${full}$`);\n}\n\nfunction combineExtractors(...extractors) {\n  return (m) =>\n    extractors\n      .reduce(\n        ([mergedVals, mergedZone, cursor], ex) => {\n          const [val, zone, next] = ex(m, cursor);\n          return [{ ...mergedVals, ...val }, zone || mergedZone, next];\n        },\n        [{}, null, 1]\n      )\n      .slice(0, 2);\n}\n\nfunction parse(s, ...patterns) {\n  if (s == null) {\n    return [null, null];\n  }\n\n  for (const [regex, extractor] of patterns) {\n    const m = regex.exec(s);\n    if (m) {\n      return extractor(m);\n    }\n  }\n  return [null, null];\n}\n\nfunction simpleParse(...keys) {\n  return (match, cursor) => {\n    const ret = {};\n    let i;\n\n    for (i = 0; i < keys.length; i++) {\n      ret[keys[i]] = parseInteger(match[cursor + i]);\n    }\n    return [ret, null, cursor + i];\n  };\n}\n\n// ISO and SQL parsing\nconst offsetRegex = /(?:([Zz])|([+-]\\d\\d)(?::?(\\d\\d))?)/;\nconst isoExtendedZone = `(?:${offsetRegex.source}?(?:\\\\[(${ianaRegex.source})\\\\])?)?`;\nconst isoTimeBaseRegex = /(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:[.,](\\d{1,30}))?)?)?/;\nconst isoTimeRegex = RegExp(`${isoTimeBaseRegex.source}${isoExtendedZone}`);\nconst isoTimeExtensionRegex = RegExp(`(?:[Tt]${isoTimeRegex.source})?`);\nconst isoYmdRegex = /([+-]\\d{6}|\\d{4})(?:-?(\\d\\d)(?:-?(\\d\\d))?)?/;\nconst isoWeekRegex = /(\\d{4})-?W(\\d\\d)(?:-?(\\d))?/;\nconst isoOrdinalRegex = /(\\d{4})-?(\\d{3})/;\nconst extractISOWeekData = simpleParse(\"weekYear\", \"weekNumber\", \"weekDay\");\nconst extractISOOrdinalData = simpleParse(\"year\", \"ordinal\");\nconst sqlYmdRegex = /(\\d{4})-(\\d\\d)-(\\d\\d)/; // dumbed-down version of the ISO one\nconst sqlTimeRegex = RegExp(\n  `${isoTimeBaseRegex.source} ?(?:${offsetRegex.source}|(${ianaRegex.source}))?`\n);\nconst sqlTimeExtensionRegex = RegExp(`(?: ${sqlTimeRegex.source})?`);\n\nfunction int(match, pos, fallback) {\n  const m = match[pos];\n  return isUndefined(m) ? fallback : parseInteger(m);\n}\n\nfunction extractISOYmd(match, cursor) {\n  const item = {\n    year: int(match, cursor),\n    month: int(match, cursor + 1, 1),\n    day: int(match, cursor + 2, 1),\n  };\n\n  return [item, null, cursor + 3];\n}\n\nfunction extractISOTime(match, cursor) {\n  const item = {\n    hours: int(match, cursor, 0),\n    minutes: int(match, cursor + 1, 0),\n    seconds: int(match, cursor + 2, 0),\n    milliseconds: parseMillis(match[cursor + 3]),\n  };\n\n  return [item, null, cursor + 4];\n}\n\nfunction extractISOOffset(match, cursor) {\n  const local = !match[cursor] && !match[cursor + 1],\n    fullOffset = signedOffset(match[cursor + 1], match[cursor + 2]),\n    zone = local ? null : FixedOffsetZone.instance(fullOffset);\n  return [{}, zone, cursor + 3];\n}\n\nfunction extractIANAZone(match, cursor) {\n  const zone = match[cursor] ? IANAZone.create(match[cursor]) : null;\n  return [{}, zone, cursor + 1];\n}\n\n// ISO time parsing\n\nconst isoTimeOnly = RegExp(`^T?${isoTimeBaseRegex.source}$`);\n\n// ISO duration parsing\n\nconst isoDuration =\n  /^-?P(?:(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)Y)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)W)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)D)?(?:T(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)H)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20})(?:[.,](-?\\d{1,20}))?S)?)?)$/;\n\nfunction extractISODuration(match) {\n  const [s, yearStr, monthStr, weekStr, dayStr, hourStr, minuteStr, secondStr, millisecondsStr] =\n    match;\n\n  const hasNegativePrefix = s[0] === \"-\";\n  const negativeSeconds = secondStr && secondStr[0] === \"-\";\n\n  const maybeNegate = (num, force = false) =>\n    num !== undefined && (force || (num && hasNegativePrefix)) ? -num : num;\n\n  return [\n    {\n      years: maybeNegate(parseFloating(yearStr)),\n      months: maybeNegate(parseFloating(monthStr)),\n      weeks: maybeNegate(parseFloating(weekStr)),\n      days: maybeNegate(parseFloating(dayStr)),\n      hours: maybeNegate(parseFloating(hourStr)),\n      minutes: maybeNegate(parseFloating(minuteStr)),\n      seconds: maybeNegate(parseFloating(secondStr), secondStr === \"-0\"),\n      milliseconds: maybeNegate(parseMillis(millisecondsStr), negativeSeconds),\n    },\n  ];\n}\n\n// These are a little braindead. EDT *should* tell us that we're in, say, America/New_York\n// and not just that we're in -240 *right now*. But since I don't think these are used that often\n// I'm just going to ignore that\nconst obsOffsets = {\n  GMT: 0,\n  EDT: -4 * 60,\n  EST: -5 * 60,\n  CDT: -5 * 60,\n  CST: -6 * 60,\n  MDT: -6 * 60,\n  MST: -7 * 60,\n  PDT: -7 * 60,\n  PST: -8 * 60,\n};\n\nfunction fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\n  const result = {\n    year: yearStr.length === 2 ? untruncateYear(parseInteger(yearStr)) : parseInteger(yearStr),\n    month: English.monthsShort.indexOf(monthStr) + 1,\n    day: parseInteger(dayStr),\n    hour: parseInteger(hourStr),\n    minute: parseInteger(minuteStr),\n  };\n\n  if (secondStr) result.second = parseInteger(secondStr);\n  if (weekdayStr) {\n    result.weekday =\n      weekdayStr.length > 3\n        ? English.weekdaysLong.indexOf(weekdayStr) + 1\n        : English.weekdaysShort.indexOf(weekdayStr) + 1;\n  }\n\n  return result;\n}\n\n// RFC 2822/5322\nconst rfc2822 =\n  /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\\d\\d)(\\d\\d)))$/;\n\nfunction extractRFC2822(match) {\n  const [\n      ,\n      weekdayStr,\n      dayStr,\n      monthStr,\n      yearStr,\n      hourStr,\n      minuteStr,\n      secondStr,\n      obsOffset,\n      milOffset,\n      offHourStr,\n      offMinuteStr,\n    ] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n\n  let offset;\n  if (obsOffset) {\n    offset = obsOffsets[obsOffset];\n  } else if (milOffset) {\n    offset = 0;\n  } else {\n    offset = signedOffset(offHourStr, offMinuteStr);\n  }\n\n  return [result, new FixedOffsetZone(offset)];\n}\n\nfunction preprocessRFC2822(s) {\n  // Remove comments and folding whitespace and replace multiple-spaces with a single space\n  return s\n    .replace(/\\([^()]*\\)|[\\n\\t]/g, \" \")\n    .replace(/(\\s\\s+)/g, \" \")\n    .trim();\n}\n\n// http date\n\nconst rfc1123 =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\\d\\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\\d{4}) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  rfc850 =\n    /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\\d\\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  ascii =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \\d|\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) (\\d{4})$/;\n\nfunction extractRFC1123Or850(match) {\n  const [, weekdayStr, dayStr, monthStr, yearStr, hourStr, minuteStr, secondStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nfunction extractASCII(match) {\n  const [, weekdayStr, monthStr, dayStr, hourStr, minuteStr, secondStr, yearStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nconst isoYmdWithTimeExtensionRegex = combineRegexes(isoYmdRegex, isoTimeExtensionRegex);\nconst isoWeekWithTimeExtensionRegex = combineRegexes(isoWeekRegex, isoTimeExtensionRegex);\nconst isoOrdinalWithTimeExtensionRegex = combineRegexes(isoOrdinalRegex, isoTimeExtensionRegex);\nconst isoTimeCombinedRegex = combineRegexes(isoTimeRegex);\n\nconst extractISOYmdTimeAndOffset = combineExtractors(\n  extractISOYmd,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOWeekTimeAndOffset = combineExtractors(\n  extractISOWeekData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOOrdinalDateAndTime = combineExtractors(\n  extractISOOrdinalData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOTimeAndOffset = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\n/*\n * @private\n */\n\nexport function parseISODate(s) {\n  return parse(\n    s,\n    [isoYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [isoWeekWithTimeExtensionRegex, extractISOWeekTimeAndOffset],\n    [isoOrdinalWithTimeExtensionRegex, extractISOOrdinalDateAndTime],\n    [isoTimeCombinedRegex, extractISOTimeAndOffset]\n  );\n}\n\nexport function parseRFC2822Date(s) {\n  return parse(preprocessRFC2822(s), [rfc2822, extractRFC2822]);\n}\n\nexport function parseHTTPDate(s) {\n  return parse(\n    s,\n    [rfc1123, extractRFC1123Or850],\n    [rfc850, extractRFC1123Or850],\n    [ascii, extractASCII]\n  );\n}\n\nexport function parseISODuration(s) {\n  return parse(s, [isoDuration, extractISODuration]);\n}\n\nconst extractISOTimeOnly = combineExtractors(extractISOTime);\n\nexport function parseISOTimeOnly(s) {\n  return parse(s, [isoTimeOnly, extractISOTimeOnly]);\n}\n\nconst sqlYmdWithTimeExtensionRegex = combineRegexes(sqlYmdRegex, sqlTimeExtensionRegex);\nconst sqlTimeCombinedRegex = combineRegexes(sqlTimeRegex);\n\nconst extractISOTimeOffsetAndIANAZone = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\nexport function parseSQL(s) {\n  return parse(\n    s,\n    [sqlYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [sqlTimeCombinedRegex, extractISOTimeOffsetAndIANAZone]\n  );\n}\n", "import { InvalidArgumentError, InvalidDurationError, InvalidUnitError } from \"./errors.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Locale from \"./impl/locale.js\";\nimport { parseISODuration, parseISOTimeOnly } from \"./impl/regexParser.js\";\nimport {\n  asNumber,\n  hasOwnProperty,\n  isNumber,\n  isUndefined,\n  normalizeObject,\n  roundTo,\n} from \"./impl/util.js\";\nimport Settings from \"./settings.js\";\nimport DateTime from \"./datetime.js\";\n\nconst INVALID = \"Invalid Duration\";\n\n// unit conversion constants\nexport const lowOrderMatrix = {\n    weeks: {\n      days: 7,\n      hours: 7 * 24,\n      minutes: 7 * 24 * 60,\n      seconds: 7 * 24 * 60 * 60,\n      milliseconds: 7 * 24 * 60 * 60 * 1000,\n    },\n    days: {\n      hours: 24,\n      minutes: 24 * 60,\n      seconds: 24 * 60 * 60,\n      milliseconds: 24 * 60 * 60 * 1000,\n    },\n    hours: { minutes: 60, seconds: 60 * 60, milliseconds: 60 * 60 * 1000 },\n    minutes: { seconds: 60, milliseconds: 60 * 1000 },\n    seconds: { milliseconds: 1000 },\n  },\n  casualMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: 52,\n      days: 365,\n      hours: 365 * 24,\n      minutes: 365 * 24 * 60,\n      seconds: 365 * 24 * 60 * 60,\n      milliseconds: 365 * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: 13,\n      days: 91,\n      hours: 91 * 24,\n      minutes: 91 * 24 * 60,\n      seconds: 91 * 24 * 60 * 60,\n      milliseconds: 91 * 24 * 60 * 60 * 1000,\n    },\n    months: {\n      weeks: 4,\n      days: 30,\n      hours: 30 * 24,\n      minutes: 30 * 24 * 60,\n      seconds: 30 * 24 * 60 * 60,\n      milliseconds: 30 * 24 * 60 * 60 * 1000,\n    },\n\n    ...lowOrderMatrix,\n  },\n  daysInYearAccurate = 146097.0 / 400,\n  daysInMonthAccurate = 146097.0 / 4800,\n  accurateMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: daysInYearAccurate / 7,\n      days: daysInYearAccurate,\n      hours: daysInYearAccurate * 24,\n      minutes: daysInYearAccurate * 24 * 60,\n      seconds: daysInYearAccurate * 24 * 60 * 60,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: daysInYearAccurate / 28,\n      days: daysInYearAccurate / 4,\n      hours: (daysInYearAccurate * 24) / 4,\n      minutes: (daysInYearAccurate * 24 * 60) / 4,\n      seconds: (daysInYearAccurate * 24 * 60 * 60) / 4,\n      milliseconds: (daysInYearAccurate * 24 * 60 * 60 * 1000) / 4,\n    },\n    months: {\n      weeks: daysInMonthAccurate / 7,\n      days: daysInMonthAccurate,\n      hours: daysInMonthAccurate * 24,\n      minutes: daysInMonthAccurate * 24 * 60,\n      seconds: daysInMonthAccurate * 24 * 60 * 60,\n      milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1000,\n    },\n    ...lowOrderMatrix,\n  };\n\n// units ordered by size\nconst orderedUnits = [\n  \"years\",\n  \"quarters\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n  \"milliseconds\",\n];\n\nconst reverseUnits = orderedUnits.slice(0).reverse();\n\n// clone really means \"create another instance just like this one, but with these changes\"\nfunction clone(dur, alts, clear = false) {\n  // deep merge for vals\n  const conf = {\n    values: clear ? alts.values : { ...dur.values, ...(alts.values || {}) },\n    loc: dur.loc.clone(alts.loc),\n    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,\n    matrix: alts.matrix || dur.matrix,\n  };\n  return new Duration(conf);\n}\n\nfunction durationToMillis(matrix, vals) {\n  let sum = vals.milliseconds ?? 0;\n  for (const unit of reverseUnits.slice(1)) {\n    if (vals[unit]) {\n      sum += vals[unit] * matrix[unit][\"milliseconds\"];\n    }\n  }\n  return sum;\n}\n\n// NB: mutates parameters\nfunction normalizeValues(matrix, vals) {\n  // the logic below assumes the overall value of the duration is positive\n  // if this is not the case, factor is used to make it so\n  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;\n\n  orderedUnits.reduceRight((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const previousVal = vals[previous] * factor;\n        const conv = matrix[current][previous];\n\n        // if (previousVal < 0):\n        // lower order unit is negative (e.g. { years: 2, days: -2 })\n        // normalize this by reducing the higher order unit by the appropriate amount\n        // and increasing the lower order unit\n        // this can never make the higher order unit negative, because this function only operates\n        // on positive durations, so the amount of time represented by the lower order unit cannot\n        // be larger than the higher order unit\n        // else:\n        // lower order unit is positive (e.g. { years: 2, days: 450 } or { years: -2, days: 450 })\n        // in this case we attempt to convert as much as possible from the lower order unit into\n        // the higher order one\n        //\n        // Math.floor takes care of both of these cases, rounding away from 0\n        // if previousVal < 0 it makes the absolute value larger\n        // if previousVal >= it makes the absolute value smaller\n        const rollUp = Math.floor(previousVal / conv);\n        vals[current] += rollUp * factor;\n        vals[previous] -= rollUp * conv * factor;\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n\n  // try to convert any decimals into smaller units if possible\n  // for example for { years: 2.5, days: 0, seconds: 0 } we want to get { years: 2, days: 182, hours: 12 }\n  orderedUnits.reduce((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const fraction = vals[previous] % 1;\n        vals[previous] -= fraction;\n        vals[current] += fraction * matrix[previous][current];\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n}\n\n// Remove all properties with a value of 0 from an object\nfunction removeZeroes(vals) {\n  const newVals = {};\n  for (const [key, value] of Object.entries(vals)) {\n    if (value !== 0) {\n      newVals[key] = value;\n    }\n  }\n  return newVals;\n}\n\n/**\n * A Duration object represents a period of time, like \"2 months\" or \"1 day, 1 hour\". Conceptually, it's just a map of units to their quantities, accompanied by some additional configuration and methods for creating, parsing, interrogating, transforming, and formatting them. They can be used on their own or in conjunction with other Luxon types; for example, you can use {@link DateTime#plus} to add a Duration object to a DateTime, producing another DateTime.\n *\n * Here is a brief overview of commonly used methods and getters in Duration:\n *\n * * **Creation** To create a Duration, use {@link Duration.fromMillis}, {@link Duration.fromObject}, or {@link Duration.fromISO}.\n * * **Unit values** See the {@link Duration#years}, {@link Duration#months}, {@link Duration#weeks}, {@link Duration#days}, {@link Duration#hours}, {@link Duration#minutes}, {@link Duration#seconds}, {@link Duration#milliseconds} accessors.\n * * **Configuration** See  {@link Duration#locale} and {@link Duration#numberingSystem} accessors.\n * * **Transformation** To create new Durations out of old ones use {@link Duration#plus}, {@link Duration#minus}, {@link Duration#normalize}, {@link Duration#set}, {@link Duration#reconfigure}, {@link Duration#shiftTo}, and {@link Duration#negate}.\n * * **Output** To convert the Duration into other representations, see {@link Duration#as}, {@link Duration#toISO}, {@link Duration#toFormat}, and {@link Duration#toJSON}\n *\n * There's are more methods documented below. In addition, for more information on subtler topics like internationalization and validity, see the external documentation.\n */\nexport default class Duration {\n  /**\n   * @private\n   */\n  constructor(config) {\n    const accurate = config.conversionAccuracy === \"longterm\" || false;\n    let matrix = accurate ? accurateMatrix : casualMatrix;\n\n    if (config.matrix) {\n      matrix = config.matrix;\n    }\n\n    /**\n     * @access private\n     */\n    this.values = config.values;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.conversionAccuracy = accurate ? \"longterm\" : \"casual\";\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.matrix = matrix;\n    /**\n     * @access private\n     */\n    this.isLuxonDuration = true;\n  }\n\n  /**\n   * Create Duration from a number of milliseconds.\n   * @param {number} count of milliseconds\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  static fromMillis(count, opts) {\n    return Duration.fromObject({ milliseconds: count }, opts);\n  }\n\n  /**\n   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.\n   * If this object is empty then a zero milliseconds duration is returned.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.years\n   * @param {number} obj.quarters\n   * @param {number} obj.months\n   * @param {number} obj.weeks\n   * @param {number} obj.days\n   * @param {number} obj.hours\n   * @param {number} obj.minutes\n   * @param {number} obj.seconds\n   * @param {number} obj.milliseconds\n   * @param {Object} [opts=[]] - options for creating this Duration\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the custom conversion system to use\n   * @return {Duration}\n   */\n  static fromObject(obj, opts = {}) {\n    if (obj == null || typeof obj !== \"object\") {\n      throw new InvalidArgumentError(\n        `Duration.fromObject: argument expected to be an object, got ${\n          obj === null ? \"null\" : typeof obj\n        }`\n      );\n    }\n\n    return new Duration({\n      values: normalizeObject(obj, Duration.normalizeUnit),\n      loc: Locale.fromObject(opts),\n      conversionAccuracy: opts.conversionAccuracy,\n      matrix: opts.matrix,\n    });\n  }\n\n  /**\n   * Create a Duration from DurationLike.\n   *\n   * @param {Object | number | Duration} durationLike\n   * One of:\n   * - object with keys like 'years' and 'hours'.\n   * - number representing milliseconds\n   * - Duration instance\n   * @return {Duration}\n   */\n  static fromDurationLike(durationLike) {\n    if (isNumber(durationLike)) {\n      return Duration.fromMillis(durationLike);\n    } else if (Duration.isDuration(durationLike)) {\n      return durationLike;\n    } else if (typeof durationLike === \"object\") {\n      return Duration.fromObject(durationLike);\n    } else {\n      throw new InvalidArgumentError(\n        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`\n      );\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 duration string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the preset conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }\n   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }\n   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }\n   * @return {Duration}\n   */\n  static fromISO(text, opts) {\n    const [parsed] = parseISODuration(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 time string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }\n   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @return {Duration}\n   */\n  static fromISOTime(text, opts) {\n    const [parsed] = parseISOTimeOnly(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create an invalid Duration.\n   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Duration}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Duration is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDurationError(invalid);\n    } else {\n      return new Duration({ invalid });\n    }\n  }\n\n  /**\n   * @private\n   */\n  static normalizeUnit(unit) {\n    const normalized = {\n      year: \"years\",\n      years: \"years\",\n      quarter: \"quarters\",\n      quarters: \"quarters\",\n      month: \"months\",\n      months: \"months\",\n      week: \"weeks\",\n      weeks: \"weeks\",\n      day: \"days\",\n      days: \"days\",\n      hour: \"hours\",\n      hours: \"hours\",\n      minute: \"minutes\",\n      minutes: \"minutes\",\n      second: \"seconds\",\n      seconds: \"seconds\",\n      millisecond: \"milliseconds\",\n      milliseconds: \"milliseconds\",\n    }[unit ? unit.toLowerCase() : unit];\n\n    if (!normalized) throw new InvalidUnitError(unit);\n\n    return normalized;\n  }\n\n  /**\n   * Check if an object is a Duration. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDuration(o) {\n    return (o && o.isLuxonDuration) || false;\n  }\n\n  /**\n   * Get  the locale of a Duration, such 'en-GB'\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:\n   * * `S` for milliseconds\n   * * `s` for seconds\n   * * `m` for minutes\n   * * `h` for hours\n   * * `d` for days\n   * * `w` for weeks\n   * * `M` for months\n   * * `y` for years\n   * Notes:\n   * * Add padding by repeating the token, e.g. \"yy\" pads the years to two digits, \"hhhh\" pads the hours out to four digits\n   * * Tokens can be escaped by wrapping with single quotes.\n   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - options\n   * @param {boolean} [opts.floor=true] - floor numerical values\n   * @param {'negative'|'all'|'negativeLargestOnly'} [opts.signMode=negative] - How to handle signs\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"y d s\") //=> \"1 6 2\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"yy dd sss\") //=> \"01 06 002\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"M S\") //=> \"12 518402000\"\n   * @example Duration.fromObject({ days: 6, seconds: 2 }).toFormat(\"d s\", { signMode: \"all\" }) //=> \"+6 +2\"\n   * @example Duration.fromObject({ days: -6, seconds: -2 }).toFormat(\"d s\", { signMode: \"all\" }) //=> \"-6 -2\"\n   * @example Duration.fromObject({ days: -6, seconds: -2 }).toFormat(\"d s\", { signMode: \"negativeLargestOnly\" }) //=> \"-6 2\"\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    // reverse-compat since 1.2; we always round down now, never up, and we do it by default\n    const fmtOpts = {\n      ...opts,\n      floor: opts.round !== false && opts.floor !== false,\n    };\n    return this.isValid\n      ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a string representation of a Duration with all units included.\n   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options\n   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.\n   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.\n   * @param {boolean} [opts.showZeros=true] - Show all units previously used by the duration even if they are zero\n   * @example\n   * ```js\n   * var dur = Duration.fromObject({ months: 1, weeks: 0, hours: 5, minutes: 6 })\n   * dur.toHuman() //=> '1 month, 0 weeks, 5 hours, 6 minutes'\n   * dur.toHuman({ listStyle: \"long\" }) //=> '1 month, 0 weeks, 5 hours, and 6 minutes'\n   * dur.toHuman({ unitDisplay: \"short\" }) //=> '1 mth, 0 wks, 5 hr, 6 min'\n   * dur.toHuman({ showZeros: false }) //=> '1 month, 5 hours, 6 minutes'\n   * ```\n   */\n  toHuman(opts = {}) {\n    if (!this.isValid) return INVALID;\n\n    const showZeros = opts.showZeros !== false;\n\n    const l = orderedUnits\n      .map((unit) => {\n        const val = this.values[unit];\n        if (isUndefined(val) || (val === 0 && !showZeros)) {\n          return null;\n        }\n        return this.loc\n          .numberFormatter({ style: \"unit\", unitDisplay: \"long\", ...opts, unit: unit.slice(0, -1) })\n          .format(val);\n      })\n      .filter((n) => n);\n\n    return this.loc\n      .listFormatter({ type: \"conjunction\", style: opts.listStyle || \"narrow\", ...opts })\n      .format(l);\n  }\n\n  /**\n   * Returns a JavaScript object with this Duration's values.\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }\n   * @return {Object}\n   */\n  toObject() {\n    if (!this.isValid) return {};\n    return { ...this.values };\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'\n   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'\n   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'\n   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'\n   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'\n   * @return {string}\n   */\n  toISO() {\n    // we could use the formatter, but this is an easier way to get the minimum string\n    if (!this.isValid) return null;\n\n    let s = \"P\";\n    if (this.years !== 0) s += this.years + \"Y\";\n    if (this.months !== 0 || this.quarters !== 0) s += this.months + this.quarters * 3 + \"M\";\n    if (this.weeks !== 0) s += this.weeks + \"W\";\n    if (this.days !== 0) s += this.days + \"D\";\n    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0)\n      s += \"T\";\n    if (this.hours !== 0) s += this.hours + \"H\";\n    if (this.minutes !== 0) s += this.minutes + \"M\";\n    if (this.seconds !== 0 || this.milliseconds !== 0)\n      // this will handle \"floating point madness\" by removing extra decimal places\n      // https://stackoverflow.com/questions/588004/is-floating-point-math-broken\n      s += roundTo(this.seconds + this.milliseconds / 1000, 3) + \"S\";\n    if (s === \"P\") s += \"T0S\";\n    return s;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.\n   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'\n   * @return {string}\n   */\n  toISOTime(opts = {}) {\n    if (!this.isValid) return null;\n\n    const millis = this.toMillis();\n    if (millis < 0 || millis >= 86400000) return null;\n\n    opts = {\n      suppressMilliseconds: false,\n      suppressSeconds: false,\n      includePrefix: false,\n      format: \"extended\",\n      ...opts,\n      includeOffset: false,\n    };\n\n    const dateTime = DateTime.fromMillis(millis, { zone: \"UTC\" });\n    return dateTime.toISOTime(opts);\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.\n   * @return {string}\n   */\n  toString() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a string representation of this Duration appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Duration { values: ${JSON.stringify(this.values)} }`;\n    } else {\n      return `Duration { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration.\n   * @return {number}\n   */\n  toMillis() {\n    if (!this.isValid) return NaN;\n\n    return durationToMillis(this.matrix, this.values);\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration),\n      result = {};\n\n    for (const k of orderedUnits) {\n      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {\n        result[k] = dur.get(k) + this.get(k);\n      }\n    }\n\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration);\n    return this.plus(dur.negate());\n  }\n\n  /**\n   * Scale this Duration by the specified amount. Return a newly-constructed Duration.\n   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === \"hours\" ? x * 2 : x) //=> { hours: 2, minutes: 30 }\n   * @return {Duration}\n   */\n  mapUnits(fn) {\n    if (!this.isValid) return this;\n    const result = {};\n    for (const k of Object.keys(this.values)) {\n      result[k] = asNumber(fn(this.values[k], k));\n    }\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2\n   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0\n   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3\n   * @return {number}\n   */\n  get(unit) {\n    return this[Duration.normalizeUnit(unit)];\n  }\n\n  /**\n   * \"Set\" the values of specified units. Return a newly-constructed Duration.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dur.set({ years: 2017 })\n   * @example dur.set({ hours: 8, minutes: 30 })\n   * @return {Duration}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const mixed = { ...this.values, ...normalizeObject(values, Duration.normalizeUnit) };\n    return clone(this, { values: mixed });\n  }\n\n  /**\n   * \"Set\" the locale and/or numberingSystem.  Returns a newly-constructed Duration.\n   * @example dur.reconfigure({ locale: 'en-GB' })\n   * @return {Duration}\n   */\n  reconfigure({ locale, numberingSystem, conversionAccuracy, matrix } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem });\n    const opts = { loc, matrix, conversionAccuracy };\n    return clone(this, opts);\n  }\n\n  /**\n   * Return the length of the duration in the specified unit.\n   * @param {string} unit - a unit such as 'minutes' or 'days'\n   * @example Duration.fromObject({years: 1}).as('days') //=> 365\n   * @example Duration.fromObject({years: 1}).as('months') //=> 12\n   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5\n   * @return {number}\n   */\n  as(unit) {\n    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;\n  }\n\n  /**\n   * Reduce this Duration to its canonical representation in its current units.\n   * Assuming the overall value of the Duration is positive, this means:\n   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)\n   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise\n   *   the overall value would be negative, see third example)\n   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)\n   *\n   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.\n   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }\n   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }\n   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }\n   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }\n   * @return {Duration}\n   */\n  normalize() {\n    if (!this.isValid) return this;\n    const vals = this.toObject();\n    normalizeValues(this.matrix, vals);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Rescale units to its largest representation\n   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }\n   * @return {Duration}\n   */\n  rescale() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.normalize().shiftToAll().toObject());\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Convert this Duration into its representation in a different set of units.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }\n   * @return {Duration}\n   */\n  shiftTo(...units) {\n    if (!this.isValid) return this;\n\n    if (units.length === 0) {\n      return this;\n    }\n\n    units = units.map((u) => Duration.normalizeUnit(u));\n\n    const built = {},\n      accumulated = {},\n      vals = this.toObject();\n    let lastUnit;\n\n    for (const k of orderedUnits) {\n      if (units.indexOf(k) >= 0) {\n        lastUnit = k;\n\n        let own = 0;\n\n        // anything we haven't boiled down yet should get boiled to this unit\n        for (const ak in accumulated) {\n          own += this.matrix[ak][k] * accumulated[ak];\n          accumulated[ak] = 0;\n        }\n\n        // plus anything that's already in this unit\n        if (isNumber(vals[k])) {\n          own += vals[k];\n        }\n\n        // only keep the integer part for now in the hopes of putting any decimal part\n        // into a smaller unit later\n        const i = Math.trunc(own);\n        built[k] = i;\n        accumulated[k] = (own * 1000 - i * 1000) / 1000;\n\n        // otherwise, keep it in the wings to boil it later\n      } else if (isNumber(vals[k])) {\n        accumulated[k] = vals[k];\n      }\n    }\n\n    // anything leftover becomes the decimal for the last unit\n    // lastUnit must be defined since units is not empty\n    for (const key in accumulated) {\n      if (accumulated[key] !== 0) {\n        built[lastUnit] +=\n          key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];\n      }\n    }\n\n    normalizeValues(this.matrix, built);\n    return clone(this, { values: built }, true);\n  }\n\n  /**\n   * Shift this Duration to all available units.\n   * Same as shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\")\n   * @return {Duration}\n   */\n  shiftToAll() {\n    if (!this.isValid) return this;\n    return this.shiftTo(\n      \"years\",\n      \"months\",\n      \"weeks\",\n      \"days\",\n      \"hours\",\n      \"minutes\",\n      \"seconds\",\n      \"milliseconds\"\n    );\n  }\n\n  /**\n   * Return the negative of this Duration.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }\n   * @return {Duration}\n   */\n  negate() {\n    if (!this.isValid) return this;\n    const negated = {};\n    for (const k of Object.keys(this.values)) {\n      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];\n    }\n    return clone(this, { values: negated }, true);\n  }\n\n  /**\n   * Removes all units with values equal to 0 from this Duration.\n   * @example Duration.fromObject({ years: 2, days: 0, hours: 0, minutes: 0 }).removeZeros().toObject() //=> { years: 2 }\n   * @return {Duration}\n   */\n  removeZeros() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.values);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Get the years.\n   * @type {number}\n   */\n  get years() {\n    return this.isValid ? this.values.years || 0 : NaN;\n  }\n\n  /**\n   * Get the quarters.\n   * @type {number}\n   */\n  get quarters() {\n    return this.isValid ? this.values.quarters || 0 : NaN;\n  }\n\n  /**\n   * Get the months.\n   * @type {number}\n   */\n  get months() {\n    return this.isValid ? this.values.months || 0 : NaN;\n  }\n\n  /**\n   * Get the weeks\n   * @type {number}\n   */\n  get weeks() {\n    return this.isValid ? this.values.weeks || 0 : NaN;\n  }\n\n  /**\n   * Get the days.\n   * @type {number}\n   */\n  get days() {\n    return this.isValid ? this.values.days || 0 : NaN;\n  }\n\n  /**\n   * Get the hours.\n   * @type {number}\n   */\n  get hours() {\n    return this.isValid ? this.values.hours || 0 : NaN;\n  }\n\n  /**\n   * Get the minutes.\n   * @type {number}\n   */\n  get minutes() {\n    return this.isValid ? this.values.minutes || 0 : NaN;\n  }\n\n  /**\n   * Get the seconds.\n   * @return {number}\n   */\n  get seconds() {\n    return this.isValid ? this.values.seconds || 0 : NaN;\n  }\n\n  /**\n   * Get the milliseconds.\n   * @return {number}\n   */\n  get milliseconds() {\n    return this.isValid ? this.values.milliseconds || 0 : NaN;\n  }\n\n  /**\n   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations\n   * on invalid DateTimes or Intervals.\n   * @return {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this Duration became invalid, or null if the Duration is valid\n   * @return {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Equality check\n   * Two Durations are equal iff they have the same units and the same values for each unit.\n   * @param {Duration} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    if (!this.loc.equals(other.loc)) {\n      return false;\n    }\n\n    function eq(v1, v2) {\n      // Consider 0 and undefined as equal\n      if (v1 === undefined || v1 === 0) return v2 === undefined || v2 === 0;\n      return v1 === v2;\n    }\n\n    for (const u of orderedUnits) {\n      if (!eq(this.values[u], other.values[u])) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n", "import DateTime, { friendlyDateTime } from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Settings from \"./settings.js\";\nimport { InvalidArgumentError, InvalidIntervalError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport * as Formats from \"./impl/formats.js\";\n\nconst INVALID = \"Invalid Interval\";\n\n// checks if the start is equal to or before the end\nfunction validateStartEnd(start, end) {\n  if (!start || !start.isValid) {\n    return Interval.invalid(\"missing or invalid start\");\n  } else if (!end || !end.isValid) {\n    return Interval.invalid(\"missing or invalid end\");\n  } else if (end < start) {\n    return Interval.invalid(\n      \"end before start\",\n      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`\n    );\n  } else {\n    return null;\n  }\n}\n\n/**\n * An Interval object represents a half-open interval of time, where each endpoint is a {@link DateTime}. Conceptually, it's a container for those two endpoints, accompanied by methods for creating, parsing, interrogating, comparing, transforming, and formatting them.\n *\n * Here is a brief overview of the most commonly used methods and getters in Interval:\n *\n * * **Creation** To create an Interval, use {@link Interval.fromDateTimes}, {@link Interval.after}, {@link Interval.before}, or {@link Interval.fromISO}.\n * * **Accessors** Use {@link Interval#start} and {@link Interval#end} to get the start and end.\n * * **Interrogation** To analyze the Interval, use {@link Interval#count}, {@link Interval#length}, {@link Interval#hasSame}, {@link Interval#contains}, {@link Interval#isAfter}, or {@link Interval#isBefore}.\n * * **Transformation** To create other Intervals out of this one, use {@link Interval#set}, {@link Interval#splitAt}, {@link Interval#splitBy}, {@link Interval#divideEqually}, {@link Interval.merge}, {@link Interval.xor}, {@link Interval#union}, {@link Interval#intersection}, or {@link Interval#difference}.\n * * **Comparison** To compare this Interval to another one, use {@link Interval#equals}, {@link Interval#overlaps}, {@link Interval#abutsStart}, {@link Interval#abutsEnd}, {@link Interval#engulfs}\n * * **Output** To convert the Interval into other representations, see {@link Interval#toString}, {@link Interval#toLocaleString}, {@link Interval#toISO}, {@link Interval#toISODate}, {@link Interval#toISOTime}, {@link Interval#toFormat}, and {@link Interval#toDuration}.\n */\nexport default class Interval {\n  /**\n   * @private\n   */\n  constructor(config) {\n    /**\n     * @access private\n     */\n    this.s = config.start;\n    /**\n     * @access private\n     */\n    this.e = config.end;\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.isLuxonInterval = true;\n  }\n\n  /**\n   * Create an invalid Interval.\n   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Interval}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Interval is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidIntervalError(invalid);\n    } else {\n      return new Interval({ invalid });\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.\n   * @param {DateTime|Date|Object} start\n   * @param {DateTime|Date|Object} end\n   * @return {Interval}\n   */\n  static fromDateTimes(start, end) {\n    const builtStart = friendlyDateTime(start),\n      builtEnd = friendlyDateTime(end);\n\n    const validateError = validateStartEnd(builtStart, builtEnd);\n\n    if (validateError == null) {\n      return new Interval({\n        start: builtStart,\n        end: builtEnd,\n      });\n    } else {\n      return validateError;\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and a Duration to extend to.\n   * @param {DateTime|Date|Object} start\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static after(start, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(start);\n    return Interval.fromDateTimes(dt, dt.plus(dur));\n  }\n\n  /**\n   * Create an Interval from an end DateTime and a Duration to extend backwards to.\n   * @param {DateTime|Date|Object} end\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static before(end, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(end);\n    return Interval.fromDateTimes(dt.minus(dur), dt);\n  }\n\n  /**\n   * Create an Interval from an ISO 8601 string.\n   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.\n   * @param {string} text - the ISO string to parse\n   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {Interval}\n   */\n  static fromISO(text, opts) {\n    const [s, e] = (text || \"\").split(\"/\", 2);\n    if (s && e) {\n      let start, startIsValid;\n      try {\n        start = DateTime.fromISO(s, opts);\n        startIsValid = start.isValid;\n      } catch (e) {\n        startIsValid = false;\n      }\n\n      let end, endIsValid;\n      try {\n        end = DateTime.fromISO(e, opts);\n        endIsValid = end.isValid;\n      } catch (e) {\n        endIsValid = false;\n      }\n\n      if (startIsValid && endIsValid) {\n        return Interval.fromDateTimes(start, end);\n      }\n\n      if (startIsValid) {\n        const dur = Duration.fromISO(e, opts);\n        if (dur.isValid) {\n          return Interval.after(start, dur);\n        }\n      } else if (endIsValid) {\n        const dur = Duration.fromISO(s, opts);\n        if (dur.isValid) {\n          return Interval.before(end, dur);\n        }\n      }\n    }\n    return Interval.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n  }\n\n  /**\n   * Check if an object is an Interval. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isInterval(o) {\n    return (o && o.isLuxonInterval) || false;\n  }\n\n  /**\n   * Returns the start of the Interval\n   * @type {DateTime}\n   */\n  get start() {\n    return this.isValid ? this.s : null;\n  }\n\n  /**\n   * Returns the end of the Interval. This is the first instant which is not part of the interval\n   * (Interval is half-open).\n   * @type {DateTime}\n   */\n  get end() {\n    return this.isValid ? this.e : null;\n  }\n\n  /**\n   * Returns the last DateTime included in the interval (since end is not part of the interval)\n   * @type {DateTime}\n   */\n  get lastDateTime() {\n    return this.isValid ? (this.e ? this.e.minus(1) : null) : null;\n  }\n\n  /**\n   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalidReason === null;\n  }\n\n  /**\n   * Returns an error code if this Interval is invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Returns the length of the Interval in the specified unit.\n   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.\n   * @return {number}\n   */\n  length(unit = \"milliseconds\") {\n    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;\n  }\n\n  /**\n   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.\n   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'\n   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'\n   * @param {string} [unit='milliseconds'] - the unit of time to count.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime\n   * @return {number}\n   */\n  count(unit = \"milliseconds\", opts) {\n    if (!this.isValid) return NaN;\n    const start = this.start.startOf(unit, opts);\n    let end;\n    if (opts?.useLocaleWeeks) {\n      end = this.end.reconfigure({ locale: start.locale });\n    } else {\n      end = this.end;\n    }\n    end = end.startOf(unit, opts);\n    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());\n  }\n\n  /**\n   * Returns whether this Interval's start and end are both in the same unit of time\n   * @param {string} unit - the unit of time to check sameness on\n   * @return {boolean}\n   */\n  hasSame(unit) {\n    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end DateTimes.\n   * @return {boolean}\n   */\n  isEmpty() {\n    return this.s.valueOf() === this.e.valueOf();\n  }\n\n  /**\n   * Return whether this Interval's start is after the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isAfter(dateTime) {\n    if (!this.isValid) return false;\n    return this.s > dateTime;\n  }\n\n  /**\n   * Return whether this Interval's end is before the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isBefore(dateTime) {\n    if (!this.isValid) return false;\n    return this.e <= dateTime;\n  }\n\n  /**\n   * Return whether this Interval contains the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  contains(dateTime) {\n    if (!this.isValid) return false;\n    return this.s <= dateTime && this.e > dateTime;\n  }\n\n  /**\n   * \"Sets\" the start and/or end dates. Returns a newly-constructed Interval.\n   * @param {Object} values - the values to set\n   * @param {DateTime} values.start - the starting DateTime\n   * @param {DateTime} values.end - the ending DateTime\n   * @return {Interval}\n   */\n  set({ start, end } = {}) {\n    if (!this.isValid) return this;\n    return Interval.fromDateTimes(start || this.s, end || this.e);\n  }\n\n  /**\n   * Split this Interval at each of the specified DateTimes\n   * @param {...DateTime} dateTimes - the unit of time to count.\n   * @return {Array}\n   */\n  splitAt(...dateTimes) {\n    if (!this.isValid) return [];\n    const sorted = dateTimes\n        .map(friendlyDateTime)\n        .filter((d) => this.contains(d))\n        .sort((a, b) => a.toMillis() - b.toMillis()),\n      results = [];\n    let { s } = this,\n      i = 0;\n\n    while (s < this.e) {\n      const added = sorted[i] || this.e,\n        next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      i += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into smaller Intervals, each of the specified length.\n   * Left over time is grouped into a smaller interval\n   * @param {Duration|Object|number} duration - The length of each resulting interval.\n   * @return {Array}\n   */\n  splitBy(duration) {\n    const dur = Duration.fromDurationLike(duration);\n\n    if (!this.isValid || !dur.isValid || dur.as(\"milliseconds\") === 0) {\n      return [];\n    }\n\n    let { s } = this,\n      idx = 1,\n      next;\n\n    const results = [];\n    while (s < this.e) {\n      const added = this.start.plus(dur.mapUnits((x) => x * idx));\n      next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      idx += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into the specified number of smaller intervals.\n   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.\n   * @return {Array}\n   */\n  divideEqually(numberOfParts) {\n    if (!this.isValid) return [];\n    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);\n  }\n\n  /**\n   * Return whether this Interval overlaps with the specified Interval\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  overlaps(other) {\n    return this.e > other.s && this.s < other.e;\n  }\n\n  /**\n   * Return whether this Interval's end is adjacent to the specified Interval's start.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsStart(other) {\n    if (!this.isValid) return false;\n    return +this.e === +other.s;\n  }\n\n  /**\n   * Return whether this Interval's start is adjacent to the specified Interval's end.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsEnd(other) {\n    if (!this.isValid) return false;\n    return +other.e === +this.s;\n  }\n\n  /**\n   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  engulfs(other) {\n    if (!this.isValid) return false;\n    return this.s <= other.s && this.e >= other.e;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end as the specified Interval.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    return this.s.equals(other.s) && this.e.equals(other.e);\n  }\n\n  /**\n   * Return an Interval representing the intersection of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.\n   * Returns null if the intersection is empty, meaning, the intervals don't intersect.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  intersection(other) {\n    if (!this.isValid) return this;\n    const s = this.s > other.s ? this.s : other.s,\n      e = this.e < other.e ? this.e : other.e;\n\n    if (s >= e) {\n      return null;\n    } else {\n      return Interval.fromDateTimes(s, e);\n    }\n  }\n\n  /**\n   * Return an Interval representing the union of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  union(other) {\n    if (!this.isValid) return this;\n    const s = this.s < other.s ? this.s : other.s,\n      e = this.e > other.e ? this.e : other.e;\n    return Interval.fromDateTimes(s, e);\n  }\n\n  /**\n   * Merge an array of Intervals into an equivalent minimal set of Intervals.\n   * Combines overlapping and adjacent Intervals.\n   * The resulting array will contain the Intervals in ascending order, that is, starting with the earliest Interval\n   * and ending with the latest.\n   *\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static merge(intervals) {\n    const [found, final] = intervals\n      .sort((a, b) => a.s - b.s)\n      .reduce(\n        ([sofar, current], item) => {\n          if (!current) {\n            return [sofar, item];\n          } else if (current.overlaps(item) || current.abutsStart(item)) {\n            return [sofar, current.union(item)];\n          } else {\n            return [sofar.concat([current]), item];\n          }\n        },\n        [[], null]\n      );\n    if (final) {\n      found.push(final);\n    }\n    return found;\n  }\n\n  /**\n   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static xor(intervals) {\n    let start = null,\n      currentCount = 0;\n    const results = [],\n      ends = intervals.map((i) => [\n        { time: i.s, type: \"s\" },\n        { time: i.e, type: \"e\" },\n      ]),\n      flattened = Array.prototype.concat(...ends),\n      arr = flattened.sort((a, b) => a.time - b.time);\n\n    for (const i of arr) {\n      currentCount += i.type === \"s\" ? 1 : -1;\n\n      if (currentCount === 1) {\n        start = i.time;\n      } else {\n        if (start && +start !== +i.time) {\n          results.push(Interval.fromDateTimes(start, i.time));\n        }\n\n        start = null;\n      }\n    }\n\n    return Interval.merge(results);\n  }\n\n  /**\n   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.\n   * @param {...Interval} intervals\n   * @return {Array}\n   */\n  difference(...intervals) {\n    return Interval.xor([this].concat(intervals))\n      .map((i) => this.intersection(i))\n      .filter((i) => i && !i.isEmpty());\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for debugging.\n   * @return {string}\n   */\n  toString() {\n    if (!this.isValid) return INVALID;\n    return `[${this.s.toISO()} – ${this.e.toISO()})`;\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;\n    } else {\n      return `Interval { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns a localized string representing this Interval. Accepts the same options as the\n   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as\n   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method\n   * is browser-specific, but in general it will return an appropriate representation of the\n   * Interval in the assigned locale. Defaults to the system's locale if no locale has been\n   * specified.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or\n   * Intl.DateTimeFormat constructor options.\n   * @param {Object} opts - Options to override the configuration of the start DateTime.\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Interval.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISO(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of date of this Interval.\n   * The time components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {string}\n   */\n  toISODate() {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISODate()}/${this.e.toISODate()}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of time of this Interval.\n   * The date components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISOTime(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this Interval formatted according to the specified format\n   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible\n   * formatting tool.\n   * @param {string} dateFormat - The format string. This string formats the start and end time.\n   * See {@link DateTime#toFormat} for details.\n   * @param {Object} opts - Options.\n   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end\n   * representations.\n   * @return {string}\n   */\n  toFormat(dateFormat, { separator = \" – \" } = {}) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;\n  }\n\n  /**\n   * Return a Duration representing the time spanned by this interval.\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }\n   * @return {Duration}\n   */\n  toDuration(unit, opts) {\n    if (!this.isValid) {\n      return Duration.invalid(this.invalidReason);\n    }\n    return this.e.diff(this.s, unit, opts);\n  }\n\n  /**\n   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes\n   * @param {function} mapFn\n   * @return {Interval}\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))\n   */\n  mapEndpoints(mapFn) {\n    return Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Settings from \"./settings.js\";\nimport Locale from \"./impl/locale.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\n\nimport { hasLocaleWeekInfo, hasRelative } from \"./impl/util.js\";\n\n/**\n * The Info class contains static methods for retrieving general time and date related data. For example, it has methods for finding out if a time zone has a DST, for listing the months in any supported locale, and for discovering which of Luxon features are available in the current environment.\n */\nexport default class Info {\n  /**\n   * Return whether the specified zone contains a DST.\n   * @param {string|Zone} [zone='local'] - Zone to check. Defaults to the environment's local zone.\n   * @return {boolean}\n   */\n  static hasDST(zone = Settings.defaultZone) {\n    const proto = DateTime.now().setZone(zone).set({ month: 12 });\n\n    return !zone.isUniversal && proto.offset !== proto.set({ month: 6 }).offset;\n  }\n\n  /**\n   * Return whether the specified zone is a valid IANA specifier.\n   * @param {string} zone - Zone to check\n   * @return {boolean}\n   */\n  static isValidIANAZone(zone) {\n    return IANAZone.isValidZone(zone);\n  }\n\n  /**\n   * Converts the input into a {@link Zone} instance.\n   *\n   * * If `input` is already a Zone instance, it is returned unchanged.\n   * * If `input` is a string containing a valid time zone name, a Zone instance\n   *   with that name is returned.\n   * * If `input` is a string that doesn't refer to a known time zone, a Zone\n   *   instance with {@link Zone#isValid} == false is returned.\n   * * If `input is a number, a Zone instance with the specified fixed offset\n   *   in minutes is returned.\n   * * If `input` is `null` or `undefined`, the default zone is returned.\n   * @param {string|Zone|number} [input] - the value to be converted\n   * @return {Zone}\n   */\n  static normalizeZone(input) {\n    return normalizeZone(input, Settings.defaultZone);\n  }\n\n  /**\n   * Get the weekday on which the week starts according to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number} the start of the week, 1 for Monday through 7 for Sunday\n   */\n  static getStartOfWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getStartOfWeek();\n  }\n\n  /**\n   * Get the minimum number of days necessary in a week before it is considered part of the next year according\n   * to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number}\n   */\n  static getMinimumDaysInFirstWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getMinDaysInFirstWeek();\n  }\n\n  /**\n   * Get the weekdays, which are considered the weekend according to the given locale\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number[]} an array of weekdays, 1 for Monday through 7 for Sunday\n   */\n  static getWeekendWeekdays({ locale = null, locObj = null } = {}) {\n    // copy the array, because we cache it internally\n    return (locObj || Locale.create(locale)).getWeekendDays().slice();\n  }\n\n  /**\n   * Return an array of standalone month names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @example Info.months()[0] //=> 'January'\n   * @example Info.months('short')[0] //=> 'Jan'\n   * @example Info.months('numeric')[0] //=> '1'\n   * @example Info.months('short', { locale: 'fr-CA' } )[0] //=> 'janv.'\n   * @example Info.months('numeric', { locale: 'ar' })[0] //=> '١'\n   * @example Info.months('long', { outputCalendar: 'islamic' })[0] //=> 'Rabiʻ I'\n   * @return {Array}\n   */\n  static months(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length);\n  }\n\n  /**\n   * Return an array of format month names.\n   * Format months differ from standalone months in that they're meant to appear next to the day of the month. In some languages, that\n   * changes the string.\n   * See {@link Info#months}\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @return {Array}\n   */\n  static monthsFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length, true);\n  }\n\n  /**\n   * Return an array of standalone week names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the weekday representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @example Info.weekdays()[0] //=> 'Monday'\n   * @example Info.weekdays('short')[0] //=> 'Mon'\n   * @example Info.weekdays('short', { locale: 'fr-CA' })[0] //=> 'lun.'\n   * @example Info.weekdays('short', { locale: 'ar' })[0] //=> 'الاثنين'\n   * @return {Array}\n   */\n  static weekdays(length = \"long\", { locale = null, numberingSystem = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length);\n  }\n\n  /**\n   * Return an array of format week names.\n   * Format weekdays differ from standalone weekdays in that they're meant to appear next to more date information. In some languages, that\n   * changes the string.\n   * See {@link Info#weekdays}\n   * @param {string} [length='long'] - the length of the month representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale=null] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @return {Array}\n   */\n  static weekdaysFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length, true);\n  }\n\n  /**\n   * Return an array of meridiems.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.meridiems() //=> [ 'AM', 'PM' ]\n   * @example Info.meridiems({ locale: 'my' }) //=> [ 'နံနက်', 'ညနေ' ]\n   * @return {Array}\n   */\n  static meridiems({ locale = null } = {}) {\n    return Locale.create(locale).meridiems();\n  }\n\n  /**\n   * Return an array of eras, such as ['BC', 'AD']. The locale can be specified, but the calendar system is always Gregorian.\n   * @param {string} [length='short'] - the length of the era representation, such as \"short\" or \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.eras() //=> [ 'BC', 'AD' ]\n   * @example Info.eras('long') //=> [ 'Before Christ', 'Anno Domini' ]\n   * @example Info.eras('long', { locale: 'fr' }) //=> [ 'avant Jésus-Christ', 'après Jésus-Christ' ]\n   * @return {Array}\n   */\n  static eras(length = \"short\", { locale = null } = {}) {\n    return Locale.create(locale, null, \"gregory\").eras(length);\n  }\n\n  /**\n   * Return the set of available features in this environment.\n   * Some features of Luxon are not available in all environments. For example, on older browsers, relative time formatting support is not available. Use this function to figure out if that's the case.\n   * Keys:\n   * * `relative`: whether this environment supports relative time formatting\n   * * `localeWeek`: whether this environment supports different weekdays for the start of the week based on the locale\n   * @example Info.features() //=> { relative: false, localeWeek: true }\n   * @return {Object}\n   */\n  static features() {\n    return { relative: hasRelative(), localeWeek: hasLocaleWeekInfo() };\n  }\n}\n", "import Duration from \"../duration.js\";\n\nfunction dayDiff(earlier, later) {\n  const utcDayStart = (dt) => dt.toUTC(0, { keepLocalTime: true }).startOf(\"day\").valueOf(),\n    ms = utcDayStart(later) - utcDayStart(earlier);\n  return Math.floor(Duration.fromMillis(ms).as(\"days\"));\n}\n\nfunction highOrderDiffs(cursor, later, units) {\n  const differs = [\n    [\"years\", (a, b) => b.year - a.year],\n    [\"quarters\", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4],\n    [\"months\", (a, b) => b.month - a.month + (b.year - a.year) * 12],\n    [\n      \"weeks\",\n      (a, b) => {\n        const days = dayDiff(a, b);\n        return (days - (days % 7)) / 7;\n      },\n    ],\n    [\"days\", dayDiff],\n  ];\n\n  const results = {};\n  const earlier = cursor;\n  let lowestOrder, highWater;\n\n  /* This loop tries to diff using larger units first.\n     If we overshoot, we backtrack and try the next smaller unit.\n     \"cursor\" starts out at the earlier timestamp and moves closer and closer to \"later\"\n     as we use smaller and smaller units.\n     highWater keeps track of where we would be if we added one more of the smallest unit,\n     this is used later to potentially convert any difference smaller than the smallest higher order unit\n     into a fraction of that smallest higher order unit\n  */\n  for (const [unit, differ] of differs) {\n    if (units.indexOf(unit) >= 0) {\n      lowestOrder = unit;\n\n      results[unit] = differ(cursor, later);\n      highWater = earlier.plus(results);\n\n      if (highWater > later) {\n        // we overshot the end point, backtrack cursor by 1\n        results[unit]--;\n        cursor = earlier.plus(results);\n\n        // if we are still overshooting now, we need to backtrack again\n        // this happens in certain situations when diffing times in different zones,\n        // because this calculation ignores time zones\n        if (cursor > later) {\n          // keep the \"overshot by 1\" around as highWater\n          highWater = cursor;\n          // backtrack cursor by 1\n          results[unit]--;\n          cursor = earlier.plus(results);\n        }\n      } else {\n        cursor = highWater;\n      }\n    }\n  }\n\n  return [cursor, results, highWater, lowestOrder];\n}\n\nexport default function (earlier, later, units, opts) {\n  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);\n\n  const remainingMillis = later - cursor;\n\n  const lowerOrderUnits = units.filter(\n    (u) => [\"hours\", \"minutes\", \"seconds\", \"milliseconds\"].indexOf(u) >= 0\n  );\n\n  if (lowerOrderUnits.length === 0) {\n    if (highWater < later) {\n      highWater = cursor.plus({ [lowestOrder]: 1 });\n    }\n\n    if (highWater !== cursor) {\n      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);\n    }\n  }\n\n  const duration = Duration.fromObject(results, opts);\n\n  if (lowerOrderUnits.length > 0) {\n    return Duration.fromMillis(remainingMillis, opts)\n      .shiftTo(...lowerOrderUnits)\n      .plus(duration);\n  } else {\n    return duration;\n  }\n}\n", "import { parseM<PERSON>is, isUndefined, untruncate<PERSON>ear, signedOffset, hasOwnProperty } from \"./util.js\";\nimport Formatter from \"./formatter.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport DateTime from \"../datetime.js\";\nimport { digitRegex, parseDigits } from \"./digits.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst MISSING_FTP = \"missing Intl.DateTimeFormat.formatToParts support\";\n\nfunction intUnit(regex, post = (i) => i) {\n  return { regex, deser: ([s]) => post(parseDigits(s)) };\n}\n\nconst NBSP = String.fromCharCode(160);\nconst spaceOrNBSP = `[ ${NBSP}]`;\nconst spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, \"g\");\n\nfunction fixListRegex(s) {\n  // make dots optional and also make them literal\n  // make space and non breakable space characters interchangeable\n  return s.replace(/\\./g, \"\\\\.?\").replace(spaceOrNBSPRegExp, spaceOrNBSP);\n}\n\nfunction stripInsensitivities(s) {\n  return s\n    .replace(/\\./g, \"\") // ignore dots that were made optional\n    .replace(spaceOrNBSPRegExp, \" \") // interchange space and nbsp\n    .toLowerCase();\n}\n\nfunction oneOf(strings, startIndex) {\n  if (strings === null) {\n    return null;\n  } else {\n    return {\n      regex: RegExp(strings.map(fixListRegex).join(\"|\")),\n      deser: ([s]) =>\n        strings.findIndex((i) => stripInsensitivities(s) === stripInsensitivities(i)) + startIndex,\n    };\n  }\n}\n\nfunction offset(regex, groups) {\n  return { regex, deser: ([, h, m]) => signedOffset(h, m), groups };\n}\n\nfunction simple(regex) {\n  return { regex, deser: ([s]) => s };\n}\n\nfunction escapeToken(value) {\n  return value.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\n/**\n * @param token\n * @param {Locale} loc\n */\nfunction unitForToken(token, loc) {\n  const one = digitRegex(loc),\n    two = digitRegex(loc, \"{2}\"),\n    three = digitRegex(loc, \"{3}\"),\n    four = digitRegex(loc, \"{4}\"),\n    six = digitRegex(loc, \"{6}\"),\n    oneOrTwo = digitRegex(loc, \"{1,2}\"),\n    oneToThree = digitRegex(loc, \"{1,3}\"),\n    oneToSix = digitRegex(loc, \"{1,6}\"),\n    oneToNine = digitRegex(loc, \"{1,9}\"),\n    twoToFour = digitRegex(loc, \"{2,4}\"),\n    fourToSix = digitRegex(loc, \"{4,6}\"),\n    literal = (t) => ({ regex: RegExp(escapeToken(t.val)), deser: ([s]) => s, literal: true }),\n    unitate = (t) => {\n      if (token.literal) {\n        return literal(t);\n      }\n      switch (t.val) {\n        // era\n        case \"G\":\n          return oneOf(loc.eras(\"short\"), 0);\n        case \"GG\":\n          return oneOf(loc.eras(\"long\"), 0);\n        // years\n        case \"y\":\n          return intUnit(oneToSix);\n        case \"yy\":\n          return intUnit(twoToFour, untruncateYear);\n        case \"yyyy\":\n          return intUnit(four);\n        case \"yyyyy\":\n          return intUnit(fourToSix);\n        case \"yyyyyy\":\n          return intUnit(six);\n        // months\n        case \"M\":\n          return intUnit(oneOrTwo);\n        case \"MM\":\n          return intUnit(two);\n        case \"MMM\":\n          return oneOf(loc.months(\"short\", true), 1);\n        case \"MMMM\":\n          return oneOf(loc.months(\"long\", true), 1);\n        case \"L\":\n          return intUnit(oneOrTwo);\n        case \"LL\":\n          return intUnit(two);\n        case \"LLL\":\n          return oneOf(loc.months(\"short\", false), 1);\n        case \"LLLL\":\n          return oneOf(loc.months(\"long\", false), 1);\n        // dates\n        case \"d\":\n          return intUnit(oneOrTwo);\n        case \"dd\":\n          return intUnit(two);\n        // ordinals\n        case \"o\":\n          return intUnit(oneToThree);\n        case \"ooo\":\n          return intUnit(three);\n        // time\n        case \"HH\":\n          return intUnit(two);\n        case \"H\":\n          return intUnit(oneOrTwo);\n        case \"hh\":\n          return intUnit(two);\n        case \"h\":\n          return intUnit(oneOrTwo);\n        case \"mm\":\n          return intUnit(two);\n        case \"m\":\n          return intUnit(oneOrTwo);\n        case \"q\":\n          return intUnit(oneOrTwo);\n        case \"qq\":\n          return intUnit(two);\n        case \"s\":\n          return intUnit(oneOrTwo);\n        case \"ss\":\n          return intUnit(two);\n        case \"S\":\n          return intUnit(oneToThree);\n        case \"SSS\":\n          return intUnit(three);\n        case \"u\":\n          return simple(oneToNine);\n        case \"uu\":\n          return simple(oneOrTwo);\n        case \"uuu\":\n          return intUnit(one);\n        // meridiem\n        case \"a\":\n          return oneOf(loc.meridiems(), 0);\n        // weekYear (k)\n        case \"kkkk\":\n          return intUnit(four);\n        case \"kk\":\n          return intUnit(twoToFour, untruncateYear);\n        // weekNumber (W)\n        case \"W\":\n          return intUnit(oneOrTwo);\n        case \"WW\":\n          return intUnit(two);\n        // weekdays\n        case \"E\":\n        case \"c\":\n          return intUnit(one);\n        case \"EEE\":\n          return oneOf(loc.weekdays(\"short\", false), 1);\n        case \"EEEE\":\n          return oneOf(loc.weekdays(\"long\", false), 1);\n        case \"ccc\":\n          return oneOf(loc.weekdays(\"short\", true), 1);\n        case \"cccc\":\n          return oneOf(loc.weekdays(\"long\", true), 1);\n        // offset/zone\n        case \"Z\":\n        case \"ZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);\n        case \"ZZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);\n        // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing\n        // because we don't have any way to figure out what they are\n        case \"z\":\n          return simple(/[a-z_+-/]{1,256}?/i);\n        // this special-case \"token\" represents a place where a macro-token expanded into a white-space literal\n        // in this case we accept any non-newline white-space\n        case \" \":\n          return simple(/[^\\S\\n\\r]/);\n        default:\n          return literal(t);\n      }\n    };\n\n  const unit = unitate(token) || {\n    invalidReason: MISSING_FTP,\n  };\n\n  unit.token = token;\n\n  return unit;\n}\n\nconst partTypeStyleToTokenVal = {\n  year: {\n    \"2-digit\": \"yy\",\n    numeric: \"yyyyy\",\n  },\n  month: {\n    numeric: \"M\",\n    \"2-digit\": \"MM\",\n    short: \"MMM\",\n    long: \"MMMM\",\n  },\n  day: {\n    numeric: \"d\",\n    \"2-digit\": \"dd\",\n  },\n  weekday: {\n    short: \"EEE\",\n    long: \"EEEE\",\n  },\n  dayperiod: \"a\",\n  dayPeriod: \"a\",\n  hour12: {\n    numeric: \"h\",\n    \"2-digit\": \"hh\",\n  },\n  hour24: {\n    numeric: \"H\",\n    \"2-digit\": \"HH\",\n  },\n  minute: {\n    numeric: \"m\",\n    \"2-digit\": \"mm\",\n  },\n  second: {\n    numeric: \"s\",\n    \"2-digit\": \"ss\",\n  },\n  timeZoneName: {\n    long: \"ZZZZZ\",\n    short: \"ZZZ\",\n  },\n};\n\nfunction tokenForPart(part, formatOpts, resolvedOpts) {\n  const { type, value } = part;\n\n  if (type === \"literal\") {\n    const isSpace = /^\\s+$/.test(value);\n    return {\n      literal: !isSpace,\n      val: isSpace ? \" \" : value,\n    };\n  }\n\n  const style = formatOpts[type];\n\n  // The user might have explicitly specified hour12 or hourCycle\n  // if so, respect their decision\n  // if not, refer back to the resolvedOpts, which are based on the locale\n  let actualType = type;\n  if (type === \"hour\") {\n    if (formatOpts.hour12 != null) {\n      actualType = formatOpts.hour12 ? \"hour12\" : \"hour24\";\n    } else if (formatOpts.hourCycle != null) {\n      if (formatOpts.hourCycle === \"h11\" || formatOpts.hourCycle === \"h12\") {\n        actualType = \"hour12\";\n      } else {\n        actualType = \"hour24\";\n      }\n    } else {\n      // tokens only differentiate between 24 hours or not,\n      // so we do not need to check hourCycle here, which is less supported anyways\n      actualType = resolvedOpts.hour12 ? \"hour12\" : \"hour24\";\n    }\n  }\n  let val = partTypeStyleToTokenVal[actualType];\n  if (typeof val === \"object\") {\n    val = val[style];\n  }\n\n  if (val) {\n    return {\n      literal: false,\n      val,\n    };\n  }\n\n  return undefined;\n}\n\nfunction buildRegex(units) {\n  const re = units.map((u) => u.regex).reduce((f, r) => `${f}(${r.source})`, \"\");\n  return [`^${re}$`, units];\n}\n\nfunction match(input, regex, handlers) {\n  const matches = input.match(regex);\n\n  if (matches) {\n    const all = {};\n    let matchIndex = 1;\n    for (const i in handlers) {\n      if (hasOwnProperty(handlers, i)) {\n        const h = handlers[i],\n          groups = h.groups ? h.groups + 1 : 1;\n        if (!h.literal && h.token) {\n          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));\n        }\n        matchIndex += groups;\n      }\n    }\n    return [matches, all];\n  } else {\n    return [matches, {}];\n  }\n}\n\nfunction dateTimeFromMatches(matches) {\n  const toField = (token) => {\n    switch (token) {\n      case \"S\":\n        return \"millisecond\";\n      case \"s\":\n        return \"second\";\n      case \"m\":\n        return \"minute\";\n      case \"h\":\n      case \"H\":\n        return \"hour\";\n      case \"d\":\n        return \"day\";\n      case \"o\":\n        return \"ordinal\";\n      case \"L\":\n      case \"M\":\n        return \"month\";\n      case \"y\":\n        return \"year\";\n      case \"E\":\n      case \"c\":\n        return \"weekday\";\n      case \"W\":\n        return \"weekNumber\";\n      case \"k\":\n        return \"weekYear\";\n      case \"q\":\n        return \"quarter\";\n      default:\n        return null;\n    }\n  };\n\n  let zone = null;\n  let specificOffset;\n  if (!isUndefined(matches.z)) {\n    zone = IANAZone.create(matches.z);\n  }\n\n  if (!isUndefined(matches.Z)) {\n    if (!zone) {\n      zone = new FixedOffsetZone(matches.Z);\n    }\n    specificOffset = matches.Z;\n  }\n\n  if (!isUndefined(matches.q)) {\n    matches.M = (matches.q - 1) * 3 + 1;\n  }\n\n  if (!isUndefined(matches.h)) {\n    if (matches.h < 12 && matches.a === 1) {\n      matches.h += 12;\n    } else if (matches.h === 12 && matches.a === 0) {\n      matches.h = 0;\n    }\n  }\n\n  if (matches.G === 0 && matches.y) {\n    matches.y = -matches.y;\n  }\n\n  if (!isUndefined(matches.u)) {\n    matches.S = parseMillis(matches.u);\n  }\n\n  const vals = Object.keys(matches).reduce((r, k) => {\n    const f = toField(k);\n    if (f) {\n      r[f] = matches[k];\n    }\n\n    return r;\n  }, {});\n\n  return [vals, zone, specificOffset];\n}\n\nlet dummyDateTimeCache = null;\n\nfunction getDummyDateTime() {\n  if (!dummyDateTimeCache) {\n    dummyDateTimeCache = DateTime.fromMillis(1555555555555);\n  }\n\n  return dummyDateTimeCache;\n}\n\nfunction maybeExpandMacroToken(token, locale) {\n  if (token.literal) {\n    return token;\n  }\n\n  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);\n  const tokens = formatOptsToTokens(formatOpts, locale);\n\n  if (tokens == null || tokens.includes(undefined)) {\n    return token;\n  }\n\n  return tokens;\n}\n\nexport function expandMacroTokens(tokens, locale) {\n  return Array.prototype.concat(...tokens.map((t) => maybeExpandMacroToken(t, locale)));\n}\n\n/**\n * @private\n */\n\nexport class TokenParser {\n  constructor(locale, format) {\n    this.locale = locale;\n    this.format = format;\n    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);\n    this.units = this.tokens.map((t) => unitForToken(t, locale));\n    this.disqualifyingUnit = this.units.find((t) => t.invalidReason);\n\n    if (!this.disqualifyingUnit) {\n      const [regexString, handlers] = buildRegex(this.units);\n      this.regex = RegExp(regexString, \"i\");\n      this.handlers = handlers;\n    }\n  }\n\n  explainFromTokens(input) {\n    if (!this.isValid) {\n      return { input, tokens: this.tokens, invalidReason: this.invalidReason };\n    } else {\n      const [rawMatches, matches] = match(input, this.regex, this.handlers),\n        [result, zone, specificOffset] = matches\n          ? dateTimeFromMatches(matches)\n          : [null, null, undefined];\n      if (hasOwnProperty(matches, \"a\") && hasOwnProperty(matches, \"H\")) {\n        throw new ConflictingSpecificationError(\n          \"Can't include meridiem when specifying 24-hour format\"\n        );\n      }\n      return {\n        input,\n        tokens: this.tokens,\n        regex: this.regex,\n        rawMatches,\n        matches,\n        result,\n        zone,\n        specificOffset,\n      };\n    }\n  }\n\n  get isValid() {\n    return !this.disqualifyingUnit;\n  }\n\n  get invalidReason() {\n    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;\n  }\n}\n\nexport function explainFromTokens(locale, input, format) {\n  const parser = new TokenParser(locale, format);\n  return parser.explainFromTokens(input);\n}\n\nexport function parseFromTokens(locale, input, format) {\n  const { result, zone, specificOffset, invalidReason } = explainFromTokens(locale, input, format);\n  return [result, zone, specificOffset, invalidReason];\n}\n\nexport function formatOptsToTokens(formatOpts, locale) {\n  if (!formatOpts) {\n    return null;\n  }\n\n  const formatter = Formatter.create(locale, formatOpts);\n  const df = formatter.dtFormatter(getDummyDateTime());\n  const parts = df.formatToParts();\n  const resolvedOpts = df.resolvedOptions();\n  return parts.map((p) => tokenForPart(p, formatOpts, resolvedOpts));\n}\n", "import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport {\n  isUndefined,\n  maybeArray,\n  isDate,\n  isNumber,\n  bestBy,\n  daysInMonth,\n  daysInYear,\n  isLeapYear,\n  weeksInWeekYear,\n  normalizeObject,\n  roundTo,\n  objToLocalTS,\n  padStart,\n} from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport {\n  parseFromTokens,\n  explainFromTokens,\n  formatOptsToTokens,\n  expandMacroTokens,\n  TokenParser,\n} from \"./impl/tokenParser.js\";\nimport {\n  gregorianToWeek,\n  weekToGregorian,\n  gregorianToOrdinal,\n  ordinalToGregorian,\n  hasInvalidGregorianData,\n  hasInvalidWeekData,\n  hasInvalidOrdinalData,\n  hasInvalidTimeData,\n  usesLocalWeekValues,\n  isoWeekdayToLocal,\n} from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport {\n  InvalidArgumentError,\n  ConflictingSpecificationError,\n  InvalidUnitError,\n  InvalidDateTimeError,\n} from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\n\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\n\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(\n      dt.c,\n      dt.loc.getMinDaysInFirstWeek(),\n      dt.loc.getStartOfWeek()\n    );\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid,\n  };\n  return new DateTime({ ...current, ...alts, old: current });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n\n  const d = new Date(ts);\n\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds(),\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day:\n        Math.min(inst.c.day, daysInMonth(year, month)) +\n        Math.trunc(dur.days) +\n        Math.trunc(dur.weeks) * 7,\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds,\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n\n  return { ts, o };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const { setZone, zone } = opts;\n  if ((parsed && Object.keys(parsed).length !== 0) || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset,\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(\n      new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)\n    );\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid\n    ? Formatter.create(Locale.create(\"en-US\"), {\n        allowZ,\n        forceSimple: true,\n      }).formatDateTimeFromString(dt, format)\n    : null;\n}\n\nfunction toISODate(o, extended, precision) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n  if (precision === \"year\") return c;\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    if (precision === \"month\") return c;\n    c += \"-\";\n  } else {\n    c += padStart(o.c.month);\n    if (precision === \"month\") return c;\n  }\n  c += padStart(o.c.day);\n  return c;\n}\n\nfunction toISOTime(\n  o,\n  extended,\n  suppressSeconds,\n  suppressMilliseconds,\n  includeOffset,\n  extendedZone,\n  precision\n) {\n  let showSeconds = !suppressSeconds || o.c.millisecond !== 0 || o.c.second !== 0,\n    c = \"\";\n  switch (precision) {\n    case \"day\":\n    case \"month\":\n    case \"year\":\n      break;\n    default:\n      c += padStart(o.c.hour);\n      if (precision === \"hour\") break;\n      if (extended) {\n        c += \":\";\n        c += padStart(o.c.minute);\n        if (precision === \"minute\") break;\n        if (showSeconds) {\n          c += \":\";\n          c += padStart(o.c.second);\n        }\n      } else {\n        c += padStart(o.c.minute);\n        if (precision === \"minute\") break;\n        if (showSeconds) {\n          c += padStart(o.c.second);\n        }\n      }\n      if (precision === \"second\") break;\n      if (showSeconds && (!suppressMilliseconds || o.c.millisecond !== 0)) {\n        c += \".\";\n        c += padStart(o.c.millisecond, 3);\n      }\n  }\n\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\n    \"weekYear\",\n    \"weekNumber\",\n    \"weekday\",\n    \"hour\",\n    \"minute\",\n    \"second\",\n    \"millisecond\",\n  ],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\",\n  }[unit.toLowerCase()];\n\n  if (!normalized) throw new InvalidUnitError(unit);\n\n  return normalized;\n}\n\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\n/**\n * @param {Zone} zone\n * @return {number}\n */\nfunction guessOffsetForZone(zone) {\n  if (zoneOffsetTs === undefined) {\n    zoneOffsetTs = Settings.now();\n  }\n\n  // Do not cache anything but IANA zones, because it is not safe to do so.\n  // Guessing an offset which is not present in the zone can cause wrong results from fixOffset\n  if (zone.type !== \"iana\") {\n    return zone.offset(zoneOffsetTs);\n  }\n  const zoneName = zone.name;\n  let offsetGuess = zoneOffsetGuessCache.get(zoneName);\n  if (offsetGuess === undefined) {\n    offsetGuess = zone.offset(zoneOffsetTs);\n    zoneOffsetGuessCache.set(zoneName, offsetGuess);\n  }\n  return offsetGuess;\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n\n  const loc = Locale.fromObject(opts);\n\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n\n  return new DateTime({ ts, zone, loc, o });\n}\n\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    rounding = isUndefined(opts.rounding) ? \"trunc\" : opts.rounding,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, opts.calendary ? \"round\" : rounding);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = (unit) => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\n\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nconst zoneOffsetGuessCache = new Map();\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n\n    let invalid =\n      config.invalid ||\n      (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) ||\n      (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options),\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`\n      );\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);\n\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset)\n        ? opts.specificOffset\n        : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    const useWeekData = definiteWeekDef || (normalized.weekday && !containsGregor);\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData\n        ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? hasInvalidOrdinalData(normalized)\n        : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData\n        ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? ordinalToGregorian(normalized)\n        : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc,\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      );\n    }\n\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({ invalid });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return (o && o.isLuxonDateTime) || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map((t) => (t ? t.val : null)).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map((t) => t.val).join(\"\");\n  }\n\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache.clear();\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return (\n        this.offset > this.set({ month: 1, day: 1 }).offset ||\n        this.offset > this.set({ month: 5 }).offset\n      );\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (\n      c1.hour === c2.hour &&\n      c1.minute === c2.minute &&\n      c1.second === c2.second &&\n      c1.millisecond === c2.millisecond\n    ) {\n      return [clone(this, { ts: ts1 }), clone(this, { ts: ts2 })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid\n      ? weeksInWeekYear(\n          this.localWeekYear,\n          this.loc.getMinDaysInFirstWeek(),\n          this.loc.getStartOfWeek()\n        )\n      : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const { locale, numberingSystem, calendar } = Formatter.create(\n      this.loc.clone(opts),\n      opts\n    ).resolvedOptions(this);\n    return { locale, numberingSystem, outputCalendar: calendar };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, { ts: newTS, zone });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });\n    return clone(this, { loc });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({ locale });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);\n\n    const settingWeekStuff =\n        !isUndefined(normalized.weekYear) ||\n        !isUndefined(normalized.weekNumber) ||\n        !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian(\n        { ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek), ...normalized },\n        minDaysInFirstWeek,\n        startOfWeek\n      );\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({ ...gregorianToOrdinal(this.c), ...normalized });\n    } else {\n      mixed = { ...this.toObject(), ...normalized };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, { ts, o });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, { useLocaleWeeks = false } = {}) {\n    if (!this.isValid) return this;\n\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const { weekday } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid\n      ? this.plus({ [unit]: 1 })\n          .startOf(unit, opts)\n          .minus(1)\n      : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this)\n      : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @param {string} [opts.precision='milliseconds'] - truncate output to desired presicion: 'years', 'months', 'days', 'hours', 'minutes', 'seconds' or 'milliseconds'. When precision and suppressSeconds or suppressMilliseconds are used together, precision sets the maximum unit shown in the output, however seconds or milliseconds will still be suppressed if they are 0.\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @example DateTime.now().toISO({ precision: 'day' }) //=> '2017-04-22Z'\n   * @example DateTime.now().toISO({ precision: 'minute' }) //=> '2017-04-22T20:47Z'\n   * @return {string|null}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false,\n    precision = \"milliseconds\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    precision = normalizeUnit(precision);\n    const ext = format === \"extended\";\n\n    let c = toISODate(this, ext, precision);\n    if (orderedUnits.indexOf(precision) >= 3) c += \"T\";\n    c += toISOTime(\n      this,\n      ext,\n      suppressSeconds,\n      suppressMilliseconds,\n      includeOffset,\n      extendedZone,\n      precision\n    );\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @param {string} [opts.precision='day'] - truncate output to desired precision: 'years', 'months', or 'days'.\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ precision: 'month' }) //=> '1982-05'\n   * @return {string|null}\n   */\n  toISODate({ format = \"extended\", precision = \"day\" } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, format === \"extended\", normalizeUnit(precision));\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @param {string} [opts.precision='milliseconds'] - truncate output to desired presicion: 'hours', 'minutes', 'seconds' or 'milliseconds'. When precision and suppressSeconds or suppressMilliseconds are used together, precision sets the maximum unit shown in the output, however seconds or milliseconds will still be suppressed if they are 0.\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, second: 56 }).toISOTime({ precision: 'minute' }) //=> '07:34Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\",\n    precision = \"milliseconds\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    precision = normalizeUnit(precision);\n    let c = includePrefix && orderedUnits.indexOf(precision) >= 3 ? \"T\" : \"\";\n    return (\n      c +\n      toISOTime(\n        this,\n        format === \"extended\",\n        suppressSeconds,\n        suppressMilliseconds,\n        includeOffset,\n        extendedZone,\n        precision\n      )\n    );\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string|null}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (including milliseconds in the fractional part) of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n\n    const base = { ...this.c };\n\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n\n    const durOpts = { locale: this.locale, numberingSystem: this.numberingSystem, ...opts };\n\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval|DateTime}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });\n    return (\n      adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts)\n    );\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return (\n      this.isValid &&\n      other.isValid &&\n      this.valueOf() === other.valueOf() &&\n      this.zone.equals(other.zone) &&\n      this.loc.equals(other.loc)\n    );\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds towards zero by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {string} [options.rounding=\"trunc\"] - rounding method to use when rounding the numbers in the output. Can be \"trunc\" (toward zero), \"expand\" (away from zero), \"round\", \"floor\", or \"ceil\".\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, { zone: this.zone }),\n      padding = options.padding ? (this < base ? -options.padding : options.padding) : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit,\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n\n    return diffRelative(options.base || DateTime.fromObject({}, { zone: this.zone }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true,\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\n        \"fromFormatParser requires an input string and a format parser\"\n      );\n    }\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(\n        `fromFormatParser called with a locale of ${localeToUse}, ` +\n          `but the format parser was created for ${formatParser.locale}`\n      );\n    }\n\n    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);\n\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(\n        result,\n        zone,\n        opts,\n        `format ${formatParser.format}`,\n        text,\n        specificOffset\n      );\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`\n    );\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Info from \"./info.js\";\nimport Zone from \"./zone.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport InvalidZone from \"./zones/invalidZone.js\";\nimport SystemZone from \"./zones/systemZone.js\";\nimport Settings from \"./settings.js\";\n\nconst VERSION = \"3.7.1\";\n\nexport {\n  VERSION,\n  DateTime,\n  Duration,\n  Interval,\n  Info,\n  Zone,\n  FixedOffsetZone,\n  IANAZone,\n  InvalidZone,\n  SystemZone,\n  Settings,\n};\n"], "mappings": ";;;;;;;AAKA,IAAM,aAAN,cAAyB,MAAM;AAAC;AAKzB,IAAM,uBAAN,cAAmC,WAAW;AAAA,EACnD,YAAY,QAAQ;AAClB,UAAM,qBAAqB,OAAO,UAAU,CAAC,EAAE;AAAA,EACjD;AACF;AAKO,IAAM,uBAAN,cAAmC,WAAW;AAAA,EACnD,YAAY,QAAQ;AAClB,UAAM,qBAAqB,OAAO,UAAU,CAAC,EAAE;AAAA,EACjD;AACF;AAKO,IAAM,uBAAN,cAAmC,WAAW;AAAA,EACnD,YAAY,QAAQ;AAClB,UAAM,qBAAqB,OAAO,UAAU,CAAC,EAAE;AAAA,EACjD;AACF;AAKO,IAAM,gCAAN,cAA4C,WAAW;AAAC;AAKxD,IAAM,mBAAN,cAA+B,WAAW;AAAA,EAC/C,YAAY,MAAM;AAChB,UAAM,gBAAgB,IAAI,EAAE;AAAA,EAC9B;AACF;AAKO,IAAM,uBAAN,cAAmC,WAAW;AAAC;AAK/C,IAAM,sBAAN,cAAkC,WAAW;AAAA,EAClD,cAAc;AACZ,UAAM,2BAA2B;AAAA,EACnC;AACF;;;ACxDA,IAAM,IAAI;AAAV,IACE,IAAI;AADN,IAEE,IAAI;AAEC,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AACP;AAEO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AACP;AAEO,IAAM,wBAAwB;AAAA,EACnC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AACX;AAEO,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AACP;AAEO,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AACX;AAEO,IAAM,cAAc;AAAA,EACzB,MAAM;AAAA,EACN,QAAQ;AACV;AAEO,IAAM,oBAAoB;AAAA,EAC/B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAEO,IAAM,yBAAyB;AAAA,EACpC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,cAAc;AAChB;AAEO,IAAM,wBAAwB;AAAA,EACnC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,cAAc;AAChB;AAEO,IAAM,iBAAiB;AAAA,EAC5B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,WAAW;AACb;AAEO,IAAM,uBAAuB;AAAA,EAClC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AACb;AAEO,IAAM,4BAA4B;AAAA,EACvC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,cAAc;AAChB;AAEO,IAAM,2BAA2B;AAAA,EACtC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,cAAc;AAChB;AAEO,IAAM,iBAAiB;AAAA,EAC5B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AACV;AAEO,IAAM,8BAA8B;AAAA,EACzC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAEO,IAAM,eAAe;AAAA,EAC1B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AACV;AAEO,IAAM,4BAA4B;AAAA,EACvC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAEO,IAAM,4BAA4B;AAAA,EACvC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AACV;AAEO,IAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,cAAc;AAChB;AAEO,IAAM,6BAA6B;AAAA,EACxC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,cAAc;AAChB;AAEO,IAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,cAAc;AAChB;AAEO,IAAM,6BAA6B;AAAA,EACxC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,cAAc;AAChB;;;AC1KA,IAAqB,OAArB,MAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,IAAI,OAAO;AACT,UAAM,IAAI,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,UAAM,IAAI,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,cAAc;AAChB,UAAM,IAAI,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,WAAW,IAAI,MAAM;AACnB,UAAM,IAAI,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,IAAI,QAAQ;AACvB,UAAM,IAAI,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,IAAI;AACT,UAAM,IAAI,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,WAAW;AAChB,UAAM,IAAI,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AACZ,UAAM,IAAI,oBAAoB;AAAA,EAChC;AACF;;;AC7FA,IAAI,YAAY;AAMhB,IAAqB,aAArB,MAAqB,oBAAmB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3C,WAAW,WAAW;AACpB,QAAI,cAAc,MAAM;AACtB,kBAAY,IAAI,YAAW;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,OAAO;AACT,WAAO,IAAI,KAAK,eAAe,EAAE,gBAAgB,EAAE;AAAA,EACrD;AAAA;AAAA,EAGA,IAAI,cAAc;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,WAAW,IAAI,EAAE,QAAQ,OAAO,GAAG;AACjC,WAAO,cAAc,IAAI,QAAQ,MAAM;AAAA,EACzC;AAAA;AAAA,EAGA,aAAa,IAAI,QAAQ;AACvB,WAAO,aAAa,KAAK,OAAO,EAAE,GAAG,MAAM;AAAA,EAC7C;AAAA;AAAA,EAGA,OAAO,IAAI;AACT,WAAO,CAAC,IAAI,KAAK,EAAE,EAAE,kBAAkB;AAAA,EACzC;AAAA;AAAA,EAGA,OAAO,WAAW;AAChB,WAAO,UAAU,SAAS;AAAA,EAC5B;AAAA;AAAA,EAGA,IAAI,UAAU;AACZ,WAAO;AAAA,EACT;AACF;;;ACzDA,IAAM,WAAW,oBAAI,IAAI;AACzB,SAAS,QAAQ,UAAU;AACzB,MAAI,MAAM,SAAS,IAAI,QAAQ;AAC/B,MAAI,QAAQ,QAAW;AACrB,UAAM,IAAI,KAAK,eAAe,SAAS;AAAA,MACrC,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,CAAC;AACD,aAAS,IAAI,UAAU,GAAG;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,IAAM,YAAY;AAAA,EAChB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AACV;AAEA,SAAS,YAAY,KAAK,MAAM;AAC9B,QAAM,YAAY,IAAI,OAAO,IAAI,EAAE,QAAQ,WAAW,EAAE,GACtD,SAAS,kDAAkD,KAAK,SAAS,GACzE,CAAC,EAAE,QAAQ,MAAM,OAAO,SAAS,OAAO,SAAS,OAAO,IAAI;AAC9D,SAAO,CAAC,OAAO,QAAQ,MAAM,SAAS,OAAO,SAAS,OAAO;AAC/D;AAEA,SAAS,YAAY,KAAK,MAAM;AAC9B,QAAM,YAAY,IAAI,cAAc,IAAI;AACxC,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAM,EAAE,MAAM,MAAM,IAAI,UAAU,CAAC;AACnC,UAAM,MAAM,UAAU,IAAI;AAE1B,QAAI,SAAS,OAAO;AAClB,aAAO,GAAG,IAAI;AAAA,IAChB,WAAW,CAAC,YAAY,GAAG,GAAG;AAC5B,aAAO,GAAG,IAAI,SAAS,OAAO,EAAE;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,gBAAgB,oBAAI,IAAI;AAK9B,IAAqB,WAArB,MAAqB,kBAAiB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzC,OAAO,OAAO,MAAM;AAClB,QAAI,OAAO,cAAc,IAAI,IAAI;AACjC,QAAI,SAAS,QAAW;AACtB,oBAAc,IAAI,MAAO,OAAO,IAAI,UAAS,IAAI,CAAE;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,aAAa;AAClB,kBAAc,MAAM;AACpB,aAAS,MAAM;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,iBAAiBA,IAAG;AACzB,WAAO,KAAK,YAAYA,EAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,YAAY,MAAM;AACvB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI;AACF,UAAI,KAAK,eAAe,SAAS,EAAE,UAAU,KAAK,CAAC,EAAE,OAAO;AAC5D,aAAO;AAAA,IACT,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,YAAY,MAAM;AAChB,UAAM;AAEN,SAAK,WAAW;AAEhB,SAAK,QAAQ,UAAS,YAAY,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,cAAc;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,WAAW,IAAI,EAAE,QAAQ,OAAO,GAAG;AACjC,WAAO,cAAc,IAAI,QAAQ,QAAQ,KAAK,IAAI;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,IAAI,QAAQ;AACvB,WAAO,aAAa,KAAK,OAAO,EAAE,GAAG,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,IAAI;AACT,QAAI,CAAC,KAAK,MAAO,QAAO;AACxB,UAAM,OAAO,IAAI,KAAK,EAAE;AAExB,QAAI,MAAM,IAAI,EAAG,QAAO;AAExB,UAAM,MAAM,QAAQ,KAAK,IAAI;AAC7B,QAAI,CAAC,MAAM,OAAO,KAAK,QAAQ,MAAM,QAAQ,MAAM,IAAI,IAAI,gBACvD,YAAY,KAAK,IAAI,IACrB,YAAY,KAAK,IAAI;AAEzB,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC,KAAK,IAAI,IAAI,IAAI;AAAA,IAC3B;AAGA,UAAM,eAAe,SAAS,KAAK,IAAI;AAEvC,UAAM,QAAQ,aAAa;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAED,QAAI,OAAO,CAAC;AACZ,UAAM,OAAO,OAAO;AACpB,YAAQ,QAAQ,IAAI,OAAO,MAAO;AAClC,YAAQ,QAAQ,SAAS,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,WAAW;AAChB,WAAO,UAAU,SAAS,UAAU,UAAU,SAAS,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AACF;;;AClOA,IAAI,cAAc,CAAC;AACnB,SAAS,YAAY,WAAW,OAAO,CAAC,GAAG;AACzC,QAAM,MAAM,KAAK,UAAU,CAAC,WAAW,IAAI,CAAC;AAC5C,MAAI,MAAM,YAAY,GAAG;AACzB,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,KAAK,WAAW,WAAW,IAAI;AACzC,gBAAY,GAAG,IAAI;AAAA,EACrB;AACA,SAAO;AACT;AAEA,IAAM,cAAc,oBAAI,IAAI;AAC5B,SAAS,aAAa,WAAW,OAAO,CAAC,GAAG;AAC1C,QAAM,MAAM,KAAK,UAAU,CAAC,WAAW,IAAI,CAAC;AAC5C,MAAI,MAAM,YAAY,IAAI,GAAG;AAC7B,MAAI,QAAQ,QAAW;AACrB,UAAM,IAAI,KAAK,eAAe,WAAW,IAAI;AAC7C,gBAAY,IAAI,KAAK,GAAG;AAAA,EAC1B;AACA,SAAO;AACT;AAEA,IAAM,eAAe,oBAAI,IAAI;AAC7B,SAAS,aAAa,WAAW,OAAO,CAAC,GAAG;AAC1C,QAAM,MAAM,KAAK,UAAU,CAAC,WAAW,IAAI,CAAC;AAC5C,MAAI,MAAM,aAAa,IAAI,GAAG;AAC9B,MAAI,QAAQ,QAAW;AACrB,UAAM,IAAI,KAAK,aAAa,WAAW,IAAI;AAC3C,iBAAa,IAAI,KAAK,GAAG;AAAA,EAC3B;AACA,SAAO;AACT;AAEA,IAAM,eAAe,oBAAI,IAAI;AAC7B,SAAS,aAAa,WAAW,OAAO,CAAC,GAAG;AAC1C,QAAkC,WAA1B,OA3CV,IA2CoC,IAAjB,yBAAiB,IAAjB,CAAT;AACR,QAAM,MAAM,KAAK,UAAU,CAAC,WAAW,YAAY,CAAC;AACpD,MAAI,MAAM,aAAa,IAAI,GAAG;AAC9B,MAAI,QAAQ,QAAW;AACrB,UAAM,IAAI,KAAK,mBAAmB,WAAW,IAAI;AACjD,iBAAa,IAAI,KAAK,GAAG;AAAA,EAC3B;AACA,SAAO;AACT;AAEA,IAAI,iBAAiB;AACrB,SAAS,eAAe;AACtB,MAAI,gBAAgB;AAClB,WAAO;AAAA,EACT,OAAO;AACL,qBAAiB,IAAI,KAAK,eAAe,EAAE,gBAAgB,EAAE;AAC7D,WAAO;AAAA,EACT;AACF;AAEA,IAAM,2BAA2B,oBAAI,IAAI;AACzC,SAAS,4BAA4B,WAAW;AAC9C,MAAI,OAAO,yBAAyB,IAAI,SAAS;AACjD,MAAI,SAAS,QAAW;AACtB,WAAO,IAAI,KAAK,eAAe,SAAS,EAAE,gBAAgB;AAC1D,6BAAyB,IAAI,WAAW,IAAI;AAAA,EAC9C;AACA,SAAO;AACT;AAEA,IAAM,gBAAgB,oBAAI,IAAI;AAC9B,SAAS,kBAAkB,WAAW;AACpC,MAAI,OAAO,cAAc,IAAI,SAAS;AACtC,MAAI,CAAC,MAAM;AACT,UAAM,SAAS,IAAI,KAAK,OAAO,SAAS;AAExC,WAAO,iBAAiB,SAAS,OAAO,YAAY,IAAI,OAAO;AAE/D,QAAI,EAAE,iBAAiB,OAAO;AAC5B,aAAO,kCAAK,uBAAyB;AAAA,IACvC;AACA,kBAAc,IAAI,WAAW,IAAI;AAAA,EACnC;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,WAAW;AAYpC,QAAM,SAAS,UAAU,QAAQ,KAAK;AACtC,MAAI,WAAW,IAAI;AACjB,gBAAY,UAAU,UAAU,GAAG,MAAM;AAAA,EAC3C;AAEA,QAAM,SAAS,UAAU,QAAQ,KAAK;AACtC,MAAI,WAAW,IAAI;AACjB,WAAO,CAAC,SAAS;AAAA,EACnB,OAAO;AACL,QAAI;AACJ,QAAI;AACJ,QAAI;AACF,gBAAU,aAAa,SAAS,EAAE,gBAAgB;AAClD,oBAAc;AAAA,IAChB,SAAS,GAAG;AACV,YAAM,UAAU,UAAU,UAAU,GAAG,MAAM;AAC7C,gBAAU,aAAa,OAAO,EAAE,gBAAgB;AAChD,oBAAc;AAAA,IAChB;AAEA,UAAM,EAAE,iBAAiB,SAAS,IAAI;AACtC,WAAO,CAAC,aAAa,iBAAiB,QAAQ;AAAA,EAChD;AACF;AAEA,SAAS,iBAAiB,WAAW,iBAAiB,gBAAgB;AACpE,MAAI,kBAAkB,iBAAiB;AACrC,QAAI,CAAC,UAAU,SAAS,KAAK,GAAG;AAC9B,mBAAa;AAAA,IACf;AAEA,QAAI,gBAAgB;AAClB,mBAAa,OAAO,cAAc;AAAA,IACpC;AAEA,QAAI,iBAAiB;AACnB,mBAAa,OAAO,eAAe;AAAA,IACrC;AACA,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAU,GAAG;AACpB,QAAM,KAAK,CAAC;AACZ,WAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,UAAM,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC;AAClC,OAAG,KAAK,EAAE,EAAE,CAAC;AAAA,EACf;AACA,SAAO;AACT;AAEA,SAAS,YAAY,GAAG;AACtB,QAAM,KAAK,CAAC;AACZ,WAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,UAAM,KAAK,SAAS,IAAI,MAAM,IAAI,KAAK,CAAC;AACxC,OAAG,KAAK,EAAE,EAAE,CAAC;AAAA,EACf;AACA,SAAO;AACT;AAEA,SAAS,UAAU,KAAK,QAAQ,WAAW,QAAQ;AACjD,QAAM,OAAO,IAAI,YAAY;AAE7B,MAAI,SAAS,SAAS;AACpB,WAAO;AAAA,EACT,WAAW,SAAS,MAAM;AACxB,WAAO,UAAU,MAAM;AAAA,EACzB,OAAO;AACL,WAAO,OAAO,MAAM;AAAA,EACtB;AACF;AAEA,SAAS,oBAAoB,KAAK;AAChC,MAAI,IAAI,mBAAmB,IAAI,oBAAoB,QAAQ;AACzD,WAAO;AAAA,EACT,OAAO;AACL,WACE,IAAI,oBAAoB,UACxB,CAAC,IAAI,UACL,IAAI,OAAO,WAAW,IAAI,KAC1B,4BAA4B,IAAI,MAAM,EAAE,oBAAoB;AAAA,EAEhE;AACF;AAMA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,MAAM,aAAa,MAAM;AACnC,SAAK,QAAQ,KAAK,SAAS;AAC3B,SAAK,QAAQ,KAAK,SAAS;AAE3B,UAAuC,WAA/B,SAAO,MArMnB,IAqM2C,IAAd,sBAAc,IAAd,CAAjB,SAAO;AAEf,QAAI,CAAC,eAAe,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG;AACrD,YAAM,WAAW,iBAAE,aAAa,SAAU;AAC1C,UAAI,KAAK,QAAQ,EAAG,UAAS,uBAAuB,KAAK;AACzD,WAAK,MAAM,aAAa,MAAM,QAAQ;AAAA,IACxC;AAAA,EACF;AAAA,EAEA,OAAO,GAAG;AACR,QAAI,KAAK,KAAK;AACZ,YAAM,QAAQ,KAAK,QAAQ,KAAK,MAAM,CAAC,IAAI;AAC3C,aAAO,KAAK,IAAI,OAAO,KAAK;AAAA,IAC9B,OAAO;AAEL,YAAM,QAAQ,KAAK,QAAQ,KAAK,MAAM,CAAC,IAAI,QAAQ,GAAG,CAAC;AACvD,aAAO,SAAS,OAAO,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AACF;AAMA,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,IAAI,MAAM,MAAM;AAC1B,SAAK,OAAO;AACZ,SAAK,eAAe;AAEpB,QAAI,IAAI;AACR,QAAI,KAAK,KAAK,UAAU;AAEtB,WAAK,KAAK;AAAA,IACZ,WAAW,GAAG,KAAK,SAAS,SAAS;AAOnC,YAAM,YAAY,MAAM,GAAG,SAAS;AACpC,YAAM,UAAU,aAAa,IAAI,WAAW,SAAS,KAAK,UAAU,SAAS;AAC7E,UAAI,GAAG,WAAW,KAAK,SAAS,OAAO,OAAO,EAAE,OAAO;AACrD,YAAI;AACJ,aAAK,KAAK;AAAA,MACZ,OAAO;AAGL,YAAI;AACJ,aAAK,KAAK,GAAG,WAAW,IAAI,KAAK,GAAG,QAAQ,KAAK,EAAE,KAAK,EAAE,SAAS,GAAG,OAAO,CAAC;AAC9E,aAAK,eAAe,GAAG;AAAA,MACzB;AAAA,IACF,WAAW,GAAG,KAAK,SAAS,UAAU;AACpC,WAAK,KAAK;AAAA,IACZ,WAAW,GAAG,KAAK,SAAS,QAAQ;AAClC,WAAK,KAAK;AACV,UAAI,GAAG,KAAK;AAAA,IACd,OAAO;AAGL,UAAI;AACJ,WAAK,KAAK,GAAG,QAAQ,KAAK,EAAE,KAAK,EAAE,SAAS,GAAG,OAAO,CAAC;AACvD,WAAK,eAAe,GAAG;AAAA,IACzB;AAEA,UAAM,WAAW,mBAAK,KAAK;AAC3B,aAAS,WAAW,SAAS,YAAY;AACzC,SAAK,MAAM,aAAa,MAAM,QAAQ;AAAA,EACxC;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,cAAc;AAGrB,aAAO,KAAK,cAAc,EACvB,IAAI,CAAC,EAAE,MAAM,MAAM,KAAK,EACxB,KAAK,EAAE;AAAA,IACZ;AACA,WAAO,KAAK,IAAI,OAAO,KAAK,GAAG,SAAS,CAAC;AAAA,EAC3C;AAAA,EAEA,gBAAgB;AACd,UAAM,QAAQ,KAAK,IAAI,cAAc,KAAK,GAAG,SAAS,CAAC;AACvD,QAAI,KAAK,cAAc;AACrB,aAAO,MAAM,IAAI,CAAC,SAAS;AACzB,YAAI,KAAK,SAAS,gBAAgB;AAChC,gBAAM,aAAa,KAAK,aAAa,WAAW,KAAK,GAAG,IAAI;AAAA,YAC1D,QAAQ,KAAK,GAAG;AAAA,YAChB,QAAQ,KAAK,KAAK;AAAA,UACpB,CAAC;AACD,iBAAO,iCACF,OADE;AAAA,YAEL,OAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,IAAI,gBAAgB;AAAA,EAClC;AACF;AAKA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,MAAM,WAAW,MAAM;AACjC,SAAK,OAAO,iBAAE,OAAO,UAAW;AAChC,QAAI,CAAC,aAAa,YAAY,GAAG;AAC/B,WAAK,MAAM,aAAa,MAAM,IAAI;AAAA,IACpC;AAAA,EACF;AAAA,EAEA,OAAO,OAAO,MAAM;AAClB,QAAI,KAAK,KAAK;AACZ,aAAO,KAAK,IAAI,OAAO,OAAO,IAAI;AAAA,IACpC,OAAO;AACL,aAAe,mBAAmB,MAAM,OAAO,KAAK,KAAK,SAAS,KAAK,KAAK,UAAU,MAAM;AAAA,IAC9F;AAAA,EACF;AAAA,EAEA,cAAc,OAAO,MAAM;AACzB,QAAI,KAAK,KAAK;AACZ,aAAO,KAAK,IAAI,cAAc,OAAO,IAAI;AAAA,IAC3C,OAAO;AACL,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB;AAAA,EAC3B,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS,CAAC,GAAG,CAAC;AAChB;AAKA,IAAqB,SAArB,MAAqB,QAAO;AAAA,EAC1B,OAAO,SAAS,MAAM;AACpB,WAAO,QAAO;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EAEA,OAAO,OAAO,QAAQ,iBAAiB,gBAAgB,cAAc,cAAc,OAAO;AACxF,UAAM,kBAAkB,UAAU,SAAS;AAE3C,UAAM,UAAU,oBAAoB,cAAc,UAAU,aAAa;AACzE,UAAM,mBAAmB,mBAAmB,SAAS;AACrD,UAAM,kBAAkB,kBAAkB,SAAS;AACnD,UAAM,gBAAgB,qBAAqB,YAAY,KAAK,SAAS;AACrE,WAAO,IAAI,QAAO,SAAS,kBAAkB,iBAAiB,eAAe,eAAe;AAAA,EAC9F;AAAA,EAEA,OAAO,aAAa;AAClB,qBAAiB;AACjB,gBAAY,MAAM;AAClB,iBAAa,MAAM;AACnB,iBAAa,MAAM;AACnB,6BAAyB,MAAM;AAC/B,kBAAc,MAAM;AAAA,EACtB;AAAA,EAEA,OAAO,WAAW,EAAE,QAAQ,iBAAiB,gBAAgB,aAAa,IAAI,CAAC,GAAG;AAChF,WAAO,QAAO,OAAO,QAAQ,iBAAiB,gBAAgB,YAAY;AAAA,EAC5E;AAAA,EAEA,YAAY,QAAQ,WAAW,gBAAgB,cAAc,iBAAiB;AAC5E,UAAM,CAAC,cAAc,uBAAuB,oBAAoB,IAAI,kBAAkB,MAAM;AAE5F,SAAK,SAAS;AACd,SAAK,kBAAkB,aAAa,yBAAyB;AAC7D,SAAK,iBAAiB,kBAAkB,wBAAwB;AAChE,SAAK,eAAe;AACpB,SAAK,OAAO,iBAAiB,KAAK,QAAQ,KAAK,iBAAiB,KAAK,cAAc;AAEnF,SAAK,gBAAgB,EAAE,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE;AAClD,SAAK,cAAc,EAAE,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE;AAChD,SAAK,gBAAgB;AACrB,SAAK,WAAW,CAAC;AAEjB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EAEA,IAAI,cAAc;AAChB,QAAI,KAAK,qBAAqB,MAAM;AAClC,WAAK,oBAAoB,oBAAoB,IAAI;AAAA,IACnD;AAEA,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,cAAc;AACZ,UAAM,eAAe,KAAK,UAAU;AACpC,UAAM,kBACH,KAAK,oBAAoB,QAAQ,KAAK,oBAAoB,YAC1D,KAAK,mBAAmB,QAAQ,KAAK,mBAAmB;AAC3D,WAAO,gBAAgB,iBAAiB,OAAO;AAAA,EACjD;AAAA,EAEA,MAAM,MAAM;AACV,QAAI,CAAC,QAAQ,OAAO,oBAAoB,IAAI,EAAE,WAAW,GAAG;AAC1D,aAAO;AAAA,IACT,OAAO;AACL,aAAO,QAAO;AAAA,QACZ,KAAK,UAAU,KAAK;AAAA,QACpB,KAAK,mBAAmB,KAAK;AAAA,QAC7B,KAAK,kBAAkB,KAAK;AAAA,QAC5B,qBAAqB,KAAK,YAAY,KAAK,KAAK;AAAA,QAChD,KAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cAAc,OAAO,CAAC,GAAG;AACvB,WAAO,KAAK,MAAM,iCAAK,OAAL,EAAW,aAAa,KAAK,EAAC;AAAA,EAClD;AAAA,EAEA,kBAAkB,OAAO,CAAC,GAAG;AAC3B,WAAO,KAAK,MAAM,iCAAK,OAAL,EAAW,aAAa,MAAM,EAAC;AAAA,EACnD;AAAA,EAEA,OAAO,QAAQ,SAAS,OAAO;AAC7B,WAAO,UAAU,MAAM,QAAgB,QAAQ,MAAM;AAInD,YAAM,mBAAmB,KAAK,SAAS,QAAQ,KAAK,KAAK,WAAW,KAAK;AACzE,gBAAU,CAAC;AACX,YAAM,OAAO,SAAS,EAAE,OAAO,QAAQ,KAAK,UAAU,IAAI,EAAE,OAAO,OAAO,GACxE,YAAY,SAAS,WAAW;AAClC,UAAI,CAAC,KAAK,YAAY,SAAS,EAAE,MAAM,GAAG;AACxC,cAAM,SAAS,CAAC,mBACZ,CAAC,OAAO,KAAK,QAAQ,IAAI,MAAM,OAAO,IACtC,CAAC,OAAO,KAAK,YAAY,IAAI,IAAI,EAAE,OAAO;AAC9C,aAAK,YAAY,SAAS,EAAE,MAAM,IAAI,UAAU,MAAM;AAAA,MACxD;AACA,aAAO,KAAK,YAAY,SAAS,EAAE,MAAM;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EAEA,SAAS,QAAQ,SAAS,OAAO;AAC/B,WAAO,UAAU,MAAM,QAAgB,UAAU,MAAM;AACrD,YAAM,OAAO,SACP,EAAE,SAAS,QAAQ,MAAM,WAAW,OAAO,QAAQ,KAAK,UAAU,IAClE,EAAE,SAAS,OAAO,GACtB,YAAY,SAAS,WAAW;AAClC,UAAI,CAAC,KAAK,cAAc,SAAS,EAAE,MAAM,GAAG;AAC1C,aAAK,cAAc,SAAS,EAAE,MAAM,IAAI;AAAA,UAAY,CAAC,OACnD,KAAK,QAAQ,IAAI,MAAM,SAAS;AAAA,QAClC;AAAA,MACF;AACA,aAAO,KAAK,cAAc,SAAS,EAAE,MAAM;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAc;AAAA,MACd,MAAM;AAGJ,YAAI,CAAC,KAAK,eAAe;AACvB,gBAAM,OAAO,EAAE,MAAM,WAAW,WAAW,MAAM;AACjD,eAAK,gBAAgB,CAAC,SAAS,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,SAAS,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC,EAAE;AAAA,YACnF,CAAC,OAAO,KAAK,QAAQ,IAAI,MAAM,WAAW;AAAA,UAC5C;AAAA,QACF;AAEA,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,QAAQ;AACX,WAAO,UAAU,MAAM,QAAgB,MAAM,MAAM;AACjD,YAAM,OAAO,EAAE,KAAK,OAAO;AAI3B,UAAI,CAAC,KAAK,SAAS,MAAM,GAAG;AAC1B,aAAK,SAAS,MAAM,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE;AAAA,UAAI,CAAC,OAC/E,KAAK,QAAQ,IAAI,MAAM,KAAK;AAAA,QAC9B;AAAA,MACF;AAEA,aAAO,KAAK,SAAS,MAAM;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EAEA,QAAQ,IAAI,UAAU,OAAO;AAC3B,UAAM,KAAK,KAAK,YAAY,IAAI,QAAQ,GACtC,UAAU,GAAG,cAAc,GAC3B,WAAW,QAAQ,KAAK,CAAC,MAAM,EAAE,KAAK,YAAY,MAAM,KAAK;AAC/D,WAAO,WAAW,SAAS,QAAQ;AAAA,EACrC;AAAA,EAEA,gBAAgB,OAAO,CAAC,GAAG;AAGzB,WAAO,IAAI,oBAAoB,KAAK,MAAM,KAAK,eAAe,KAAK,aAAa,IAAI;AAAA,EACtF;AAAA,EAEA,YAAY,IAAI,WAAW,CAAC,GAAG;AAC7B,WAAO,IAAI,kBAAkB,IAAI,KAAK,MAAM,QAAQ;AAAA,EACtD;AAAA,EAEA,aAAa,OAAO,CAAC,GAAG;AACtB,WAAO,IAAI,iBAAiB,KAAK,MAAM,KAAK,UAAU,GAAG,IAAI;AAAA,EAC/D;AAAA,EAEA,cAAc,OAAO,CAAC,GAAG;AACvB,WAAO,YAAY,KAAK,MAAM,IAAI;AAAA,EACpC;AAAA,EAEA,YAAY;AACV,WACE,KAAK,WAAW,QAChB,KAAK,OAAO,YAAY,MAAM,WAC9B,4BAA4B,KAAK,IAAI,EAAE,OAAO,WAAW,OAAO;AAAA,EAEpE;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,cAAc;AACrB,aAAO,KAAK;AAAA,IACd,WAAW,CAAC,kBAAkB,GAAG;AAC/B,aAAO;AAAA,IACT,OAAO;AACL,aAAO,kBAAkB,KAAK,MAAM;AAAA,IACtC;AAAA,EACF;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,gBAAgB,EAAE;AAAA,EAChC;AAAA,EAEA,wBAAwB;AACtB,WAAO,KAAK,gBAAgB,EAAE;AAAA,EAChC;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,gBAAgB,EAAE;AAAA,EAChC;AAAA,EAEA,OAAO,OAAO;AACZ,WACE,KAAK,WAAW,MAAM,UACtB,KAAK,oBAAoB,MAAM,mBAC/B,KAAK,mBAAmB,MAAM;AAAA,EAElC;AAAA,EAEA,WAAW;AACT,WAAO,UAAU,KAAK,MAAM,KAAK,KAAK,eAAe,KAAK,KAAK,cAAc;AAAA,EAC/E;AACF;;;ACrjBA,IAAIC,aAAY;AAMhB,IAAqB,kBAArB,MAAqB,yBAAwB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhD,WAAW,cAAc;AACvB,QAAIA,eAAc,MAAM;AACtB,MAAAA,aAAY,IAAI,iBAAgB,CAAC;AAAA,IACnC;AACA,WAAOA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAASC,SAAQ;AACtB,WAAOA,YAAW,IAAI,iBAAgB,cAAc,IAAI,iBAAgBA,OAAM;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,eAAeC,IAAG;AACvB,QAAIA,IAAG;AACL,YAAM,IAAIA,GAAE,MAAM,uCAAuC;AACzD,UAAI,GAAG;AACL,eAAO,IAAI,iBAAgB,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,MACrD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,YAAYD,SAAQ;AAClB,UAAM;AAEN,SAAK,QAAQA;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU,IAAI,QAAQ,MAAM,aAAa,KAAK,OAAO,QAAQ,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,WAAW;AACb,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,UAAU,aAAa,CAAC,KAAK,OAAO,QAAQ,CAAC;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,IAAI,QAAQ;AACvB,WAAO,aAAa,KAAK,OAAO,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,cAAc;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,WAAW;AAChB,WAAO,UAAU,SAAS,WAAW,UAAU,UAAU,KAAK;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,UAAU;AACZ,WAAO;AAAA,EACT;AACF;;;AC/IA,IAAqB,cAArB,cAAyC,KAAK;AAAA,EAC5C,YAAY,UAAU;AACpB,UAAM;AAEN,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAGA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAGA,IAAI,cAAc;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,aAAa;AACX,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,eAAe;AACb,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,SAAS;AACP,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,SAAS;AACP,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,UAAU;AACZ,WAAO;AAAA,EACT;AACF;;;ACxCO,SAAS,cAAc,OAAOE,cAAa;AAChD,MAAIC;AACJ,MAAI,YAAY,KAAK,KAAK,UAAU,MAAM;AACxC,WAAOD;AAAA,EACT,WAAW,iBAAiB,MAAM;AAChC,WAAO;AAAA,EACT,WAAW,SAAS,KAAK,GAAG;AAC1B,UAAM,UAAU,MAAM,YAAY;AAClC,QAAI,YAAY,UAAW,QAAOA;AAAA,aACzB,YAAY,WAAW,YAAY,SAAU,QAAO,WAAW;AAAA,aAC/D,YAAY,SAAS,YAAY,MAAO,QAAO,gBAAgB;AAAA,QACnE,QAAO,gBAAgB,eAAe,OAAO,KAAK,SAAS,OAAO,KAAK;AAAA,EAC9E,WAAW,SAAS,KAAK,GAAG;AAC1B,WAAO,gBAAgB,SAAS,KAAK;AAAA,EACvC,WAAW,OAAO,UAAU,YAAY,YAAY,SAAS,OAAO,MAAM,WAAW,YAAY;AAG/F,WAAO;AAAA,EACT,OAAO;AACL,WAAO,IAAI,YAAY,KAAK;AAAA,EAC9B;AACF;;;ACjCA,IAAM,mBAAmB;AAAA,EACvB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAEA,IAAM,wBAAwB;AAAA,EAC5B,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,SAAS,CAAC,MAAM,IAAI;AAAA,EACpB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,UAAU,CAAC,OAAO,KAAK;AAAA,EACvB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,SAAS,CAAC,MAAM,IAAI;AAAA,EACpB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AAAA,EACjB,MAAM,CAAC,MAAM,IAAI;AACnB;AAEA,IAAM,eAAe,iBAAiB,QAAQ,QAAQ,YAAY,EAAE,EAAE,MAAM,EAAE;AAEvE,SAAS,YAAY,KAAK;AAC/B,MAAI,QAAQ,SAAS,KAAK,EAAE;AAC5B,MAAI,MAAM,KAAK,GAAG;AAChB,YAAQ;AACR,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAM,OAAO,IAAI,WAAW,CAAC;AAE7B,UAAI,IAAI,CAAC,EAAE,OAAO,iBAAiB,OAAO,MAAM,IAAI;AAClD,iBAAS,aAAa,QAAQ,IAAI,CAAC,CAAC;AAAA,MACtC,OAAO;AACL,mBAAW,OAAO,uBAAuB;AACvC,gBAAM,CAAC,KAAK,GAAG,IAAI,sBAAsB,GAAG;AAC5C,cAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,qBAAS,OAAO;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,OAAO,EAAE;AAAA,EAC3B,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAGA,IAAM,kBAAkB,oBAAI,IAAI;AACzB,SAAS,uBAAuB;AACrC,kBAAgB,MAAM;AACxB;AAEO,SAAS,WAAW,EAAE,gBAAgB,GAAG,SAAS,IAAI;AAC3D,QAAM,KAAK,mBAAmB;AAE9B,MAAI,cAAc,gBAAgB,IAAI,EAAE;AACxC,MAAI,gBAAgB,QAAW;AAC7B,kBAAc,oBAAI,IAAI;AACtB,oBAAgB,IAAI,IAAI,WAAW;AAAA,EACrC;AACA,MAAI,QAAQ,YAAY,IAAI,MAAM;AAClC,MAAI,UAAU,QAAW;AACvB,YAAQ,IAAI,OAAO,GAAG,iBAAiB,EAAE,CAAC,GAAG,MAAM,EAAE;AACrD,gBAAY,IAAI,QAAQ,KAAK;AAAA,EAC/B;AAEA,SAAO;AACT;;;ACpFA,IAAI,MAAM,MAAM,KAAK,IAAI;AAAzB,IACE,cAAc;AADhB,IAEE,gBAAgB;AAFlB,IAGE,yBAAyB;AAH3B,IAIE,wBAAwB;AAJ1B,IAKE,qBAAqB;AALvB,IAME;AANF,IAOE,sBAAsB;AAKxB,IAAqB,WAArB,MAA8B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,WAAW,MAAM;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,IAAIE,IAAG;AAChB,UAAMA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,YAAY,MAAM;AAC3B,kBAAc;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,cAAc;AACvB,WAAO,cAAc,aAAa,WAAW,QAAQ;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,gBAAgB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,cAAc,QAAQ;AAC/B,oBAAgB;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,yBAAyB;AAClC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,uBAAuB,iBAAiB;AACjD,6BAAyB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,wBAAwB;AACjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,sBAAsB,gBAAgB;AAC/C,4BAAwB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,oBAAoB,cAAc;AAC3C,0BAAsB,qBAAqB,YAAY;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,qBAAqB;AAC9B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,WAAW,mBAAmB,YAAY;AACxC,yBAAqB,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,iBAAiB;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,eAAe,GAAG;AAC3B,qBAAiB;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,cAAc;AACnB,WAAO,WAAW;AAClB,aAAS,WAAW;AACpB,aAAS,WAAW;AACpB,yBAAqB;AAAA,EACvB;AACF;;;ACnLA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,YAAY,QAAQ,aAAa;AAC/B,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AAAA,EAEA,YAAY;AACV,QAAI,KAAK,aAAa;AACpB,aAAO,GAAG,KAAK,MAAM,KAAK,KAAK,WAAW;AAAA,IAC5C,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;ACAA,IAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAA5E,IACE,aAAa,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAErE,SAAS,eAAe,MAAM,OAAO;AACnC,SAAO,IAAI;AAAA,IACT;AAAA,IACA,iBAAiB,KAAK,aAAa,OAAO,KAAK,UAAU,IAAI;AAAA,EAC/D;AACF;AAEO,SAAS,UAAU,MAAM,OAAO,KAAK;AAC1C,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC;AAEjD,MAAI,OAAO,OAAO,QAAQ,GAAG;AAC3B,MAAE,eAAe,EAAE,eAAe,IAAI,IAAI;AAAA,EAC5C;AAEA,QAAM,KAAK,EAAE,UAAU;AAEvB,SAAO,OAAO,IAAI,IAAI;AACxB;AAEA,SAAS,eAAe,MAAM,OAAO,KAAK;AACxC,SAAO,OAAO,WAAW,IAAI,IAAI,aAAa,eAAe,QAAQ,CAAC;AACxE;AAEA,SAAS,iBAAiB,MAAM,SAAS;AACvC,QAAM,QAAQ,WAAW,IAAI,IAAI,aAAa,eAC5C,SAAS,MAAM,UAAU,CAAC,MAAM,IAAI,OAAO,GAC3C,MAAM,UAAU,MAAM,MAAM;AAC9B,SAAO,EAAE,OAAO,SAAS,GAAG,IAAI;AAClC;AAEO,SAAS,kBAAkB,YAAY,aAAa;AACzD,UAAS,aAAa,cAAc,KAAK,IAAK;AAChD;AAMO,SAAS,gBAAgB,SAAS,qBAAqB,GAAG,cAAc,GAAG;AAChF,QAAM,EAAE,MAAM,OAAO,IAAI,IAAI,SAC3B,UAAU,eAAe,MAAM,OAAO,GAAG,GACzC,UAAU,kBAAkB,UAAU,MAAM,OAAO,GAAG,GAAG,WAAW;AAEtE,MAAI,aAAa,KAAK,OAAO,UAAU,UAAU,KAAK,sBAAsB,CAAC,GAC3E;AAEF,MAAI,aAAa,GAAG;AAClB,eAAW,OAAO;AAClB,iBAAa,gBAAgB,UAAU,oBAAoB,WAAW;AAAA,EACxE,WAAW,aAAa,gBAAgB,MAAM,oBAAoB,WAAW,GAAG;AAC9E,eAAW,OAAO;AAClB,iBAAa;AAAA,EACf,OAAO;AACL,eAAW;AAAA,EACb;AAEA,SAAO,iBAAE,UAAU,YAAY,WAAY,WAAW,OAAO;AAC/D;AAEO,SAAS,gBAAgB,UAAU,qBAAqB,GAAG,cAAc,GAAG;AACjF,QAAM,EAAE,UAAU,YAAY,QAAQ,IAAI,UACxC,gBAAgB,kBAAkB,UAAU,UAAU,GAAG,kBAAkB,GAAG,WAAW,GACzF,aAAa,WAAW,QAAQ;AAElC,MAAI,UAAU,aAAa,IAAI,UAAU,gBAAgB,IAAI,oBAC3D;AAEF,MAAI,UAAU,GAAG;AACf,WAAO,WAAW;AAClB,eAAW,WAAW,IAAI;AAAA,EAC5B,WAAW,UAAU,YAAY;AAC/B,WAAO,WAAW;AAClB,eAAW,WAAW,QAAQ;AAAA,EAChC,OAAO;AACL,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,OAAO,IAAI,IAAI,iBAAiB,MAAM,OAAO;AACrD,SAAO,iBAAE,MAAM,OAAO,OAAQ,WAAW,QAAQ;AACnD;AAEO,SAAS,mBAAmB,UAAU;AAC3C,QAAM,EAAE,MAAM,OAAO,IAAI,IAAI;AAC7B,QAAM,UAAU,eAAe,MAAM,OAAO,GAAG;AAC/C,SAAO,iBAAE,MAAM,WAAY,WAAW,QAAQ;AAChD;AAEO,SAAS,mBAAmB,aAAa;AAC9C,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,QAAM,EAAE,OAAO,IAAI,IAAI,iBAAiB,MAAM,OAAO;AACrD,SAAO,iBAAE,MAAM,OAAO,OAAQ,WAAW,WAAW;AACtD;AAQO,SAAS,oBAAoB,KAAK,KAAK;AAC5C,QAAM,oBACJ,CAAC,YAAY,IAAI,YAAY,KAC7B,CAAC,YAAY,IAAI,eAAe,KAChC,CAAC,YAAY,IAAI,aAAa;AAChC,MAAI,mBAAmB;AACrB,UAAM,iBACJ,CAAC,YAAY,IAAI,OAAO,KAAK,CAAC,YAAY,IAAI,UAAU,KAAK,CAAC,YAAY,IAAI,QAAQ;AAExF,QAAI,gBAAgB;AAClB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,YAAY,IAAI,YAAY,EAAG,KAAI,UAAU,IAAI;AACtD,QAAI,CAAC,YAAY,IAAI,eAAe,EAAG,KAAI,aAAa,IAAI;AAC5D,QAAI,CAAC,YAAY,IAAI,aAAa,EAAG,KAAI,WAAW,IAAI;AACxD,WAAO,IAAI;AACX,WAAO,IAAI;AACX,WAAO,IAAI;AACX,WAAO;AAAA,MACL,oBAAoB,IAAI,sBAAsB;AAAA,MAC9C,aAAa,IAAI,eAAe;AAAA,IAClC;AAAA,EACF,OAAO;AACL,WAAO,EAAE,oBAAoB,GAAG,aAAa,EAAE;AAAA,EACjD;AACF;AAEO,SAAS,mBAAmB,KAAK,qBAAqB,GAAG,cAAc,GAAG;AAC/E,QAAM,YAAY,UAAU,IAAI,QAAQ,GACtC,YAAY;AAAA,IACV,IAAI;AAAA,IACJ;AAAA,IACA,gBAAgB,IAAI,UAAU,oBAAoB,WAAW;AAAA,EAC/D,GACA,eAAe,eAAe,IAAI,SAAS,GAAG,CAAC;AAEjD,MAAI,CAAC,WAAW;AACd,WAAO,eAAe,YAAY,IAAI,QAAQ;AAAA,EAChD,WAAW,CAAC,WAAW;AACrB,WAAO,eAAe,QAAQ,IAAI,UAAU;AAAA,EAC9C,WAAW,CAAC,cAAc;AACxB,WAAO,eAAe,WAAW,IAAI,OAAO;AAAA,EAC9C,MAAO,QAAO;AAChB;AAEO,SAAS,sBAAsB,KAAK;AACzC,QAAM,YAAY,UAAU,IAAI,IAAI,GAClC,eAAe,eAAe,IAAI,SAAS,GAAG,WAAW,IAAI,IAAI,CAAC;AAEpE,MAAI,CAAC,WAAW;AACd,WAAO,eAAe,QAAQ,IAAI,IAAI;AAAA,EACxC,WAAW,CAAC,cAAc;AACxB,WAAO,eAAe,WAAW,IAAI,OAAO;AAAA,EAC9C,MAAO,QAAO;AAChB;AAEO,SAAS,wBAAwB,KAAK;AAC3C,QAAM,YAAY,UAAU,IAAI,IAAI,GAClC,aAAa,eAAe,IAAI,OAAO,GAAG,EAAE,GAC5C,WAAW,eAAe,IAAI,KAAK,GAAG,YAAY,IAAI,MAAM,IAAI,KAAK,CAAC;AAExE,MAAI,CAAC,WAAW;AACd,WAAO,eAAe,QAAQ,IAAI,IAAI;AAAA,EACxC,WAAW,CAAC,YAAY;AACtB,WAAO,eAAe,SAAS,IAAI,KAAK;AAAA,EAC1C,WAAW,CAAC,UAAU;AACpB,WAAO,eAAe,OAAO,IAAI,GAAG;AAAA,EACtC,MAAO,QAAO;AAChB;AAEO,SAAS,mBAAmB,KAAK;AACtC,QAAM,EAAE,MAAM,QAAQ,QAAQ,YAAY,IAAI;AAC9C,QAAM,YACF,eAAe,MAAM,GAAG,EAAE,KACzB,SAAS,MAAM,WAAW,KAAK,WAAW,KAAK,gBAAgB,GAClE,cAAc,eAAe,QAAQ,GAAG,EAAE,GAC1C,cAAc,eAAe,QAAQ,GAAG,EAAE,GAC1C,mBAAmB,eAAe,aAAa,GAAG,GAAG;AAEvD,MAAI,CAAC,WAAW;AACd,WAAO,eAAe,QAAQ,IAAI;AAAA,EACpC,WAAW,CAAC,aAAa;AACvB,WAAO,eAAe,UAAU,MAAM;AAAA,EACxC,WAAW,CAAC,aAAa;AACvB,WAAO,eAAe,UAAU,MAAM;AAAA,EACxC,WAAW,CAAC,kBAAkB;AAC5B,WAAO,eAAe,eAAe,WAAW;AAAA,EAClD,MAAO,QAAO;AAChB;;;AC7LO,SAAS,YAAY,GAAG;AAC7B,SAAO,OAAO,MAAM;AACtB;AAEO,SAAS,SAAS,GAAG;AAC1B,SAAO,OAAO,MAAM;AACtB;AAEO,SAAS,UAAU,GAAG;AAC3B,SAAO,OAAO,MAAM,YAAY,IAAI,MAAM;AAC5C;AAEO,SAAS,SAAS,GAAG;AAC1B,SAAO,OAAO,MAAM;AACtB;AAEO,SAAS,OAAO,GAAG;AACxB,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAIO,SAAS,cAAc;AAC5B,MAAI;AACF,WAAO,OAAO,SAAS,eAAe,CAAC,CAAC,KAAK;AAAA,EAC/C,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEO,SAAS,oBAAoB;AAClC,MAAI;AACF,WACE,OAAO,SAAS,eAChB,CAAC,CAAC,KAAK,WACN,cAAc,KAAK,OAAO,aAAa,iBAAiB,KAAK,OAAO;AAAA,EAEzE,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAIO,SAAS,WAAW,OAAO;AAChC,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AAEO,SAAS,OAAO,KAAK,IAAI,SAAS;AACvC,MAAI,IAAI,WAAW,GAAG;AACpB,WAAO;AAAA,EACT;AACA,SAAO,IAAI,OAAO,CAAC,MAAM,SAAS;AAChC,UAAM,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI;AAC5B,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT,WAAW,QAAQ,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,GAAG;AAChD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,GAAG,IAAI,EAAE,CAAC;AACZ;AAEO,SAAS,KAAK,KAAK,MAAM;AAC9B,SAAO,KAAK,OAAO,CAAC,GAAG,MAAM;AAC3B,MAAE,CAAC,IAAI,IAAI,CAAC;AACZ,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEO,SAAS,eAAe,KAAK,MAAM;AACxC,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI;AACvD;AAEO,SAAS,qBAAqB,UAAU;AAC7C,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT,WAAW,OAAO,aAAa,UAAU;AACvC,UAAM,IAAI,qBAAqB,iCAAiC;AAAA,EAClE,OAAO;AACL,QACE,CAAC,eAAe,SAAS,UAAU,GAAG,CAAC,KACvC,CAAC,eAAe,SAAS,aAAa,GAAG,CAAC,KAC1C,CAAC,MAAM,QAAQ,SAAS,OAAO,KAC/B,SAAS,QAAQ,KAAK,CAAC,MAAM,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC,GACrD;AACA,YAAM,IAAI,qBAAqB,uBAAuB;AAAA,IACxD;AACA,WAAO;AAAA,MACL,UAAU,SAAS;AAAA,MACnB,aAAa,SAAS;AAAA,MACtB,SAAS,MAAM,KAAK,SAAS,OAAO;AAAA,IACtC;AAAA,EACF;AACF;AAIO,SAAS,eAAe,OAAO,QAAQ,KAAK;AACjD,SAAO,UAAU,KAAK,KAAK,SAAS,UAAU,SAAS;AACzD;AAGO,SAAS,SAAS,GAAGC,IAAG;AAC7B,SAAO,IAAIA,KAAI,KAAK,MAAM,IAAIA,EAAC;AACjC;AAEO,SAAS,SAAS,OAAOA,KAAI,GAAG;AACrC,QAAM,QAAQ,QAAQ;AACtB,MAAI;AACJ,MAAI,OAAO;AACT,aAAS,OAAO,KAAK,CAAC,OAAO,SAASA,IAAG,GAAG;AAAA,EAC9C,OAAO;AACL,cAAU,KAAK,OAAO,SAASA,IAAG,GAAG;AAAA,EACvC;AACA,SAAO;AACT;AAEO,SAAS,aAAa,QAAQ;AACnC,MAAI,YAAY,MAAM,KAAK,WAAW,QAAQ,WAAW,IAAI;AAC3D,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,QAAQ,EAAE;AAAA,EAC5B;AACF;AAEO,SAAS,cAAc,QAAQ;AACpC,MAAI,YAAY,MAAM,KAAK,WAAW,QAAQ,WAAW,IAAI;AAC3D,WAAO;AAAA,EACT,OAAO;AACL,WAAO,WAAW,MAAM;AAAA,EAC1B;AACF;AAEO,SAAS,YAAY,UAAU;AAEpC,MAAI,YAAY,QAAQ,KAAK,aAAa,QAAQ,aAAa,IAAI;AACjE,WAAO;AAAA,EACT,OAAO;AACL,UAAM,IAAI,WAAW,OAAO,QAAQ,IAAI;AACxC,WAAO,KAAK,MAAM,CAAC;AAAA,EACrB;AACF;AAEO,SAAS,QAAQ,QAAQ,QAAQ,WAAW,SAAS;AAC1D,QAAM,SAAS,MAAM;AACrB,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,SAAS,IACZ,KAAK,KAAK,SAAS,MAAM,IAAI,SAC7B,KAAK,MAAM,SAAS,MAAM,IAAI;AAAA,IACpC,KAAK;AACH,aAAO,KAAK,MAAM,SAAS,MAAM,IAAI;AAAA,IACvC,KAAK;AACH,aAAO,KAAK,MAAM,SAAS,MAAM,IAAI;AAAA,IACvC,KAAK;AACH,aAAO,KAAK,MAAM,SAAS,MAAM,IAAI;AAAA,IACvC,KAAK;AACH,aAAO,KAAK,KAAK,SAAS,MAAM,IAAI;AAAA,IACtC;AACE,YAAM,IAAI,WAAW,kBAAkB,QAAQ,kBAAkB;AAAA,EACrE;AACF;AAIO,SAAS,WAAW,MAAM;AAC/B,SAAO,OAAO,MAAM,MAAM,OAAO,QAAQ,KAAK,OAAO,QAAQ;AAC/D;AAEO,SAAS,WAAW,MAAM;AAC/B,SAAO,WAAW,IAAI,IAAI,MAAM;AAClC;AAEO,SAAS,YAAY,MAAM,OAAO;AACvC,QAAM,WAAW,SAAS,QAAQ,GAAG,EAAE,IAAI,GACzC,UAAU,QAAQ,QAAQ,YAAY;AAExC,MAAI,aAAa,GAAG;AAClB,WAAO,WAAW,OAAO,IAAI,KAAK;AAAA,EACpC,OAAO;AACL,WAAO,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE,WAAW,CAAC;AAAA,EACxE;AACF;AAGO,SAAS,aAAa,KAAK;AAChC,MAAI,IAAI,KAAK;AAAA,IACX,IAAI;AAAA,IACJ,IAAI,QAAQ;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAGA,MAAI,IAAI,OAAO,OAAO,IAAI,QAAQ,GAAG;AACnC,QAAI,IAAI,KAAK,CAAC;AAId,MAAE,eAAe,IAAI,MAAM,IAAI,QAAQ,GAAG,IAAI,GAAG;AAAA,EACnD;AACA,SAAO,CAAC;AACV;AAGA,SAAS,gBAAgB,MAAM,oBAAoB,aAAa;AAC9D,QAAM,QAAQ,kBAAkB,UAAU,MAAM,GAAG,kBAAkB,GAAG,WAAW;AACnF,SAAO,CAAC,QAAQ,qBAAqB;AACvC;AAEO,SAAS,gBAAgB,UAAU,qBAAqB,GAAG,cAAc,GAAG;AACjF,QAAM,aAAa,gBAAgB,UAAU,oBAAoB,WAAW;AAC5E,QAAM,iBAAiB,gBAAgB,WAAW,GAAG,oBAAoB,WAAW;AACpF,UAAQ,WAAW,QAAQ,IAAI,aAAa,kBAAkB;AAChE;AAEO,SAAS,eAAe,MAAM;AACnC,MAAI,OAAO,IAAI;AACb,WAAO;AAAA,EACT,MAAO,QAAO,OAAO,SAAS,qBAAqB,OAAO,OAAO,MAAO;AAC1E;AAIO,SAAS,cAAc,IAAI,cAAc,QAAQ,WAAW,MAAM;AACvE,QAAM,OAAO,IAAI,KAAK,EAAE,GACtB,WAAW;AAAA,IACT,WAAW;AAAA,IACX,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,MAAM;AAAA,IACN,QAAQ;AAAA,EACV;AAEF,MAAI,UAAU;AACZ,aAAS,WAAW;AAAA,EACtB;AAEA,QAAM,WAAW,iBAAE,cAAc,gBAAiB;AAElD,QAAM,SAAS,IAAI,KAAK,eAAe,QAAQ,QAAQ,EACpD,cAAc,IAAI,EAClB,KAAK,CAAC,MAAM,EAAE,KAAK,YAAY,MAAM,cAAc;AACtD,SAAO,SAAS,OAAO,QAAQ;AACjC;AAGO,SAAS,aAAa,YAAY,cAAc;AACrD,MAAI,UAAU,SAAS,YAAY,EAAE;AAGrC,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,cAAU;AAAA,EACZ;AAEA,QAAM,SAAS,SAAS,cAAc,EAAE,KAAK,GAC3C,eAAe,UAAU,KAAK,OAAO,GAAG,SAAS,EAAE,IAAI,CAAC,SAAS;AACnE,SAAO,UAAU,KAAK;AACxB;AAIO,SAAS,SAAS,OAAO;AAC9B,QAAM,eAAe,OAAO,KAAK;AACjC,MAAI,OAAO,UAAU,aAAa,UAAU,MAAM,CAAC,OAAO,SAAS,YAAY;AAC7E,UAAM,IAAI,qBAAqB,sBAAsB,KAAK,EAAE;AAC9D,SAAO;AACT;AAEO,SAAS,gBAAgB,KAAK,YAAY;AAC/C,QAAM,aAAa,CAAC;AACpB,aAAW,KAAK,KAAK;AACnB,QAAI,eAAe,KAAK,CAAC,GAAG;AAC1B,YAAM,IAAI,IAAI,CAAC;AACf,UAAI,MAAM,UAAa,MAAM,KAAM;AACnC,iBAAW,WAAW,CAAC,CAAC,IAAI,SAAS,CAAC;AAAA,IACxC;AAAA,EACF;AACA,SAAO;AACT;AASO,SAAS,aAAaC,SAAQ,QAAQ;AAC3C,QAAM,QAAQ,KAAK,MAAM,KAAK,IAAIA,UAAS,EAAE,CAAC,GAC5C,UAAU,KAAK,MAAM,KAAK,IAAIA,UAAS,EAAE,CAAC,GAC1C,OAAOA,WAAU,IAAI,MAAM;AAE7B,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,GAAG,IAAI,GAAG,SAAS,OAAO,CAAC,CAAC,IAAI,SAAS,SAAS,CAAC,CAAC;AAAA,IAC7D,KAAK;AACH,aAAO,GAAG,IAAI,GAAG,KAAK,GAAG,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE;AAAA,IAC3D,KAAK;AACH,aAAO,GAAG,IAAI,GAAG,SAAS,OAAO,CAAC,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;AAAA,IAC5D;AACE,YAAM,IAAI,WAAW,gBAAgB,MAAM,sCAAsC;AAAA,EACrF;AACF;AAEO,SAAS,WAAW,KAAK;AAC9B,SAAO,KAAK,KAAK,CAAC,QAAQ,UAAU,UAAU,aAAa,CAAC;AAC9D;;;AC9TO,IAAM,aAAa;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAM,cAAc;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAM,eAAe,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAEhF,SAAS,OAAO,QAAQ;AAC7B,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,CAAC,GAAG,YAAY;AAAA,IACzB,KAAK;AACH,aAAO,CAAC,GAAG,WAAW;AAAA,IACxB,KAAK;AACH,aAAO,CAAC,GAAG,UAAU;AAAA,IACvB,KAAK;AACH,aAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAAA,IACvE,KAAK;AACH,aAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,IAChF;AACE,aAAO;AAAA,EACX;AACF;AAEO,IAAM,eAAe;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAM,gBAAgB,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAEtE,IAAM,iBAAiB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAEzD,SAAS,SAAS,QAAQ;AAC/B,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,CAAC,GAAG,cAAc;AAAA,IAC3B,KAAK;AACH,aAAO,CAAC,GAAG,aAAa;AAAA,IAC1B,KAAK;AACH,aAAO,CAAC,GAAG,YAAY;AAAA,IACzB,KAAK;AACH,aAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,IAC3C;AACE,aAAO;AAAA,EACX;AACF;AAEO,IAAM,YAAY,CAAC,MAAM,IAAI;AAE7B,IAAM,WAAW,CAAC,iBAAiB,aAAa;AAEhD,IAAM,YAAY,CAAC,MAAM,IAAI;AAE7B,IAAM,aAAa,CAAC,KAAK,GAAG;AAE5B,SAAS,KAAK,QAAQ;AAC3B,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,CAAC,GAAG,UAAU;AAAA,IACvB,KAAK;AACH,aAAO,CAAC,GAAG,SAAS;AAAA,IACtB,KAAK;AACH,aAAO,CAAC,GAAG,QAAQ;AAAA,IACrB;AACE,aAAO;AAAA,EACX;AACF;AAEO,SAAS,oBAAoB,IAAI;AACtC,SAAO,UAAU,GAAG,OAAO,KAAK,IAAI,CAAC;AACvC;AAEO,SAAS,mBAAmB,IAAI,QAAQ;AAC7C,SAAO,SAAS,MAAM,EAAE,GAAG,UAAU,CAAC;AACxC;AAEO,SAAS,iBAAiB,IAAI,QAAQ;AAC3C,SAAO,OAAO,MAAM,EAAE,GAAG,QAAQ,CAAC;AACpC;AAEO,SAAS,eAAe,IAAI,QAAQ;AACzC,SAAO,KAAK,MAAM,EAAE,GAAG,OAAO,IAAI,IAAI,CAAC;AACzC;AAEO,SAAS,mBAAmB,MAAM,OAAO,UAAU,UAAU,SAAS,OAAO;AAClF,QAAM,QAAQ;AAAA,IACZ,OAAO,CAAC,QAAQ,KAAK;AAAA,IACrB,UAAU,CAAC,WAAW,MAAM;AAAA,IAC5B,QAAQ,CAAC,SAAS,KAAK;AAAA,IACvB,OAAO,CAAC,QAAQ,KAAK;AAAA,IACrB,MAAM,CAAC,OAAO,OAAO,MAAM;AAAA,IAC3B,OAAO,CAAC,QAAQ,KAAK;AAAA,IACrB,SAAS,CAAC,UAAU,MAAM;AAAA,IAC1B,SAAS,CAAC,UAAU,MAAM;AAAA,EAC5B;AAEA,QAAM,WAAW,CAAC,SAAS,WAAW,SAAS,EAAE,QAAQ,IAAI,MAAM;AAEnE,MAAI,YAAY,UAAU,UAAU;AAClC,UAAM,QAAQ,SAAS;AACvB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO,QAAQ,aAAa,QAAQ,MAAM,IAAI,EAAE,CAAC,CAAC;AAAA,MACpD,KAAK;AACH,eAAO,QAAQ,cAAc,QAAQ,MAAM,IAAI,EAAE,CAAC,CAAC;AAAA,MACrD,KAAK;AACH,eAAO,QAAQ,UAAU,QAAQ,MAAM,IAAI,EAAE,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,WAAW,OAAO,GAAG,OAAO,EAAE,KAAK,QAAQ,GAC/C,WAAW,KAAK,IAAI,KAAK,GACzB,WAAW,aAAa,GACxB,WAAW,MAAM,IAAI,GACrB,UAAU,SACN,WACE,SAAS,CAAC,IACV,SAAS,CAAC,KAAK,SAAS,CAAC,IAC3B,WACA,MAAM,IAAI,EAAE,CAAC,IACb;AACN,SAAO,WAAW,GAAG,QAAQ,IAAI,OAAO,SAAS,MAAM,QAAQ,IAAI,OAAO;AAC5E;;;ACjKA,SAAS,gBAAgB,QAAQ,eAAe;AAC9C,MAAIC,KAAI;AACR,aAAW,SAAS,QAAQ;AAC1B,QAAI,MAAM,SAAS;AACjB,MAAAA,MAAK,MAAM;AAAA,IACb,OAAO;AACL,MAAAA,MAAK,cAAc,MAAM,GAAG;AAAA,IAC9B;AAAA,EACF;AACA,SAAOA;AACT;AAEA,IAAM,yBAAyB;AAAA,EAC7B,GAAW;AAAA,EACX,IAAY;AAAA,EACZ,KAAa;AAAA,EACb,MAAc;AAAA,EACd,GAAW;AAAA,EACX,IAAY;AAAA,EACZ,KAAa;AAAA,EACb,MAAc;AAAA,EACd,GAAW;AAAA,EACX,IAAY;AAAA,EACZ,KAAa;AAAA,EACb,MAAc;AAAA,EACd,GAAW;AAAA,EACX,IAAY;AAAA,EACZ,KAAa;AAAA,EACb,MAAc;AAAA,EACd,GAAW;AAAA,EACX,IAAY;AAAA,EACZ,KAAa;AAAA,EACb,MAAc;AAChB;AAMA,IAAqB,YAArB,MAAqB,WAAU;AAAA,EAC7B,OAAO,OAAO,QAAQ,OAAO,CAAC,GAAG;AAC/B,WAAO,IAAI,WAAU,QAAQ,IAAI;AAAA,EACnC;AAAA,EAEA,OAAO,YAAY,KAAK;AAItB,QAAI,UAAU,MACZ,cAAc,IACd,YAAY;AACd,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAM,IAAI,IAAI,OAAO,CAAC;AACtB,UAAI,MAAM,KAAK;AAEb,YAAI,YAAY,SAAS,KAAK,WAAW;AACvC,iBAAO,KAAK;AAAA,YACV,SAAS,aAAa,QAAQ,KAAK,WAAW;AAAA,YAC9C,KAAK,gBAAgB,KAAK,MAAM;AAAA,UAClC,CAAC;AAAA,QACH;AACA,kBAAU;AACV,sBAAc;AACd,oBAAY,CAAC;AAAA,MACf,WAAW,WAAW;AACpB,uBAAe;AAAA,MACjB,WAAW,MAAM,SAAS;AACxB,uBAAe;AAAA,MACjB,OAAO;AACL,YAAI,YAAY,SAAS,GAAG;AAC1B,iBAAO,KAAK,EAAE,SAAS,QAAQ,KAAK,WAAW,GAAG,KAAK,YAAY,CAAC;AAAA,QACtE;AACA,sBAAc;AACd,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,QAAI,YAAY,SAAS,GAAG;AAC1B,aAAO,KAAK,EAAE,SAAS,aAAa,QAAQ,KAAK,WAAW,GAAG,KAAK,YAAY,CAAC;AAAA,IACnF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,uBAAuB,OAAO;AACnC,WAAO,uBAAuB,KAAK;AAAA,EACrC;AAAA,EAEA,YAAY,QAAQ,YAAY;AAC9B,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,wBAAwB,IAAI,MAAM;AAChC,QAAI,KAAK,cAAc,MAAM;AAC3B,WAAK,YAAY,KAAK,IAAI,kBAAkB;AAAA,IAC9C;AACA,UAAM,KAAK,KAAK,UAAU,YAAY,IAAI,kCAAK,KAAK,OAAS,KAAM;AACnE,WAAO,GAAG,OAAO;AAAA,EACnB;AAAA,EAEA,YAAY,IAAI,OAAO,CAAC,GAAG;AACzB,WAAO,KAAK,IAAI,YAAY,IAAI,kCAAK,KAAK,OAAS,KAAM;AAAA,EAC3D;AAAA,EAEA,eAAe,IAAI,MAAM;AACvB,WAAO,KAAK,YAAY,IAAI,IAAI,EAAE,OAAO;AAAA,EAC3C;AAAA,EAEA,oBAAoB,IAAI,MAAM;AAC5B,WAAO,KAAK,YAAY,IAAI,IAAI,EAAE,cAAc;AAAA,EAClD;AAAA,EAEA,eAAe,UAAU,MAAM;AAC7B,UAAM,KAAK,KAAK,YAAY,SAAS,OAAO,IAAI;AAChD,WAAO,GAAG,IAAI,YAAY,SAAS,MAAM,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC;AAAA,EAC9E;AAAA,EAEA,gBAAgB,IAAI,MAAM;AACxB,WAAO,KAAK,YAAY,IAAI,IAAI,EAAE,gBAAgB;AAAA,EACpD;AAAA,EAEA,IAAIC,IAAG,IAAI,GAAG,cAAc,QAAW;AAErC,QAAI,KAAK,KAAK,aAAa;AACzB,aAAO,SAASA,IAAG,CAAC;AAAA,IACtB;AAEA,UAAM,OAAO,mBAAK,KAAK;AAEvB,QAAI,IAAI,GAAG;AACT,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,aAAa;AACf,WAAK,cAAc;AAAA,IACrB;AAEA,WAAO,KAAK,IAAI,gBAAgB,IAAI,EAAE,OAAOA,EAAC;AAAA,EAChD;AAAA,EAEA,yBAAyB,IAAI,KAAK;AAChC,UAAM,eAAe,KAAK,IAAI,YAAY,MAAM,MAC9C,uBAAuB,KAAK,IAAI,kBAAkB,KAAK,IAAI,mBAAmB,WAC9E,SAAS,CAAC,MAAM,YAAY,KAAK,IAAI,QAAQ,IAAI,MAAM,OAAO,GAC9DC,gBAAe,CAAC,SAAS;AACvB,UAAI,GAAG,iBAAiB,GAAG,WAAW,KAAK,KAAK,QAAQ;AACtD,eAAO;AAAA,MACT;AAEA,aAAO,GAAG,UAAU,GAAG,KAAK,aAAa,GAAG,IAAI,KAAK,MAAM,IAAI;AAAA,IACjE,GACA,WAAW,MACT,eACY,oBAAoB,EAAE,IAC9B,OAAO,EAAE,MAAM,WAAW,WAAW,MAAM,GAAG,WAAW,GAC/D,QAAQ,CAAC,QAAQ,eACf,eACY,iBAAiB,IAAI,MAAM,IACnC,OAAO,aAAa,EAAE,OAAO,OAAO,IAAI,EAAE,OAAO,QAAQ,KAAK,UAAU,GAAG,OAAO,GACxF,UAAU,CAAC,QAAQ,eACjB,eACY,mBAAmB,IAAI,MAAM,IACrC;AAAA,MACE,aAAa,EAAE,SAAS,OAAO,IAAI,EAAE,SAAS,QAAQ,OAAO,QAAQ,KAAK,UAAU;AAAA,MACpF;AAAA,IACF,GACN,aAAa,CAAC,UAAU;AACtB,YAAM,aAAa,WAAU,uBAAuB,KAAK;AACzD,UAAI,YAAY;AACd,eAAO,KAAK,wBAAwB,IAAI,UAAU;AAAA,MACpD,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,GACA,MAAM,CAAC,WACL,eAAuB,eAAe,IAAI,MAAM,IAAI,OAAO,EAAE,KAAK,OAAO,GAAG,KAAK,GACnF,gBAAgB,CAAC,UAAU;AAEzB,cAAQ,OAAO;AAAA,QAEb,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,WAAW;AAAA,QAChC,KAAK;AAAA,QAEL,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,aAAa,CAAC;AAAA,QAEnC,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,MAAM;AAAA,QAC3B,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,QAAQ,CAAC;AAAA,QAE9B,KAAK;AACH,iBAAO,KAAK,IAAI,KAAK,MAAM,GAAG,cAAc,EAAE,GAAG,CAAC;AAAA,QACpD,KAAK;AACH,iBAAO,KAAK,IAAI,KAAK,MAAM,GAAG,cAAc,GAAG,CAAC;AAAA,QAElD,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,MAAM;AAAA,QAC3B,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,QAAQ,CAAC;AAAA,QAE9B,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,OAAO,OAAO,IAAI,KAAK,GAAG,OAAO,EAAE;AAAA,QACxD,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,OAAO,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,CAAC;AAAA,QAC3D,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,IAAI;AAAA,QACzB,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,QAE5B,KAAK;AAEH,iBAAOA,cAAa,EAAE,QAAQ,UAAU,QAAQ,KAAK,KAAK,OAAO,CAAC;AAAA,QACpE,KAAK;AAEH,iBAAOA,cAAa,EAAE,QAAQ,SAAS,QAAQ,KAAK,KAAK,OAAO,CAAC;AAAA,QACnE,KAAK;AAEH,iBAAOA,cAAa,EAAE,QAAQ,UAAU,QAAQ,KAAK,KAAK,OAAO,CAAC;AAAA,QACpE,KAAK;AAEH,iBAAO,GAAG,KAAK,WAAW,GAAG,IAAI,EAAE,QAAQ,SAAS,QAAQ,KAAK,IAAI,OAAO,CAAC;AAAA,QAC/E,KAAK;AAEH,iBAAO,GAAG,KAAK,WAAW,GAAG,IAAI,EAAE,QAAQ,QAAQ,QAAQ,KAAK,IAAI,OAAO,CAAC;AAAA,QAE9E,KAAK;AAEH,iBAAO,GAAG;AAAA,QAEZ,KAAK;AACH,iBAAO,SAAS;AAAA,QAElB,KAAK;AACH,iBAAO,uBAAuB,OAAO,EAAE,KAAK,UAAU,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG;AAAA,QACnF,KAAK;AACH,iBAAO,uBAAuB,OAAO,EAAE,KAAK,UAAU,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC;AAAA,QAEtF,KAAK;AAEH,iBAAO,KAAK,IAAI,GAAG,OAAO;AAAA,QAC5B,KAAK;AAEH,iBAAO,QAAQ,SAAS,IAAI;AAAA,QAC9B,KAAK;AAEH,iBAAO,QAAQ,QAAQ,IAAI;AAAA,QAC7B,KAAK;AAEH,iBAAO,QAAQ,UAAU,IAAI;AAAA,QAE/B,KAAK;AAEH,iBAAO,KAAK,IAAI,GAAG,OAAO;AAAA,QAC5B,KAAK;AAEH,iBAAO,QAAQ,SAAS,KAAK;AAAA,QAC/B,KAAK;AAEH,iBAAO,QAAQ,QAAQ,KAAK;AAAA,QAC9B,KAAK;AAEH,iBAAO,QAAQ,UAAU,KAAK;AAAA,QAEhC,KAAK;AAEH,iBAAO,uBACH,OAAO,EAAE,OAAO,WAAW,KAAK,UAAU,GAAG,OAAO,IACpD,KAAK,IAAI,GAAG,KAAK;AAAA,QACvB,KAAK;AAEH,iBAAO,uBACH,OAAO,EAAE,OAAO,WAAW,KAAK,UAAU,GAAG,OAAO,IACpD,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,QAC1B,KAAK;AAEH,iBAAO,MAAM,SAAS,IAAI;AAAA,QAC5B,KAAK;AAEH,iBAAO,MAAM,QAAQ,IAAI;AAAA,QAC3B,KAAK;AAEH,iBAAO,MAAM,UAAU,IAAI;AAAA,QAE7B,KAAK;AAEH,iBAAO,uBACH,OAAO,EAAE,OAAO,UAAU,GAAG,OAAO,IACpC,KAAK,IAAI,GAAG,KAAK;AAAA,QACvB,KAAK;AAEH,iBAAO,uBACH,OAAO,EAAE,OAAO,UAAU,GAAG,OAAO,IACpC,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,QAC1B,KAAK;AAEH,iBAAO,MAAM,SAAS,KAAK;AAAA,QAC7B,KAAK;AAEH,iBAAO,MAAM,QAAQ,KAAK;AAAA,QAC5B,KAAK;AAEH,iBAAO,MAAM,UAAU,KAAK;AAAA,QAE9B,KAAK;AAEH,iBAAO,uBAAuB,OAAO,EAAE,MAAM,UAAU,GAAG,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAAA,QACtF,KAAK;AAEH,iBAAO,uBACH,OAAO,EAAE,MAAM,UAAU,GAAG,MAAM,IAClC,KAAK,IAAI,GAAG,KAAK,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC;AAAA,QAC9C,KAAK;AAEH,iBAAO,uBACH,OAAO,EAAE,MAAM,UAAU,GAAG,MAAM,IAClC,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,QACzB,KAAK;AAEH,iBAAO,uBACH,OAAO,EAAE,MAAM,UAAU,GAAG,MAAM,IAClC,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,QAEzB,KAAK;AAEH,iBAAO,IAAI,OAAO;AAAA,QACpB,KAAK;AAEH,iBAAO,IAAI,MAAM;AAAA,QACnB,KAAK;AACH,iBAAO,IAAI,QAAQ;AAAA,QACrB,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,SAAS,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC;AAAA,QACrD,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,UAAU,CAAC;AAAA,QAChC,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,UAAU;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,YAAY,CAAC;AAAA,QAClC,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,eAAe;AAAA,QACpC,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,iBAAiB,CAAC;AAAA,QACvC,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,cAAc,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC;AAAA,QAC1D,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,eAAe,CAAC;AAAA,QACrC,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,OAAO;AAAA,QAC5B,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,SAAS,CAAC;AAAA,QAC/B,KAAK;AAEH,iBAAO,KAAK,IAAI,GAAG,OAAO;AAAA,QAC5B,KAAK;AAEH,iBAAO,KAAK,IAAI,GAAG,SAAS,CAAC;AAAA,QAC/B,KAAK;AACH,iBAAO,KAAK,IAAI,KAAK,MAAM,GAAG,KAAK,GAAI,CAAC;AAAA,QAC1C,KAAK;AACH,iBAAO,KAAK,IAAI,GAAG,EAAE;AAAA,QACvB;AACE,iBAAO,WAAW,KAAK;AAAA,MAC3B;AAAA,IACF;AAEF,WAAO,gBAAgB,WAAU,YAAY,GAAG,GAAG,aAAa;AAAA,EAClE;AAAA,EAEA,yBAAyB,KAAK,KAAK;AACjC,UAAM,gBAAgB,KAAK,KAAK,aAAa,wBAAwB,KAAK;AAC1E,UAAM,eAAe,CAAC,UAAU;AAC5B,cAAQ,MAAM,CAAC,GAAG;AAAA,QAChB,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF,GACA,gBAAgB,CAAC,QAAQ,SAAS,CAAC,UAAU;AAC3C,YAAM,SAAS,aAAa,KAAK;AACjC,UAAI,QAAQ;AACV,cAAM,kBACJ,KAAK,sBAAsB,WAAW,KAAK,cAAc,gBAAgB;AAC3E,YAAI;AACJ,YAAI,KAAK,KAAK,aAAa,yBAAyB,WAAW,KAAK,aAAa;AAC/E,wBAAc;AAAA,QAChB,WAAW,KAAK,KAAK,aAAa,OAAO;AACvC,wBAAc;AAAA,QAChB,OAAO;AAEL,wBAAc;AAAA,QAChB;AACA,eAAO,KAAK,IAAI,OAAO,IAAI,MAAM,IAAI,iBAAiB,MAAM,QAAQ,WAAW;AAAA,MACjF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,GACA,SAAS,WAAU,YAAY,GAAG,GAClC,aAAa,OAAO;AAAA,MAClB,CAAC,OAAO,EAAE,SAAS,IAAI,MAAO,UAAU,QAAQ,MAAM,OAAO,GAAG;AAAA,MAChE,CAAC;AAAA,IACH,GACA,YAAY,IAAI,QAAQ,GAAG,WAAW,IAAI,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GACxE,eAAe;AAAA,MACb,oBAAoB,YAAY;AAAA;AAAA;AAAA,MAGhC,aAAa,OAAO,KAAK,UAAU,MAAM,EAAE,CAAC;AAAA,IAC9C;AACF,WAAO,gBAAgB,QAAQ,cAAc,WAAW,YAAY,CAAC;AAAA,EACvE;AACF;;;AC3ZA,IAAM,YAAY;AAElB,SAAS,kBAAkB,SAAS;AAClC,QAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,MAAM,IAAI,EAAE,QAAQ,EAAE;AACtD,SAAO,OAAO,IAAI,IAAI,GAAG;AAC3B;AAEA,SAAS,qBAAqB,YAAY;AACxC,SAAO,CAAC,MACN,WACG;AAAA,IACC,CAAC,CAAC,YAAY,YAAY,MAAM,GAAG,OAAO;AACxC,YAAM,CAAC,KAAK,MAAM,IAAI,IAAI,GAAG,GAAG,MAAM;AACtC,aAAO,CAAC,kCAAK,aAAe,MAAO,QAAQ,YAAY,IAAI;AAAA,IAC7D;AAAA,IACA,CAAC,CAAC,GAAG,MAAM,CAAC;AAAA,EACd,EACC,MAAM,GAAG,CAAC;AACjB;AAEA,SAAS,MAAMC,OAAM,UAAU;AAC7B,MAAIA,MAAK,MAAM;AACb,WAAO,CAAC,MAAM,IAAI;AAAA,EACpB;AAEA,aAAW,CAAC,OAAO,SAAS,KAAK,UAAU;AACzC,UAAM,IAAI,MAAM,KAAKA,EAAC;AACtB,QAAI,GAAG;AACL,aAAO,UAAU,CAAC;AAAA,IACpB;AAAA,EACF;AACA,SAAO,CAAC,MAAM,IAAI;AACpB;AAEA,SAAS,eAAe,MAAM;AAC5B,SAAO,CAACC,QAAO,WAAW;AACxB,UAAM,MAAM,CAAC;AACb,QAAI;AAEJ,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,UAAI,KAAK,CAAC,CAAC,IAAI,aAAaA,OAAM,SAAS,CAAC,CAAC;AAAA,IAC/C;AACA,WAAO,CAAC,KAAK,MAAM,SAAS,CAAC;AAAA,EAC/B;AACF;AAGA,IAAM,cAAc;AACpB,IAAM,kBAAkB,MAAM,YAAY,MAAM,WAAW,UAAU,MAAM;AAC3E,IAAM,mBAAmB;AACzB,IAAM,eAAe,OAAO,GAAG,iBAAiB,MAAM,GAAG,eAAe,EAAE;AAC1E,IAAM,wBAAwB,OAAO,UAAU,aAAa,MAAM,IAAI;AACtE,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,qBAAqB,YAAY,YAAY,cAAc,SAAS;AAC1E,IAAM,wBAAwB,YAAY,QAAQ,SAAS;AAC3D,IAAM,cAAc;AACpB,IAAM,eAAe;AAAA,EACnB,GAAG,iBAAiB,MAAM,QAAQ,YAAY,MAAM,KAAK,UAAU,MAAM;AAC3E;AACA,IAAM,wBAAwB,OAAO,OAAO,aAAa,MAAM,IAAI;AAEnE,SAAS,IAAIA,QAAO,KAAK,UAAU;AACjC,QAAM,IAAIA,OAAM,GAAG;AACnB,SAAO,YAAY,CAAC,IAAI,WAAW,aAAa,CAAC;AACnD;AAEA,SAAS,cAAcA,QAAO,QAAQ;AACpC,QAAM,OAAO;AAAA,IACX,MAAM,IAAIA,QAAO,MAAM;AAAA,IACvB,OAAO,IAAIA,QAAO,SAAS,GAAG,CAAC;AAAA,IAC/B,KAAK,IAAIA,QAAO,SAAS,GAAG,CAAC;AAAA,EAC/B;AAEA,SAAO,CAAC,MAAM,MAAM,SAAS,CAAC;AAChC;AAEA,SAAS,eAAeA,QAAO,QAAQ;AACrC,QAAM,OAAO;AAAA,IACX,OAAO,IAAIA,QAAO,QAAQ,CAAC;AAAA,IAC3B,SAAS,IAAIA,QAAO,SAAS,GAAG,CAAC;AAAA,IACjC,SAAS,IAAIA,QAAO,SAAS,GAAG,CAAC;AAAA,IACjC,cAAc,YAAYA,OAAM,SAAS,CAAC,CAAC;AAAA,EAC7C;AAEA,SAAO,CAAC,MAAM,MAAM,SAAS,CAAC;AAChC;AAEA,SAAS,iBAAiBA,QAAO,QAAQ;AACvC,QAAM,QAAQ,CAACA,OAAM,MAAM,KAAK,CAACA,OAAM,SAAS,CAAC,GAC/C,aAAa,aAAaA,OAAM,SAAS,CAAC,GAAGA,OAAM,SAAS,CAAC,CAAC,GAC9D,OAAO,QAAQ,OAAO,gBAAgB,SAAS,UAAU;AAC3D,SAAO,CAAC,CAAC,GAAG,MAAM,SAAS,CAAC;AAC9B;AAEA,SAAS,gBAAgBA,QAAO,QAAQ;AACtC,QAAM,OAAOA,OAAM,MAAM,IAAI,SAAS,OAAOA,OAAM,MAAM,CAAC,IAAI;AAC9D,SAAO,CAAC,CAAC,GAAG,MAAM,SAAS,CAAC;AAC9B;AAIA,IAAM,cAAc,OAAO,MAAM,iBAAiB,MAAM,GAAG;AAI3D,IAAM,cACJ;AAEF,SAAS,mBAAmBA,QAAO;AACjC,QAAM,CAACD,IAAG,SAAS,UAAU,SAAS,QAAQ,SAAS,WAAW,WAAW,eAAe,IAC1FC;AAEF,QAAM,oBAAoBD,GAAE,CAAC,MAAM;AACnC,QAAM,kBAAkB,aAAa,UAAU,CAAC,MAAM;AAEtD,QAAM,cAAc,CAAC,KAAK,QAAQ,UAChC,QAAQ,WAAc,SAAU,OAAO,qBAAsB,CAAC,MAAM;AAEtE,SAAO;AAAA,IACL;AAAA,MACE,OAAO,YAAY,cAAc,OAAO,CAAC;AAAA,MACzC,QAAQ,YAAY,cAAc,QAAQ,CAAC;AAAA,MAC3C,OAAO,YAAY,cAAc,OAAO,CAAC;AAAA,MACzC,MAAM,YAAY,cAAc,MAAM,CAAC;AAAA,MACvC,OAAO,YAAY,cAAc,OAAO,CAAC;AAAA,MACzC,SAAS,YAAY,cAAc,SAAS,CAAC;AAAA,MAC7C,SAAS,YAAY,cAAc,SAAS,GAAG,cAAc,IAAI;AAAA,MACjE,cAAc,YAAY,YAAY,eAAe,GAAG,eAAe;AAAA,IACzE;AAAA,EACF;AACF;AAKA,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AACZ;AAEA,SAAS,YAAY,YAAY,SAAS,UAAU,QAAQ,SAAS,WAAW,WAAW;AACzF,QAAM,SAAS;AAAA,IACb,MAAM,QAAQ,WAAW,IAAI,eAAe,aAAa,OAAO,CAAC,IAAI,aAAa,OAAO;AAAA,IACzF,OAAe,YAAY,QAAQ,QAAQ,IAAI;AAAA,IAC/C,KAAK,aAAa,MAAM;AAAA,IACxB,MAAM,aAAa,OAAO;AAAA,IAC1B,QAAQ,aAAa,SAAS;AAAA,EAChC;AAEA,MAAI,UAAW,QAAO,SAAS,aAAa,SAAS;AACrD,MAAI,YAAY;AACd,WAAO,UACL,WAAW,SAAS,IACR,aAAa,QAAQ,UAAU,IAAI,IACnC,cAAc,QAAQ,UAAU,IAAI;AAAA,EACpD;AAEA,SAAO;AACT;AAGA,IAAM,UACJ;AAEF,SAAS,eAAeC,QAAO;AAC7B,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIA,QACJ,SAAS,YAAY,YAAY,SAAS,UAAU,QAAQ,SAAS,WAAW,SAAS;AAE3F,MAAIC;AACJ,MAAI,WAAW;AACb,IAAAA,UAAS,WAAW,SAAS;AAAA,EAC/B,WAAW,WAAW;AACpB,IAAAA,UAAS;AAAA,EACX,OAAO;AACL,IAAAA,UAAS,aAAa,YAAY,YAAY;AAAA,EAChD;AAEA,SAAO,CAAC,QAAQ,IAAI,gBAAgBA,OAAM,CAAC;AAC7C;AAEA,SAAS,kBAAkBF,IAAG;AAE5B,SAAOA,GACJ,QAAQ,sBAAsB,GAAG,EACjC,QAAQ,YAAY,GAAG,EACvB,KAAK;AACV;AAIA,IAAM,UACF;AADJ,IAEE,SACE;AAHJ,IAIE,QACE;AAEJ,SAAS,oBAAoBC,QAAO;AAClC,QAAM,CAAC,EAAE,YAAY,QAAQ,UAAU,SAAS,SAAS,WAAW,SAAS,IAAIA,QAC/E,SAAS,YAAY,YAAY,SAAS,UAAU,QAAQ,SAAS,WAAW,SAAS;AAC3F,SAAO,CAAC,QAAQ,gBAAgB,WAAW;AAC7C;AAEA,SAAS,aAAaA,QAAO;AAC3B,QAAM,CAAC,EAAE,YAAY,UAAU,QAAQ,SAAS,WAAW,WAAW,OAAO,IAAIA,QAC/E,SAAS,YAAY,YAAY,SAAS,UAAU,QAAQ,SAAS,WAAW,SAAS;AAC3F,SAAO,CAAC,QAAQ,gBAAgB,WAAW;AAC7C;AAEA,IAAM,+BAA+B,eAAe,aAAa,qBAAqB;AACtF,IAAM,gCAAgC,eAAe,cAAc,qBAAqB;AACxF,IAAM,mCAAmC,eAAe,iBAAiB,qBAAqB;AAC9F,IAAM,uBAAuB,eAAe,YAAY;AAExD,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AACF;AAMO,SAAS,aAAaD,IAAG;AAC9B,SAAO;AAAA,IACLA;AAAA,IACA,CAAC,8BAA8B,0BAA0B;AAAA,IACzD,CAAC,+BAA+B,2BAA2B;AAAA,IAC3D,CAAC,kCAAkC,4BAA4B;AAAA,IAC/D,CAAC,sBAAsB,uBAAuB;AAAA,EAChD;AACF;AAEO,SAAS,iBAAiBA,IAAG;AAClC,SAAO,MAAM,kBAAkBA,EAAC,GAAG,CAAC,SAAS,cAAc,CAAC;AAC9D;AAEO,SAAS,cAAcA,IAAG;AAC/B,SAAO;AAAA,IACLA;AAAA,IACA,CAAC,SAAS,mBAAmB;AAAA,IAC7B,CAAC,QAAQ,mBAAmB;AAAA,IAC5B,CAAC,OAAO,YAAY;AAAA,EACtB;AACF;AAEO,SAAS,iBAAiBA,IAAG;AAClC,SAAO,MAAMA,IAAG,CAAC,aAAa,kBAAkB,CAAC;AACnD;AAEA,IAAM,qBAAqB,kBAAkB,cAAc;AAEpD,SAAS,iBAAiBA,IAAG;AAClC,SAAO,MAAMA,IAAG,CAAC,aAAa,kBAAkB,CAAC;AACnD;AAEA,IAAM,+BAA+B,eAAe,aAAa,qBAAqB;AACtF,IAAM,uBAAuB,eAAe,YAAY;AAExD,IAAM,kCAAkC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF;AAEO,SAAS,SAASA,IAAG;AAC1B,SAAO;AAAA,IACLA;AAAA,IACA,CAAC,8BAA8B,0BAA0B;AAAA,IACzD,CAAC,sBAAsB,+BAA+B;AAAA,EACxD;AACF;;;AC9TA,IAAM,UAAU;AAGT,IAAM,iBAAiB;AAAA,EAC1B,OAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO,IAAI;AAAA,IACX,SAAS,IAAI,KAAK;AAAA,IAClB,SAAS,IAAI,KAAK,KAAK;AAAA,IACvB,cAAc,IAAI,KAAK,KAAK,KAAK;AAAA,EACnC;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,SAAS,KAAK;AAAA,IACd,SAAS,KAAK,KAAK;AAAA,IACnB,cAAc,KAAK,KAAK,KAAK;AAAA,EAC/B;AAAA,EACA,OAAO,EAAE,SAAS,IAAI,SAAS,KAAK,IAAI,cAAc,KAAK,KAAK,IAAK;AAAA,EACrE,SAAS,EAAE,SAAS,IAAI,cAAc,KAAK,IAAK;AAAA,EAChD,SAAS,EAAE,cAAc,IAAK;AAChC;AAjBK,IAkBL,eAAe;AAAA,EACb,OAAO;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO,MAAM;AAAA,IACb,SAAS,MAAM,KAAK;AAAA,IACpB,SAAS,MAAM,KAAK,KAAK;AAAA,IACzB,cAAc,MAAM,KAAK,KAAK,KAAK;AAAA,EACrC;AAAA,EACA,UAAU;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO,KAAK;AAAA,IACZ,SAAS,KAAK,KAAK;AAAA,IACnB,SAAS,KAAK,KAAK,KAAK;AAAA,IACxB,cAAc,KAAK,KAAK,KAAK,KAAK;AAAA,EACpC;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO,KAAK;AAAA,IACZ,SAAS,KAAK,KAAK;AAAA,IACnB,SAAS,KAAK,KAAK,KAAK;AAAA,IACxB,cAAc,KAAK,KAAK,KAAK,KAAK;AAAA,EACpC;AAAA,GAEG;AA/CA,IAiDL,qBAAqB,SAAW;AAjD3B,IAkDL,sBAAsB,SAAW;AAlD5B,IAmDL,iBAAiB;AAAA,EACf,OAAO;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO,qBAAqB;AAAA,IAC5B,MAAM;AAAA,IACN,OAAO,qBAAqB;AAAA,IAC5B,SAAS,qBAAqB,KAAK;AAAA,IACnC,SAAS,qBAAqB,KAAK,KAAK;AAAA,IACxC,cAAc,qBAAqB,KAAK,KAAK,KAAK;AAAA,EACpD;AAAA,EACA,UAAU;AAAA,IACR,QAAQ;AAAA,IACR,OAAO,qBAAqB;AAAA,IAC5B,MAAM,qBAAqB;AAAA,IAC3B,OAAQ,qBAAqB,KAAM;AAAA,IACnC,SAAU,qBAAqB,KAAK,KAAM;AAAA,IAC1C,SAAU,qBAAqB,KAAK,KAAK,KAAM;AAAA,IAC/C,cAAe,qBAAqB,KAAK,KAAK,KAAK,MAAQ;AAAA,EAC7D;AAAA,EACA,QAAQ;AAAA,IACN,OAAO,sBAAsB;AAAA,IAC7B,MAAM;AAAA,IACN,OAAO,sBAAsB;AAAA,IAC7B,SAAS,sBAAsB,KAAK;AAAA,IACpC,SAAS,sBAAsB,KAAK,KAAK;AAAA,IACzC,cAAc,sBAAsB,KAAK,KAAK,KAAK;AAAA,EACrD;AAAA,GACG;AAIP,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,eAAe,aAAa,MAAM,CAAC,EAAE,QAAQ;AAGnD,SAAS,MAAM,KAAK,MAAM,QAAQ,OAAO;AAEvC,QAAM,OAAO;AAAA,IACX,QAAQ,QAAQ,KAAK,SAAS,kCAAK,IAAI,SAAY,KAAK,UAAU,CAAC;AAAA,IACnE,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG;AAAA,IAC3B,oBAAoB,KAAK,sBAAsB,IAAI;AAAA,IACnD,QAAQ,KAAK,UAAU,IAAI;AAAA,EAC7B;AACA,SAAO,IAAI,SAAS,IAAI;AAC1B;AAEA,SAAS,iBAAiB,QAAQ,MAAM;AACtC,MAAI,MAAM,KAAK,gBAAgB;AAC/B,aAAW,QAAQ,aAAa,MAAM,CAAC,GAAG;AACxC,QAAI,KAAK,IAAI,GAAG;AACd,aAAO,KAAK,IAAI,IAAI,OAAO,IAAI,EAAE,cAAc;AAAA,IACjD;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,gBAAgB,QAAQ,MAAM;AAGrC,QAAM,SAAS,iBAAiB,QAAQ,IAAI,IAAI,IAAI,KAAK;AAEzD,eAAa,YAAY,CAAC,UAAU,YAAY;AAC9C,QAAI,CAAC,YAAY,KAAK,OAAO,CAAC,GAAG;AAC/B,UAAI,UAAU;AACZ,cAAM,cAAc,KAAK,QAAQ,IAAI;AACrC,cAAM,OAAO,OAAO,OAAO,EAAE,QAAQ;AAiBrC,cAAM,SAAS,KAAK,MAAM,cAAc,IAAI;AAC5C,aAAK,OAAO,KAAK,SAAS;AAC1B,aAAK,QAAQ,KAAK,SAAS,OAAO;AAAA,MACpC;AACA,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,GAAG,IAAI;AAIP,eAAa,OAAO,CAAC,UAAU,YAAY;AACzC,QAAI,CAAC,YAAY,KAAK,OAAO,CAAC,GAAG;AAC/B,UAAI,UAAU;AACZ,cAAM,WAAW,KAAK,QAAQ,IAAI;AAClC,aAAK,QAAQ,KAAK;AAClB,aAAK,OAAO,KAAK,WAAW,OAAO,QAAQ,EAAE,OAAO;AAAA,MACtD;AACA,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,GAAG,IAAI;AACT;AAGA,SAAS,aAAa,MAAM;AAC1B,QAAM,UAAU,CAAC;AACjB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC/C,QAAI,UAAU,GAAG;AACf,cAAQ,GAAG,IAAI;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AAeA,IAAqB,WAArB,MAAqB,UAAS;AAAA;AAAA;AAAA;AAAA,EAI5B,YAAY,QAAQ;AAClB,UAAM,WAAW,OAAO,uBAAuB,cAAc;AAC7D,QAAI,SAAS,WAAW,iBAAiB;AAEzC,QAAI,OAAO,QAAQ;AACjB,eAAS,OAAO;AAAA,IAClB;AAKA,SAAK,SAAS,OAAO;AAIrB,SAAK,MAAM,OAAO,OAAO,OAAO,OAAO;AAIvC,SAAK,qBAAqB,WAAW,aAAa;AAIlD,SAAK,UAAU,OAAO,WAAW;AAIjC,SAAK,SAAS;AAId,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,WAAW,OAAO,MAAM;AAC7B,WAAO,UAAS,WAAW,EAAE,cAAc,MAAM,GAAG,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,OAAO,WAAW,KAAK,OAAO,CAAC,GAAG;AAChC,QAAI,OAAO,QAAQ,OAAO,QAAQ,UAAU;AAC1C,YAAM,IAAI;AAAA,QACR,+DACE,QAAQ,OAAO,SAAS,OAAO,GACjC;AAAA,MACF;AAAA,IACF;AAEA,WAAO,IAAI,UAAS;AAAA,MAClB,QAAQ,gBAAgB,KAAK,UAAS,aAAa;AAAA,MACnD,KAAK,OAAO,WAAW,IAAI;AAAA,MAC3B,oBAAoB,KAAK;AAAA,MACzB,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,iBAAiB,cAAc;AACpC,QAAI,SAAS,YAAY,GAAG;AAC1B,aAAO,UAAS,WAAW,YAAY;AAAA,IACzC,WAAW,UAAS,WAAW,YAAY,GAAG;AAC5C,aAAO;AAAA,IACT,WAAW,OAAO,iBAAiB,UAAU;AAC3C,aAAO,UAAS,WAAW,YAAY;AAAA,IACzC,OAAO;AACL,YAAM,IAAI;AAAA,QACR,6BAA6B,YAAY,YAAY,OAAO,YAAY;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,QAAQ,MAAM,MAAM;AACzB,UAAM,CAAC,MAAM,IAAI,iBAAiB,IAAI;AACtC,QAAI,QAAQ;AACV,aAAO,UAAS,WAAW,QAAQ,IAAI;AAAA,IACzC,OAAO;AACL,aAAO,UAAS,QAAQ,cAAc,cAAc,IAAI,+BAA+B;AAAA,IACzF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,YAAY,MAAM,MAAM;AAC7B,UAAM,CAAC,MAAM,IAAI,iBAAiB,IAAI;AACtC,QAAI,QAAQ;AACV,aAAO,UAAS,WAAW,QAAQ,IAAI;AAAA,IACzC,OAAO;AACL,aAAO,UAAS,QAAQ,cAAc,cAAc,IAAI,+BAA+B;AAAA,IACzF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QAAQ,QAAQ,cAAc,MAAM;AACzC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,qBAAqB,kDAAkD;AAAA,IACnF;AAEA,UAAM,UAAU,kBAAkB,UAAU,SAAS,IAAI,QAAQ,QAAQ,WAAW;AAEpF,QAAI,SAAS,gBAAgB;AAC3B,YAAM,IAAI,qBAAqB,OAAO;AAAA,IACxC,OAAO;AACL,aAAO,IAAI,UAAS,EAAE,QAAQ,CAAC;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,cAAc,MAAM;AACzB,UAAM,aAAa;AAAA,MACjB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,aAAa;AAAA,MACb,cAAc;AAAA,IAChB,EAAE,OAAO,KAAK,YAAY,IAAI,IAAI;AAElC,QAAI,CAAC,WAAY,OAAM,IAAI,iBAAiB,IAAI;AAEhD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,WAAW,GAAG;AACnB,WAAQ,KAAK,EAAE,mBAAoB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,KAAK,IAAI,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,kBAAkB;AACpB,WAAO,KAAK,UAAU,KAAK,IAAI,kBAAkB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,SAAS,KAAK,OAAO,CAAC,GAAG;AAEvB,UAAM,UAAU,iCACX,OADW;AAAA,MAEd,OAAO,KAAK,UAAU,SAAS,KAAK,UAAU;AAAA,IAChD;AACA,WAAO,KAAK,UACR,UAAU,OAAO,KAAK,KAAK,OAAO,EAAE,yBAAyB,MAAM,GAAG,IACtE;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,QAAQ,OAAO,CAAC,GAAG;AACjB,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,YAAY,KAAK,cAAc;AAErC,UAAMG,KAAI,aACP,IAAI,CAAC,SAAS;AACb,YAAM,MAAM,KAAK,OAAO,IAAI;AAC5B,UAAI,YAAY,GAAG,KAAM,QAAQ,KAAK,CAAC,WAAY;AACjD,eAAO;AAAA,MACT;AACA,aAAO,KAAK,IACT,gBAAgB,+BAAE,OAAO,QAAQ,aAAa,UAAW,OAAzC,EAA+C,MAAM,KAAK,MAAM,GAAG,EAAE,EAAE,EAAC,EACxF,OAAO,GAAG;AAAA,IACf,CAAC,EACA,OAAO,CAACC,OAAMA,EAAC;AAElB,WAAO,KAAK,IACT,cAAc,iBAAE,MAAM,eAAe,OAAO,KAAK,aAAa,YAAa,KAAM,EACjF,OAAOD,EAAC;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,QAAI,CAAC,KAAK,QAAS,QAAO,CAAC;AAC3B,WAAO,mBAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,QAAQ;AAEN,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,QAAIE,KAAI;AACR,QAAI,KAAK,UAAU,EAAG,CAAAA,MAAK,KAAK,QAAQ;AACxC,QAAI,KAAK,WAAW,KAAK,KAAK,aAAa,EAAG,CAAAA,MAAK,KAAK,SAAS,KAAK,WAAW,IAAI;AACrF,QAAI,KAAK,UAAU,EAAG,CAAAA,MAAK,KAAK,QAAQ;AACxC,QAAI,KAAK,SAAS,EAAG,CAAAA,MAAK,KAAK,OAAO;AACtC,QAAI,KAAK,UAAU,KAAK,KAAK,YAAY,KAAK,KAAK,YAAY,KAAK,KAAK,iBAAiB;AACxF,MAAAA,MAAK;AACP,QAAI,KAAK,UAAU,EAAG,CAAAA,MAAK,KAAK,QAAQ;AACxC,QAAI,KAAK,YAAY,EAAG,CAAAA,MAAK,KAAK,UAAU;AAC5C,QAAI,KAAK,YAAY,KAAK,KAAK,iBAAiB;AAG9C,MAAAA,MAAK,QAAQ,KAAK,UAAU,KAAK,eAAe,KAAM,CAAC,IAAI;AAC7D,QAAIA,OAAM,IAAK,CAAAA,MAAK;AACpB,WAAOA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,UAAU,OAAO,CAAC,GAAG;AACnB,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,SAAS,KAAK,SAAS;AAC7B,QAAI,SAAS,KAAK,UAAU,MAAU,QAAO;AAE7C,WAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,QAAQ;AAAA,OACL,OALE;AAAA,MAML,eAAe;AAAA,IACjB;AAEA,UAAM,WAAW,SAAS,WAAW,QAAQ,EAAE,MAAM,MAAM,CAAC;AAC5D,WAAO,SAAS,UAAU,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,CAAC,OAAO,IAAI,4BAA4B,CAAC,IAAI;AAC3C,QAAI,KAAK,SAAS;AAChB,aAAO,sBAAsB,KAAK,UAAU,KAAK,MAAM,CAAC;AAAA,IAC1D,OAAO;AACL,aAAO,+BAA+B,KAAK,aAAa;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,WAAO,iBAAiB,KAAK,QAAQ,KAAK,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,UAAU;AACb,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,MAAM,UAAS,iBAAiB,QAAQ,GAC5C,SAAS,CAAC;AAEZ,eAAW,KAAK,cAAc;AAC5B,UAAI,eAAe,IAAI,QAAQ,CAAC,KAAK,eAAe,KAAK,QAAQ,CAAC,GAAG;AACnE,eAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,MACrC;AAAA,IACF;AAEA,WAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,UAAU;AACd,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,MAAM,UAAS,iBAAiB,QAAQ;AAC9C,WAAO,KAAK,KAAK,IAAI,OAAO,CAAC;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,IAAI;AACX,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,SAAS,CAAC;AAChB,eAAW,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG;AACxC,aAAO,CAAC,IAAI,SAAS,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,IAC5C;AACA,WAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,MAAM;AACR,WAAO,KAAK,UAAS,cAAc,IAAI,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,QAAQ;AACV,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,QAAQ,kCAAK,KAAK,SAAW,gBAAgB,QAAQ,UAAS,aAAa;AACjF,WAAO,MAAM,MAAM,EAAE,QAAQ,MAAM,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,EAAE,QAAQ,iBAAiB,oBAAoB,OAAO,IAAI,CAAC,GAAG;AACxE,UAAM,MAAM,KAAK,IAAI,MAAM,EAAE,QAAQ,gBAAgB,CAAC;AACtD,UAAM,OAAO,EAAE,KAAK,QAAQ,mBAAmB;AAC/C,WAAO,MAAM,MAAM,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,GAAG,MAAM;AACP,WAAO,KAAK,UAAU,KAAK,QAAQ,IAAI,EAAE,IAAI,IAAI,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,YAAY;AACV,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,OAAO,KAAK,SAAS;AAC3B,oBAAgB,KAAK,QAAQ,IAAI;AACjC,WAAO,MAAM,MAAM,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,OAAO,aAAa,KAAK,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC;AAClE,WAAO,MAAM,MAAM,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM,IAAI,CAAC,MAAM,UAAS,cAAc,CAAC,CAAC;AAElD,UAAM,QAAQ,CAAC,GACb,cAAc,CAAC,GACf,OAAO,KAAK,SAAS;AACvB,QAAI;AAEJ,eAAW,KAAK,cAAc;AAC5B,UAAI,MAAM,QAAQ,CAAC,KAAK,GAAG;AACzB,mBAAW;AAEX,YAAI,MAAM;AAGV,mBAAW,MAAM,aAAa;AAC5B,iBAAO,KAAK,OAAO,EAAE,EAAE,CAAC,IAAI,YAAY,EAAE;AAC1C,sBAAY,EAAE,IAAI;AAAA,QACpB;AAGA,YAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACrB,iBAAO,KAAK,CAAC;AAAA,QACf;AAIA,cAAM,IAAI,KAAK,MAAM,GAAG;AACxB,cAAM,CAAC,IAAI;AACX,oBAAY,CAAC,KAAK,MAAM,MAAO,IAAI,OAAQ;AAAA,MAG7C,WAAW,SAAS,KAAK,CAAC,CAAC,GAAG;AAC5B,oBAAY,CAAC,IAAI,KAAK,CAAC;AAAA,MACzB;AAAA,IACF;AAIA,eAAW,OAAO,aAAa;AAC7B,UAAI,YAAY,GAAG,MAAM,GAAG;AAC1B,cAAM,QAAQ,KACZ,QAAQ,WAAW,YAAY,GAAG,IAAI,YAAY,GAAG,IAAI,KAAK,OAAO,QAAQ,EAAE,GAAG;AAAA,MACtF;AAAA,IACF;AAEA,oBAAgB,KAAK,QAAQ,KAAK;AAClC,WAAO,MAAM,MAAM,EAAE,QAAQ,MAAM,GAAG,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,UAAU,CAAC;AACjB,eAAW,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG;AACxC,cAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,OAAO,CAAC;AAAA,IACxD;AACA,WAAO,MAAM,MAAM,EAAE,QAAQ,QAAQ,GAAG,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,OAAO,aAAa,KAAK,MAAM;AACrC,WAAO,MAAM,MAAM,EAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,OAAO,SAAS,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,KAAK,OAAO,YAAY,IAAI;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,KAAK,OAAO,UAAU,IAAI;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,OAAO,SAAS,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU,KAAK,OAAO,QAAQ,IAAI;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,OAAO,SAAS,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,OAAO,WAAW,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,OAAO,WAAW,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU,KAAK,OAAO,gBAAgB,IAAI;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU,KAAK,QAAQ,cAAc;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO;AACZ,QAAI,CAAC,KAAK,WAAW,CAAC,MAAM,SAAS;AACnC,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,KAAK,IAAI,OAAO,MAAM,GAAG,GAAG;AAC/B,aAAO;AAAA,IACT;AAEA,aAAS,GAAG,IAAI,IAAI;AAElB,UAAI,OAAO,UAAa,OAAO,EAAG,QAAO,OAAO,UAAa,OAAO;AACpE,aAAO,OAAO;AAAA,IAChB;AAEA,eAAW,KAAK,cAAc;AAC5B,UAAI,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,CAAC,GAAG;AACxC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;ACx+BA,IAAMC,WAAU;AAGhB,SAAS,iBAAiB,OAAO,KAAK;AACpC,MAAI,CAAC,SAAS,CAAC,MAAM,SAAS;AAC5B,WAAO,SAAS,QAAQ,0BAA0B;AAAA,EACpD,WAAW,CAAC,OAAO,CAAC,IAAI,SAAS;AAC/B,WAAO,SAAS,QAAQ,wBAAwB;AAAA,EAClD,WAAW,MAAM,OAAO;AACtB,WAAO,SAAS;AAAA,MACd;AAAA,MACA,qEAAqE,MAAM,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC;AAAA,IAC3G;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAcA,IAAqB,WAArB,MAAqB,UAAS;AAAA;AAAA;AAAA;AAAA,EAI5B,YAAY,QAAQ;AAIlB,SAAK,IAAI,OAAO;AAIhB,SAAK,IAAI,OAAO;AAIhB,SAAK,UAAU,OAAO,WAAW;AAIjC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QAAQ,QAAQ,cAAc,MAAM;AACzC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,qBAAqB,kDAAkD;AAAA,IACnF;AAEA,UAAM,UAAU,kBAAkB,UAAU,SAAS,IAAI,QAAQ,QAAQ,WAAW;AAEpF,QAAI,SAAS,gBAAgB;AAC3B,YAAM,IAAI,qBAAqB,OAAO;AAAA,IACxC,OAAO;AACL,aAAO,IAAI,UAAS,EAAE,QAAQ,CAAC;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,cAAc,OAAO,KAAK;AAC/B,UAAM,aAAa,iBAAiB,KAAK,GACvC,WAAW,iBAAiB,GAAG;AAEjC,UAAM,gBAAgB,iBAAiB,YAAY,QAAQ;AAE3D,QAAI,iBAAiB,MAAM;AACzB,aAAO,IAAI,UAAS;AAAA,QAClB,OAAO;AAAA,QACP,KAAK;AAAA,MACP,CAAC;AAAA,IACH,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM,OAAO,UAAU;AAC5B,UAAM,MAAM,SAAS,iBAAiB,QAAQ,GAC5C,KAAK,iBAAiB,KAAK;AAC7B,WAAO,UAAS,cAAc,IAAI,GAAG,KAAK,GAAG,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO,KAAK,UAAU;AAC3B,UAAM,MAAM,SAAS,iBAAiB,QAAQ,GAC5C,KAAK,iBAAiB,GAAG;AAC3B,WAAO,UAAS,cAAc,GAAG,MAAM,GAAG,GAAG,EAAE;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,QAAQ,MAAM,MAAM;AACzB,UAAM,CAACC,IAAG,CAAC,KAAK,QAAQ,IAAI,MAAM,KAAK,CAAC;AACxC,QAAIA,MAAK,GAAG;AACV,UAAI,OAAO;AACX,UAAI;AACF,gBAAQ,SAAS,QAAQA,IAAG,IAAI;AAChC,uBAAe,MAAM;AAAA,MACvB,SAASC,IAAG;AACV,uBAAe;AAAA,MACjB;AAEA,UAAI,KAAK;AACT,UAAI;AACF,cAAM,SAAS,QAAQ,GAAG,IAAI;AAC9B,qBAAa,IAAI;AAAA,MACnB,SAASA,IAAG;AACV,qBAAa;AAAA,MACf;AAEA,UAAI,gBAAgB,YAAY;AAC9B,eAAO,UAAS,cAAc,OAAO,GAAG;AAAA,MAC1C;AAEA,UAAI,cAAc;AAChB,cAAM,MAAM,SAAS,QAAQ,GAAG,IAAI;AACpC,YAAI,IAAI,SAAS;AACf,iBAAO,UAAS,MAAM,OAAO,GAAG;AAAA,QAClC;AAAA,MACF,WAAW,YAAY;AACrB,cAAM,MAAM,SAAS,QAAQD,IAAG,IAAI;AACpC,YAAI,IAAI,SAAS;AACf,iBAAO,UAAS,OAAO,KAAK,GAAG;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAS,QAAQ,cAAc,cAAc,IAAI,+BAA+B;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,WAAW,GAAG;AACnB,WAAQ,KAAK,EAAE,mBAAoB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAM;AACR,WAAO,KAAK,UAAU,KAAK,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAW,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,IAAI,OAAQ;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,WAAO,KAAK,kBAAkB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU,KAAK,QAAQ,cAAc;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,gBAAgB;AAC5B,WAAO,KAAK,UAAU,KAAK,WAAW,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO,gBAAgB,MAAM;AACjC,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,QAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI;AAC3C,QAAI;AACJ,QAAI,MAAM,gBAAgB;AACxB,YAAM,KAAK,IAAI,YAAY,EAAE,QAAQ,MAAM,OAAO,CAAC;AAAA,IACrD,OAAO;AACL,YAAM,KAAK;AAAA,IACb;AACA,UAAM,IAAI,QAAQ,MAAM,IAAI;AAC5B,WAAO,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,KAAK,IAAI,QAAQ;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,WAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,KAAK,EAAE,MAAM,CAAC,EAAE,QAAQ,KAAK,GAAG,IAAI,IAAI;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,WAAO,KAAK,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,UAAU;AAChB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,UAAU;AACjB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,UAAU;AACjB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,KAAK,KAAK,YAAY,KAAK,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,EAAE,OAAO,IAAI,IAAI,CAAC,GAAG;AACvB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,UAAS,cAAc,SAAS,KAAK,GAAG,OAAO,KAAK,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,WAAW;AACpB,QAAI,CAAC,KAAK,QAAS,QAAO,CAAC;AAC3B,UAAM,SAAS,UACV,IAAI,gBAAgB,EACpB,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,EAC9B,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,IAAI,EAAE,SAAS,CAAC,GAC7C,UAAU,CAAC;AACb,QAAI,EAAE,GAAAA,GAAE,IAAI,MACV,IAAI;AAEN,WAAOA,KAAI,KAAK,GAAG;AACjB,YAAM,QAAQ,OAAO,CAAC,KAAK,KAAK,GAC9B,OAAO,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,IAAI;AACrC,cAAQ,KAAK,UAAS,cAAcA,IAAG,IAAI,CAAC;AAC5C,MAAAA,KAAI;AACJ,WAAK;AAAA,IACP;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,UAAU;AAChB,UAAM,MAAM,SAAS,iBAAiB,QAAQ;AAE9C,QAAI,CAAC,KAAK,WAAW,CAAC,IAAI,WAAW,IAAI,GAAG,cAAc,MAAM,GAAG;AACjE,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,EAAE,GAAAA,GAAE,IAAI,MACV,MAAM,GACN;AAEF,UAAM,UAAU,CAAC;AACjB,WAAOA,KAAI,KAAK,GAAG;AACjB,YAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,IAAI,GAAG,CAAC;AAC1D,aAAO,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,IAAI;AACnC,cAAQ,KAAK,UAAS,cAAcA,IAAG,IAAI,CAAC;AAC5C,MAAAA,KAAI;AACJ,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,eAAe;AAC3B,QAAI,CAAC,KAAK,QAAS,QAAO,CAAC;AAC3B,WAAO,KAAK,QAAQ,KAAK,OAAO,IAAI,aAAa,EAAE,MAAM,GAAG,aAAa;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO;AACd,WAAO,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,CAAC,KAAK,MAAM,CAAC,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO;AACd,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,CAAC,MAAM,MAAM,CAAC,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO;AACb,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,WAAO,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO;AACZ,QAAI,CAAC,KAAK,WAAW,CAAC,MAAM,SAAS;AACnC,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,EAAE,OAAO,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAMA,KAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,GAC1C,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;AAExC,QAAIA,MAAK,GAAG;AACV,aAAO;AAAA,IACT,OAAO;AACL,aAAO,UAAS,cAAcA,IAAG,CAAC;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO;AACX,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAMA,KAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,GAC1C,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;AACxC,WAAO,UAAS,cAAcA,IAAG,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,MAAM,WAAW;AACtB,UAAM,CAAC,OAAO,KAAK,IAAI,UACpB,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,EAAE,CAAC,EACxB;AAAA,MACC,CAAC,CAAC,OAAO,OAAO,GAAG,SAAS;AAC1B,YAAI,CAAC,SAAS;AACZ,iBAAO,CAAC,OAAO,IAAI;AAAA,QACrB,WAAW,QAAQ,SAAS,IAAI,KAAK,QAAQ,WAAW,IAAI,GAAG;AAC7D,iBAAO,CAAC,OAAO,QAAQ,MAAM,IAAI,CAAC;AAAA,QACpC,OAAO;AACL,iBAAO,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI;AAAA,QACvC;AAAA,MACF;AAAA,MACA,CAAC,CAAC,GAAG,IAAI;AAAA,IACX;AACF,QAAI,OAAO;AACT,YAAM,KAAK,KAAK;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,IAAI,WAAW;AACpB,QAAI,QAAQ,MACV,eAAe;AACjB,UAAM,UAAU,CAAC,GACf,OAAO,UAAU,IAAI,CAAC,MAAM;AAAA,MAC1B,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI;AAAA,MACvB,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI;AAAA,IACzB,CAAC,GACD,YAAY,MAAM,UAAU,OAAO,GAAG,IAAI,GAC1C,MAAM,UAAU,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AAEhD,eAAW,KAAK,KAAK;AACnB,sBAAgB,EAAE,SAAS,MAAM,IAAI;AAErC,UAAI,iBAAiB,GAAG;AACtB,gBAAQ,EAAE;AAAA,MACZ,OAAO;AACL,YAAI,SAAS,CAAC,UAAU,CAAC,EAAE,MAAM;AAC/B,kBAAQ,KAAK,UAAS,cAAc,OAAO,EAAE,IAAI,CAAC;AAAA,QACpD;AAEA,gBAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO,UAAS,MAAM,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,WAAW;AACvB,WAAO,UAAS,IAAI,CAAC,IAAI,EAAE,OAAO,SAAS,CAAC,EACzC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC,EAC/B,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,QAAQ,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,QAAI,CAAC,KAAK,QAAS,QAAOD;AAC1B,WAAO,IAAI,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,EAAE,MAAM,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,CAAC,OAAO,IAAI,4BAA4B,CAAC,IAAI;AAC3C,QAAI,KAAK,SAAS;AAChB,aAAO,qBAAqB,KAAK,EAAE,MAAM,CAAC,UAAU,KAAK,EAAE,MAAM,CAAC;AAAA,IACpE,OAAO;AACL,aAAO,+BAA+B,KAAK,aAAa;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,eAAe,aAAqB,YAAY,OAAO,CAAC,GAAG;AACzD,WAAO,KAAK,UACR,UAAU,OAAO,KAAK,EAAE,IAAI,MAAM,IAAI,GAAG,UAAU,EAAE,eAAe,IAAI,IACxEA;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM;AACV,QAAI,CAAC,KAAK,QAAS,QAAOA;AAC1B,WAAO,GAAG,KAAK,EAAE,MAAM,IAAI,CAAC,IAAI,KAAK,EAAE,MAAM,IAAI,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,QAAI,CAAC,KAAK,QAAS,QAAOA;AAC1B,WAAO,GAAG,KAAK,EAAE,UAAU,CAAC,IAAI,KAAK,EAAE,UAAU,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,MAAM;AACd,QAAI,CAAC,KAAK,QAAS,QAAOA;AAC1B,WAAO,GAAG,KAAK,EAAE,UAAU,IAAI,CAAC,IAAI,KAAK,EAAE,UAAU,IAAI,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,SAAS,YAAY,EAAE,YAAY,MAAM,IAAI,CAAC,GAAG;AAC/C,QAAI,CAAC,KAAK,QAAS,QAAOA;AAC1B,WAAO,GAAG,KAAK,EAAE,SAAS,UAAU,CAAC,GAAG,SAAS,GAAG,KAAK,EAAE,SAAS,UAAU,CAAC;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,WAAW,MAAM,MAAM;AACrB,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO,SAAS,QAAQ,KAAK,aAAa;AAAA,IAC5C;AACA,WAAO,KAAK,EAAE,KAAK,KAAK,GAAG,MAAM,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,OAAO;AAClB,WAAO,UAAS,cAAc,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC;AAAA,EAC5D;AACF;;;ACjpBA,IAAqB,OAArB,MAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,OAAO,OAAO,OAAO,SAAS,aAAa;AACzC,UAAM,QAAQ,SAAS,IAAI,EAAE,QAAQ,IAAI,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC;AAE5D,WAAO,CAAC,KAAK,eAAe,MAAM,WAAW,MAAM,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,gBAAgB,MAAM;AAC3B,WAAO,SAAS,YAAY,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,cAAc,OAAO;AAC1B,WAAO,cAAc,OAAO,SAAS,WAAW;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,eAAe,EAAE,SAAS,MAAM,SAAS,KAAK,IAAI,CAAC,GAAG;AAC3D,YAAQ,UAAU,OAAO,OAAO,MAAM,GAAG,eAAe;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,0BAA0B,EAAE,SAAS,MAAM,SAAS,KAAK,IAAI,CAAC,GAAG;AACtE,YAAQ,UAAU,OAAO,OAAO,MAAM,GAAG,sBAAsB;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,mBAAmB,EAAE,SAAS,MAAM,SAAS,KAAK,IAAI,CAAC,GAAG;AAE/D,YAAQ,UAAU,OAAO,OAAO,MAAM,GAAG,eAAe,EAAE,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,OAAO,OACL,SAAS,QACT,EAAE,SAAS,MAAM,kBAAkB,MAAM,SAAS,MAAM,iBAAiB,UAAU,IAAI,CAAC,GACxF;AACA,YAAQ,UAAU,OAAO,OAAO,QAAQ,iBAAiB,cAAc,GAAG,OAAO,MAAM;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,OAAO,aACL,SAAS,QACT,EAAE,SAAS,MAAM,kBAAkB,MAAM,SAAS,MAAM,iBAAiB,UAAU,IAAI,CAAC,GACxF;AACA,YAAQ,UAAU,OAAO,OAAO,QAAQ,iBAAiB,cAAc,GAAG,OAAO,QAAQ,IAAI;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,SAAS,SAAS,QAAQ,EAAE,SAAS,MAAM,kBAAkB,MAAM,SAAS,KAAK,IAAI,CAAC,GAAG;AAC9F,YAAQ,UAAU,OAAO,OAAO,QAAQ,iBAAiB,IAAI,GAAG,SAAS,MAAM;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,eACL,SAAS,QACT,EAAE,SAAS,MAAM,kBAAkB,MAAM,SAAS,KAAK,IAAI,CAAC,GAC5D;AACA,YAAQ,UAAU,OAAO,OAAO,QAAQ,iBAAiB,IAAI,GAAG,SAAS,QAAQ,IAAI;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,UAAU,EAAE,SAAS,KAAK,IAAI,CAAC,GAAG;AACvC,WAAO,OAAO,OAAO,MAAM,EAAE,UAAU;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,KAAK,SAAS,SAAS,EAAE,SAAS,KAAK,IAAI,CAAC,GAAG;AACpD,WAAO,OAAO,OAAO,QAAQ,MAAM,SAAS,EAAE,KAAK,MAAM;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,WAAW;AAChB,WAAO,EAAE,UAAU,YAAY,GAAG,YAAY,kBAAkB,EAAE;AAAA,EACpE;AACF;;;AC1MA,SAAS,QAAQ,SAAS,OAAO;AAC/B,QAAM,cAAc,CAAC,OAAO,GAAG,MAAM,GAAG,EAAE,eAAe,KAAK,CAAC,EAAE,QAAQ,KAAK,EAAE,QAAQ,GACtF,KAAK,YAAY,KAAK,IAAI,YAAY,OAAO;AAC/C,SAAO,KAAK,MAAM,SAAS,WAAW,EAAE,EAAE,GAAG,MAAM,CAAC;AACtD;AAEA,SAAS,eAAe,QAAQ,OAAO,OAAO;AAC5C,QAAM,UAAU;AAAA,IACd,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AAAA,IACnC,CAAC,YAAY,CAAC,GAAG,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC;AAAA,IACpE,CAAC,UAAU,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE;AAAA,IAC/D;AAAA,MACE;AAAA,MACA,CAAC,GAAG,MAAM;AACR,cAAM,OAAO,QAAQ,GAAG,CAAC;AACzB,gBAAQ,OAAQ,OAAO,KAAM;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,CAAC,QAAQ,OAAO;AAAA,EAClB;AAEA,QAAM,UAAU,CAAC;AACjB,QAAM,UAAU;AAChB,MAAI,aAAa;AAUjB,aAAW,CAAC,MAAM,MAAM,KAAK,SAAS;AACpC,QAAI,MAAM,QAAQ,IAAI,KAAK,GAAG;AAC5B,oBAAc;AAEd,cAAQ,IAAI,IAAI,OAAO,QAAQ,KAAK;AACpC,kBAAY,QAAQ,KAAK,OAAO;AAEhC,UAAI,YAAY,OAAO;AAErB,gBAAQ,IAAI;AACZ,iBAAS,QAAQ,KAAK,OAAO;AAK7B,YAAI,SAAS,OAAO;AAElB,sBAAY;AAEZ,kBAAQ,IAAI;AACZ,mBAAS,QAAQ,KAAK,OAAO;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAEA,SAAO,CAAC,QAAQ,SAAS,WAAW,WAAW;AACjD;AAEe,SAAR,aAAkB,SAAS,OAAO,OAAO,MAAM;AACpD,MAAI,CAAC,QAAQ,SAAS,WAAW,WAAW,IAAI,eAAe,SAAS,OAAO,KAAK;AAEpF,QAAM,kBAAkB,QAAQ;AAEhC,QAAM,kBAAkB,MAAM;AAAA,IAC5B,CAAC,MAAM,CAAC,SAAS,WAAW,WAAW,cAAc,EAAE,QAAQ,CAAC,KAAK;AAAA,EACvE;AAEA,MAAI,gBAAgB,WAAW,GAAG;AAChC,QAAI,YAAY,OAAO;AACrB,kBAAY,OAAO,KAAK,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC;AAAA,IAC9C;AAEA,QAAI,cAAc,QAAQ;AACxB,cAAQ,WAAW,KAAK,QAAQ,WAAW,KAAK,KAAK,mBAAmB,YAAY;AAAA,IACtF;AAAA,EACF;AAEA,QAAM,WAAW,SAAS,WAAW,SAAS,IAAI;AAElD,MAAI,gBAAgB,SAAS,GAAG;AAC9B,WAAO,SAAS,WAAW,iBAAiB,IAAI,EAC7C,QAAQ,GAAG,eAAe,EAC1B,KAAK,QAAQ;AAAA,EAClB,OAAO;AACL,WAAO;AAAA,EACT;AACF;;;ACtFA,IAAM,cAAc;AAEpB,SAAS,QAAQ,OAAO,OAAO,CAAC,MAAM,GAAG;AACvC,SAAO,EAAE,OAAO,OAAO,CAAC,CAACG,EAAC,MAAM,KAAK,YAAYA,EAAC,CAAC,EAAE;AACvD;AAEA,IAAM,OAAO,OAAO,aAAa,GAAG;AACpC,IAAM,cAAc,KAAK,IAAI;AAC7B,IAAM,oBAAoB,IAAI,OAAO,aAAa,GAAG;AAErD,SAAS,aAAaA,IAAG;AAGvB,SAAOA,GAAE,QAAQ,OAAO,MAAM,EAAE,QAAQ,mBAAmB,WAAW;AACxE;AAEA,SAAS,qBAAqBA,IAAG;AAC/B,SAAOA,GACJ,QAAQ,OAAO,EAAE,EACjB,QAAQ,mBAAmB,GAAG,EAC9B,YAAY;AACjB;AAEA,SAAS,MAAM,SAAS,YAAY;AAClC,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,MACL,OAAO,OAAO,QAAQ,IAAI,YAAY,EAAE,KAAK,GAAG,CAAC;AAAA,MACjD,OAAO,CAAC,CAACA,EAAC,MACR,QAAQ,UAAU,CAAC,MAAM,qBAAqBA,EAAC,MAAM,qBAAqB,CAAC,CAAC,IAAI;AAAA,IACpF;AAAA,EACF;AACF;AAEA,SAAS,OAAO,OAAO,QAAQ;AAC7B,SAAO,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,aAAa,GAAG,CAAC,GAAG,OAAO;AAClE;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,EAAE,OAAO,OAAO,CAAC,CAACA,EAAC,MAAMA,GAAE;AACpC;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,+BAA+B,MAAM;AAC5D;AAMA,SAAS,aAAa,OAAO,KAAK;AAChC,QAAM,MAAM,WAAW,GAAG,GACxB,MAAM,WAAW,KAAK,KAAK,GAC3B,QAAQ,WAAW,KAAK,KAAK,GAC7B,OAAO,WAAW,KAAK,KAAK,GAC5B,MAAM,WAAW,KAAK,KAAK,GAC3B,WAAW,WAAW,KAAK,OAAO,GAClC,aAAa,WAAW,KAAK,OAAO,GACpC,WAAW,WAAW,KAAK,OAAO,GAClC,YAAY,WAAW,KAAK,OAAO,GACnC,YAAY,WAAW,KAAK,OAAO,GACnC,YAAY,WAAW,KAAK,OAAO,GACnC,UAAU,CAAC,OAAO,EAAE,OAAO,OAAO,YAAY,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,CAACA,EAAC,MAAMA,IAAG,SAAS,KAAK,IACxF,UAAU,CAAC,MAAM;AACf,QAAI,MAAM,SAAS;AACjB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,YAAQ,EAAE,KAAK;AAAA,MAEb,KAAK;AACH,eAAO,MAAM,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,MACnC,KAAK;AACH,eAAO,MAAM,IAAI,KAAK,MAAM,GAAG,CAAC;AAAA,MAElC,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,WAAW,cAAc;AAAA,MAC1C,KAAK;AACH,eAAO,QAAQ,IAAI;AAAA,MACrB,KAAK;AACH,eAAO,QAAQ,SAAS;AAAA,MAC1B,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MAEpB,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,MAAM,IAAI,OAAO,SAAS,IAAI,GAAG,CAAC;AAAA,MAC3C,KAAK;AACH,eAAO,MAAM,IAAI,OAAO,QAAQ,IAAI,GAAG,CAAC;AAAA,MAC1C,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,MAAM,IAAI,OAAO,SAAS,KAAK,GAAG,CAAC;AAAA,MAC5C,KAAK;AACH,eAAO,MAAM,IAAI,OAAO,QAAQ,KAAK,GAAG,CAAC;AAAA,MAE3C,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MAEpB,KAAK;AACH,eAAO,QAAQ,UAAU;AAAA,MAC3B,KAAK;AACH,eAAO,QAAQ,KAAK;AAAA,MAEtB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ,UAAU;AAAA,MAC3B,KAAK;AACH,eAAO,QAAQ,KAAK;AAAA,MACtB,KAAK;AACH,eAAO,OAAO,SAAS;AAAA,MACzB,KAAK;AACH,eAAO,OAAO,QAAQ;AAAA,MACxB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MAEpB,KAAK;AACH,eAAO,MAAM,IAAI,UAAU,GAAG,CAAC;AAAA,MAEjC,KAAK;AACH,eAAO,QAAQ,IAAI;AAAA,MACrB,KAAK;AACH,eAAO,QAAQ,WAAW,cAAc;AAAA,MAE1C,KAAK;AACH,eAAO,QAAQ,QAAQ;AAAA,MACzB,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MAEpB,KAAK;AAAA,MACL,KAAK;AACH,eAAO,QAAQ,GAAG;AAAA,MACpB,KAAK;AACH,eAAO,MAAM,IAAI,SAAS,SAAS,KAAK,GAAG,CAAC;AAAA,MAC9C,KAAK;AACH,eAAO,MAAM,IAAI,SAAS,QAAQ,KAAK,GAAG,CAAC;AAAA,MAC7C,KAAK;AACH,eAAO,MAAM,IAAI,SAAS,SAAS,IAAI,GAAG,CAAC;AAAA,MAC7C,KAAK;AACH,eAAO,MAAM,IAAI,SAAS,QAAQ,IAAI,GAAG,CAAC;AAAA,MAE5C,KAAK;AAAA,MACL,KAAK;AACH,eAAO,OAAO,IAAI,OAAO,QAAQ,SAAS,MAAM,SAAS,IAAI,MAAM,KAAK,GAAG,CAAC;AAAA,MAC9E,KAAK;AACH,eAAO,OAAO,IAAI,OAAO,QAAQ,SAAS,MAAM,KAAK,IAAI,MAAM,IAAI,GAAG,CAAC;AAAA,MAGzE,KAAK;AACH,eAAO,OAAO,oBAAoB;AAAA,MAGpC,KAAK;AACH,eAAO,OAAO,WAAW;AAAA,MAC3B;AACE,eAAO,QAAQ,CAAC;AAAA,IACpB;AAAA,EACF;AAEF,QAAM,OAAO,QAAQ,KAAK,KAAK;AAAA,IAC7B,eAAe;AAAA,EACjB;AAEA,OAAK,QAAQ;AAEb,SAAO;AACT;AAEA,IAAM,0BAA0B;AAAA,EAC9B,MAAM;AAAA,IACJ,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,KAAK;AAAA,IACH,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,MAAM,YAAY,cAAc;AACpD,QAAM,EAAE,MAAM,MAAM,IAAI;AAExB,MAAI,SAAS,WAAW;AACtB,UAAM,UAAU,QAAQ,KAAK,KAAK;AAClC,WAAO;AAAA,MACL,SAAS,CAAC;AAAA,MACV,KAAK,UAAU,MAAM;AAAA,IACvB;AAAA,EACF;AAEA,QAAM,QAAQ,WAAW,IAAI;AAK7B,MAAI,aAAa;AACjB,MAAI,SAAS,QAAQ;AACnB,QAAI,WAAW,UAAU,MAAM;AAC7B,mBAAa,WAAW,SAAS,WAAW;AAAA,IAC9C,WAAW,WAAW,aAAa,MAAM;AACvC,UAAI,WAAW,cAAc,SAAS,WAAW,cAAc,OAAO;AACpE,qBAAa;AAAA,MACf,OAAO;AACL,qBAAa;AAAA,MACf;AAAA,IACF,OAAO;AAGL,mBAAa,aAAa,SAAS,WAAW;AAAA,IAChD;AAAA,EACF;AACA,MAAI,MAAM,wBAAwB,UAAU;AAC5C,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,KAAK;AAAA,EACjB;AAEA,MAAI,KAAK;AACP,WAAO;AAAA,MACL,SAAS;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,WAAW,OAAO;AACzB,QAAM,KAAK,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,EAAE;AAC7E,SAAO,CAAC,IAAI,EAAE,KAAK,KAAK;AAC1B;AAEA,SAAS,MAAM,OAAO,OAAO,UAAU;AACrC,QAAM,UAAU,MAAM,MAAM,KAAK;AAEjC,MAAI,SAAS;AACX,UAAM,MAAM,CAAC;AACb,QAAI,aAAa;AACjB,eAAW,KAAK,UAAU;AACxB,UAAI,eAAe,UAAU,CAAC,GAAG;AAC/B,cAAM,IAAI,SAAS,CAAC,GAClB,SAAS,EAAE,SAAS,EAAE,SAAS,IAAI;AACrC,YAAI,CAAC,EAAE,WAAW,EAAE,OAAO;AACzB,cAAI,EAAE,MAAM,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM,QAAQ,MAAM,YAAY,aAAa,MAAM,CAAC;AAAA,QAC9E;AACA,sBAAc;AAAA,MAChB;AAAA,IACF;AACA,WAAO,CAAC,SAAS,GAAG;AAAA,EACtB,OAAO;AACL,WAAO,CAAC,SAAS,CAAC,CAAC;AAAA,EACrB;AACF;AAEA,SAAS,oBAAoB,SAAS;AACpC,QAAM,UAAU,CAAC,UAAU;AACzB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAEA,MAAI,OAAO;AACX,MAAI;AACJ,MAAI,CAAC,YAAY,QAAQ,CAAC,GAAG;AAC3B,WAAO,SAAS,OAAO,QAAQ,CAAC;AAAA,EAClC;AAEA,MAAI,CAAC,YAAY,QAAQ,CAAC,GAAG;AAC3B,QAAI,CAAC,MAAM;AACT,aAAO,IAAI,gBAAgB,QAAQ,CAAC;AAAA,IACtC;AACA,qBAAiB,QAAQ;AAAA,EAC3B;AAEA,MAAI,CAAC,YAAY,QAAQ,CAAC,GAAG;AAC3B,YAAQ,KAAK,QAAQ,IAAI,KAAK,IAAI;AAAA,EACpC;AAEA,MAAI,CAAC,YAAY,QAAQ,CAAC,GAAG;AAC3B,QAAI,QAAQ,IAAI,MAAM,QAAQ,MAAM,GAAG;AACrC,cAAQ,KAAK;AAAA,IACf,WAAW,QAAQ,MAAM,MAAM,QAAQ,MAAM,GAAG;AAC9C,cAAQ,IAAI;AAAA,IACd;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM,KAAK,QAAQ,GAAG;AAChC,YAAQ,IAAI,CAAC,QAAQ;AAAA,EACvB;AAEA,MAAI,CAAC,YAAY,QAAQ,CAAC,GAAG;AAC3B,YAAQ,IAAI,YAAY,QAAQ,CAAC;AAAA,EACnC;AAEA,QAAM,OAAO,OAAO,KAAK,OAAO,EAAE,OAAO,CAAC,GAAG,MAAM;AACjD,UAAM,IAAI,QAAQ,CAAC;AACnB,QAAI,GAAG;AACL,QAAE,CAAC,IAAI,QAAQ,CAAC;AAAA,IAClB;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,MAAM,MAAM,cAAc;AACpC;AAEA,IAAI,qBAAqB;AAEzB,SAAS,mBAAmB;AAC1B,MAAI,CAAC,oBAAoB;AACvB,yBAAqB,SAAS,WAAW,aAAa;AAAA,EACxD;AAEA,SAAO;AACT;AAEA,SAAS,sBAAsB,OAAO,QAAQ;AAC5C,MAAI,MAAM,SAAS;AACjB,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,UAAU,uBAAuB,MAAM,GAAG;AAC7D,QAAM,SAAS,mBAAmB,YAAY,MAAM;AAEpD,MAAI,UAAU,QAAQ,OAAO,SAAS,MAAS,GAAG;AAChD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,SAAS,kBAAkB,QAAQ,QAAQ;AAChD,SAAO,MAAM,UAAU,OAAO,GAAG,OAAO,IAAI,CAAC,MAAM,sBAAsB,GAAG,MAAM,CAAC,CAAC;AACtF;AAMO,IAAM,cAAN,MAAkB;AAAA,EACvB,YAAY,QAAQ,QAAQ;AAC1B,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,SAAS,kBAAkB,UAAU,YAAY,MAAM,GAAG,MAAM;AACrE,SAAK,QAAQ,KAAK,OAAO,IAAI,CAAC,MAAM,aAAa,GAAG,MAAM,CAAC;AAC3D,SAAK,oBAAoB,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,aAAa;AAE/D,QAAI,CAAC,KAAK,mBAAmB;AAC3B,YAAM,CAAC,aAAa,QAAQ,IAAI,WAAW,KAAK,KAAK;AACrD,WAAK,QAAQ,OAAO,aAAa,GAAG;AACpC,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,kBAAkB,OAAO;AACvB,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO,EAAE,OAAO,QAAQ,KAAK,QAAQ,eAAe,KAAK,cAAc;AAAA,IACzE,OAAO;AACL,YAAM,CAAC,YAAY,OAAO,IAAI,MAAM,OAAO,KAAK,OAAO,KAAK,QAAQ,GAClE,CAAC,QAAQ,MAAM,cAAc,IAAI,UAC7B,oBAAoB,OAAO,IAC3B,CAAC,MAAM,MAAM,MAAS;AAC5B,UAAI,eAAe,SAAS,GAAG,KAAK,eAAe,SAAS,GAAG,GAAG;AAChE,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,CAAC,KAAK;AAAA,EACf;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,oBAAoB,KAAK,kBAAkB,gBAAgB;AAAA,EACzE;AACF;AAEO,SAAS,kBAAkB,QAAQ,OAAO,QAAQ;AACvD,QAAM,SAAS,IAAI,YAAY,QAAQ,MAAM;AAC7C,SAAO,OAAO,kBAAkB,KAAK;AACvC;AAEO,SAAS,gBAAgB,QAAQ,OAAO,QAAQ;AACrD,QAAM,EAAE,QAAQ,MAAM,gBAAgB,cAAc,IAAI,kBAAkB,QAAQ,OAAO,MAAM;AAC/F,SAAO,CAAC,QAAQ,MAAM,gBAAgB,aAAa;AACrD;AAEO,SAAS,mBAAmB,YAAY,QAAQ;AACrD,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,UAAU,OAAO,QAAQ,UAAU;AACrD,QAAM,KAAK,UAAU,YAAY,iBAAiB,CAAC;AACnD,QAAM,QAAQ,GAAG,cAAc;AAC/B,QAAM,eAAe,GAAG,gBAAgB;AACxC,SAAO,MAAM,IAAI,CAAC,MAAM,aAAa,GAAG,YAAY,YAAY,CAAC;AACnE;;;ACncA,IAAMC,WAAU;AAChB,IAAM,WAAW;AAEjB,SAAS,gBAAgB,MAAM;AAC7B,SAAO,IAAI,QAAQ,oBAAoB,aAAa,KAAK,IAAI,oBAAoB;AACnF;AAMA,SAAS,uBAAuB,IAAI;AAClC,MAAI,GAAG,aAAa,MAAM;AACxB,OAAG,WAAW,gBAAgB,GAAG,CAAC;AAAA,EACpC;AACA,SAAO,GAAG;AACZ;AAKA,SAAS,4BAA4B,IAAI;AACvC,MAAI,GAAG,kBAAkB,MAAM;AAC7B,OAAG,gBAAgB;AAAA,MACjB,GAAG;AAAA,MACH,GAAG,IAAI,sBAAsB;AAAA,MAC7B,GAAG,IAAI,eAAe;AAAA,IACxB;AAAA,EACF;AACA,SAAO,GAAG;AACZ;AAIA,SAASC,OAAM,MAAM,MAAM;AACzB,QAAM,UAAU;AAAA,IACd,IAAI,KAAK;AAAA,IACT,MAAM,KAAK;AAAA,IACX,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,IACR,KAAK,KAAK;AAAA,IACV,SAAS,KAAK;AAAA,EAChB;AACA,SAAO,IAAI,SAAS,gDAAK,UAAY,OAAjB,EAAuB,KAAK,QAAQ,EAAC;AAC3D;AAIA,SAAS,UAAU,SAAS,GAAG,IAAI;AAEjC,MAAI,WAAW,UAAU,IAAI,KAAK;AAGlC,QAAM,KAAK,GAAG,OAAO,QAAQ;AAG7B,MAAI,MAAM,IAAI;AACZ,WAAO,CAAC,UAAU,CAAC;AAAA,EACrB;AAGA,eAAa,KAAK,KAAK,KAAK;AAG5B,QAAM,KAAK,GAAG,OAAO,QAAQ;AAC7B,MAAI,OAAO,IAAI;AACb,WAAO,CAAC,UAAU,EAAE;AAAA,EACtB;AAGA,SAAO,CAAC,UAAU,KAAK,IAAI,IAAI,EAAE,IAAI,KAAK,KAAM,KAAK,IAAI,IAAI,EAAE,CAAC;AAClE;AAGA,SAAS,QAAQ,IAAIC,SAAQ;AAC3B,QAAMA,UAAS,KAAK;AAEpB,QAAM,IAAI,IAAI,KAAK,EAAE;AAErB,SAAO;AAAA,IACL,MAAM,EAAE,eAAe;AAAA,IACvB,OAAO,EAAE,YAAY,IAAI;AAAA,IACzB,KAAK,EAAE,WAAW;AAAA,IAClB,MAAM,EAAE,YAAY;AAAA,IACpB,QAAQ,EAAE,cAAc;AAAA,IACxB,QAAQ,EAAE,cAAc;AAAA,IACxB,aAAa,EAAE,mBAAmB;AAAA,EACpC;AACF;AAGA,SAAS,QAAQ,KAAKA,SAAQ,MAAM;AAClC,SAAO,UAAU,aAAa,GAAG,GAAGA,SAAQ,IAAI;AAClD;AAGA,SAAS,WAAW,MAAM,KAAK;AAC7B,QAAM,OAAO,KAAK,GAChB,OAAO,KAAK,EAAE,OAAO,KAAK,MAAM,IAAI,KAAK,GACzC,QAAQ,KAAK,EAAE,QAAQ,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,QAAQ,IAAI,GAC3E,IAAI,iCACC,KAAK,IADN;AAAA,IAEF;AAAA,IACA;AAAA,IACA,KACE,KAAK,IAAI,KAAK,EAAE,KAAK,YAAY,MAAM,KAAK,CAAC,IAC7C,KAAK,MAAM,IAAI,IAAI,IACnB,KAAK,MAAM,IAAI,KAAK,IAAI;AAAA,EAC5B,IACA,cAAc,SAAS,WAAW;AAAA,IAChC,OAAO,IAAI,QAAQ,KAAK,MAAM,IAAI,KAAK;AAAA,IACvC,UAAU,IAAI,WAAW,KAAK,MAAM,IAAI,QAAQ;AAAA,IAChD,QAAQ,IAAI,SAAS,KAAK,MAAM,IAAI,MAAM;AAAA,IAC1C,OAAO,IAAI,QAAQ,KAAK,MAAM,IAAI,KAAK;AAAA,IACvC,MAAM,IAAI,OAAO,KAAK,MAAM,IAAI,IAAI;AAAA,IACpC,OAAO,IAAI;AAAA,IACX,SAAS,IAAI;AAAA,IACb,SAAS,IAAI;AAAA,IACb,cAAc,IAAI;AAAA,EACpB,CAAC,EAAE,GAAG,cAAc,GACpB,UAAU,aAAa,CAAC;AAE1B,MAAI,CAAC,IAAI,CAAC,IAAI,UAAU,SAAS,MAAM,KAAK,IAAI;AAEhD,MAAI,gBAAgB,GAAG;AACrB,UAAM;AAEN,QAAI,KAAK,KAAK,OAAO,EAAE;AAAA,EACzB;AAEA,SAAO,EAAE,IAAI,EAAE;AACjB;AAIA,SAAS,oBAAoB,QAAQ,YAAY,MAAM,QAAQ,MAAM,gBAAgB;AACnF,QAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,MAAK,UAAU,OAAO,KAAK,MAAM,EAAE,WAAW,KAAM,YAAY;AAC9D,UAAM,qBAAqB,cAAc,MACvC,OAAO,SAAS,WAAW,QAAQ,iCAC9B,OAD8B;AAAA,MAEjC,MAAM;AAAA,MACN;AAAA,IACF,EAAC;AACH,WAAO,UAAU,OAAO,KAAK,QAAQ,IAAI;AAAA,EAC3C,OAAO;AACL,WAAO,SAAS;AAAA,MACd,IAAI,QAAQ,cAAc,cAAc,IAAI,wBAAwB,MAAM,EAAE;AAAA,IAC9E;AAAA,EACF;AACF;AAIA,SAAS,aAAa,IAAI,QAAQ,SAAS,MAAM;AAC/C,SAAO,GAAG,UACN,UAAU,OAAO,OAAO,OAAO,OAAO,GAAG;AAAA,IACvC;AAAA,IACA,aAAa;AAAA,EACf,CAAC,EAAE,yBAAyB,IAAI,MAAM,IACtC;AACN;AAEA,SAAS,UAAU,GAAG,UAAU,WAAW;AACzC,QAAM,aAAa,EAAE,EAAE,OAAO,QAAQ,EAAE,EAAE,OAAO;AACjD,MAAI,IAAI;AACR,MAAI,cAAc,EAAE,EAAE,QAAQ,EAAG,MAAK;AACtC,OAAK,SAAS,EAAE,EAAE,MAAM,aAAa,IAAI,CAAC;AAC1C,MAAI,cAAc,OAAQ,QAAO;AACjC,MAAI,UAAU;AACZ,SAAK;AACL,SAAK,SAAS,EAAE,EAAE,KAAK;AACvB,QAAI,cAAc,QAAS,QAAO;AAClC,SAAK;AAAA,EACP,OAAO;AACL,SAAK,SAAS,EAAE,EAAE,KAAK;AACvB,QAAI,cAAc,QAAS,QAAO;AAAA,EACpC;AACA,OAAK,SAAS,EAAE,EAAE,GAAG;AACrB,SAAO;AACT;AAEA,SAAS,UACP,GACA,UACA,iBACA,sBACA,eACA,cACA,WACA;AACA,MAAI,cAAc,CAAC,mBAAmB,EAAE,EAAE,gBAAgB,KAAK,EAAE,EAAE,WAAW,GAC5E,IAAI;AACN,UAAQ,WAAW;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH;AAAA,IACF;AACE,WAAK,SAAS,EAAE,EAAE,IAAI;AACtB,UAAI,cAAc,OAAQ;AAC1B,UAAI,UAAU;AACZ,aAAK;AACL,aAAK,SAAS,EAAE,EAAE,MAAM;AACxB,YAAI,cAAc,SAAU;AAC5B,YAAI,aAAa;AACf,eAAK;AACL,eAAK,SAAS,EAAE,EAAE,MAAM;AAAA,QAC1B;AAAA,MACF,OAAO;AACL,aAAK,SAAS,EAAE,EAAE,MAAM;AACxB,YAAI,cAAc,SAAU;AAC5B,YAAI,aAAa;AACf,eAAK,SAAS,EAAE,EAAE,MAAM;AAAA,QAC1B;AAAA,MACF;AACA,UAAI,cAAc,SAAU;AAC5B,UAAI,gBAAgB,CAAC,wBAAwB,EAAE,EAAE,gBAAgB,IAAI;AACnE,aAAK;AACL,aAAK,SAAS,EAAE,EAAE,aAAa,CAAC;AAAA,MAClC;AAAA,EACJ;AAEA,MAAI,eAAe;AACjB,QAAI,EAAE,iBAAiB,EAAE,WAAW,KAAK,CAAC,cAAc;AACtD,WAAK;AAAA,IACP,WAAW,EAAE,IAAI,GAAG;AAClB,WAAK;AACL,WAAK,SAAS,KAAK,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;AACnC,WAAK;AACL,WAAK,SAAS,KAAK,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;AAAA,IACrC,OAAO;AACL,WAAK;AACL,WAAK,SAAS,KAAK,MAAM,EAAE,IAAI,EAAE,CAAC;AAClC,WAAK;AACL,WAAK,SAAS,KAAK,MAAM,EAAE,IAAI,EAAE,CAAC;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,cAAc;AAChB,SAAK,MAAM,EAAE,KAAK,WAAW;AAAA,EAC/B;AACA,SAAO;AACT;AAGA,IAAM,oBAAoB;AAAA,EACtB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,aAAa;AACf;AAPF,IAQE,wBAAwB;AAAA,EACtB,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,aAAa;AACf;AAfF,IAgBE,2BAA2B;AAAA,EACzB,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,aAAa;AACf;AAGF,IAAMC,gBAAe,CAAC,QAAQ,SAAS,OAAO,QAAQ,UAAU,UAAU,aAAa;AAAvF,IACE,mBAAmB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AATF,IAUE,sBAAsB,CAAC,QAAQ,WAAW,QAAQ,UAAU,UAAU,aAAa;AAGrF,SAAS,cAAc,MAAM;AAC3B,QAAM,aAAa;AAAA,IACjB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,EACX,EAAE,KAAK,YAAY,CAAC;AAEpB,MAAI,CAAC,WAAY,OAAM,IAAI,iBAAiB,IAAI;AAEhD,SAAO;AACT;AAEA,SAAS,4BAA4B,MAAM;AACzC,UAAQ,KAAK,YAAY,GAAG;AAAA,IAC1B,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO,cAAc,IAAI;AAAA,EAC7B;AACF;AAyBA,SAAS,mBAAmB,MAAM;AAChC,MAAI,iBAAiB,QAAW;AAC9B,mBAAe,SAAS,IAAI;AAAA,EAC9B;AAIA,MAAI,KAAK,SAAS,QAAQ;AACxB,WAAO,KAAK,OAAO,YAAY;AAAA,EACjC;AACA,QAAM,WAAW,KAAK;AACtB,MAAI,cAAc,qBAAqB,IAAI,QAAQ;AACnD,MAAI,gBAAgB,QAAW;AAC7B,kBAAc,KAAK,OAAO,YAAY;AACtC,yBAAqB,IAAI,UAAU,WAAW;AAAA,EAChD;AACA,SAAO;AACT;AAKA,SAAS,QAAQ,KAAK,MAAM;AAC1B,QAAM,OAAO,cAAc,KAAK,MAAM,SAAS,WAAW;AAC1D,MAAI,CAAC,KAAK,SAAS;AACjB,WAAO,SAAS,QAAQ,gBAAgB,IAAI,CAAC;AAAA,EAC/C;AAEA,QAAM,MAAM,OAAO,WAAW,IAAI;AAElC,MAAI,IAAI;AAGR,MAAI,CAAC,YAAY,IAAI,IAAI,GAAG;AAC1B,eAAW,KAAKA,eAAc;AAC5B,UAAI,YAAY,IAAI,CAAC,CAAC,GAAG;AACvB,YAAI,CAAC,IAAI,kBAAkB,CAAC;AAAA,MAC9B;AAAA,IACF;AAEA,UAAM,UAAU,wBAAwB,GAAG,KAAK,mBAAmB,GAAG;AACtE,QAAI,SAAS;AACX,aAAO,SAAS,QAAQ,OAAO;AAAA,IACjC;AAEA,UAAM,eAAe,mBAAmB,IAAI;AAC5C,KAAC,IAAI,CAAC,IAAI,QAAQ,KAAK,cAAc,IAAI;AAAA,EAC3C,OAAO;AACL,SAAK,SAAS,IAAI;AAAA,EACpB;AAEA,SAAO,IAAI,SAAS,EAAE,IAAI,MAAM,KAAK,EAAE,CAAC;AAC1C;AAEA,SAAS,aAAa,OAAO,KAAK,MAAM;AACtC,QAAM,QAAQ,YAAY,KAAK,KAAK,IAAI,OAAO,KAAK,OAClD,WAAW,YAAY,KAAK,QAAQ,IAAI,UAAU,KAAK,UACvD,SAAS,CAAC,GAAG,SAAS;AACpB,QAAI,QAAQ,GAAG,SAAS,KAAK,YAAY,IAAI,GAAG,KAAK,YAAY,UAAU,QAAQ;AACnF,UAAM,YAAY,IAAI,IAAI,MAAM,IAAI,EAAE,aAAa,IAAI;AACvD,WAAO,UAAU,OAAO,GAAG,IAAI;AAAA,EACjC,GACA,SAAS,CAAC,SAAS;AACjB,QAAI,KAAK,WAAW;AAClB,UAAI,CAAC,IAAI,QAAQ,OAAO,IAAI,GAAG;AAC7B,eAAO,IAAI,QAAQ,IAAI,EAAE,KAAK,MAAM,QAAQ,IAAI,GAAG,IAAI,EAAE,IAAI,IAAI;AAAA,MACnE,MAAO,QAAO;AAAA,IAChB,OAAO;AACL,aAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI,IAAI;AAAA,IACvC;AAAA,EACF;AAEF,MAAI,KAAK,MAAM;AACb,WAAO,OAAO,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI;AAAA,EAC5C;AAEA,aAAW,QAAQ,KAAK,OAAO;AAC7B,UAAM,QAAQ,OAAO,IAAI;AACzB,QAAI,KAAK,IAAI,KAAK,KAAK,GAAG;AACxB,aAAO,OAAO,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,OAAO,QAAQ,MAAM,KAAK,GAAG,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,CAAC;AACvE;AAEA,SAAS,SAAS,SAAS;AACzB,MAAI,OAAO,CAAC,GACV;AACF,MAAI,QAAQ,SAAS,KAAK,OAAO,QAAQ,QAAQ,SAAS,CAAC,MAAM,UAAU;AACzE,WAAO,QAAQ,QAAQ,SAAS,CAAC;AACjC,WAAO,MAAM,KAAK,OAAO,EAAE,MAAM,GAAG,QAAQ,SAAS,CAAC;AAAA,EACxD,OAAO;AACL,WAAO,MAAM,KAAK,OAAO;AAAA,EAC3B;AACA,SAAO,CAAC,MAAM,IAAI;AACpB;AAKA,IAAI;AAOJ,IAAM,uBAAuB,oBAAI,IAAI;AAsBrC,IAAqB,WAArB,MAAqB,UAAS;AAAA;AAAA;AAAA;AAAA,EAI5B,YAAY,QAAQ;AAClB,UAAM,OAAO,OAAO,QAAQ,SAAS;AAErC,QAAI,UACF,OAAO,YACN,OAAO,MAAM,OAAO,EAAE,IAAI,IAAI,QAAQ,eAAe,IAAI,UACzD,CAAC,KAAK,UAAU,gBAAgB,IAAI,IAAI;AAI3C,SAAK,KAAK,YAAY,OAAO,EAAE,IAAI,SAAS,IAAI,IAAI,OAAO;AAE3D,QAAI,IAAI,MACN,IAAI;AACN,QAAI,CAAC,SAAS;AACZ,YAAM,YAAY,OAAO,OAAO,OAAO,IAAI,OAAO,KAAK,MAAM,OAAO,IAAI,KAAK,OAAO,IAAI;AAExF,UAAI,WAAW;AACb,SAAC,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC;AAAA,MACtC,OAAO;AAGL,cAAM,KAAK,SAAS,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,OAAO,IAAI,KAAK,OAAO,KAAK,EAAE;AAC7E,YAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,kBAAU,OAAO,MAAM,EAAE,IAAI,IAAI,IAAI,QAAQ,eAAe,IAAI;AAChE,YAAI,UAAU,OAAO;AACrB,YAAI,UAAU,OAAO;AAAA,MACvB;AAAA,IACF;AAKA,SAAK,QAAQ;AAIb,SAAK,MAAM,OAAO,OAAO,OAAO,OAAO;AAIvC,SAAK,UAAU;AAIf,SAAK,WAAW;AAIhB,SAAK,gBAAgB;AAIrB,SAAK,IAAI;AAIT,SAAK,IAAI;AAIT,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,MAAM;AACX,WAAO,IAAI,UAAS,CAAC,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,OAAO,QAAQ;AACb,UAAM,CAAC,MAAM,IAAI,IAAI,SAAS,SAAS,GACrC,CAAC,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,WAAW,IAAI;AAC1D,WAAO,QAAQ,EAAE,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,YAAY,GAAG,IAAI;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,OAAO,MAAM;AACX,UAAM,CAAC,MAAM,IAAI,IAAI,SAAS,SAAS,GACrC,CAAC,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,WAAW,IAAI;AAE1D,SAAK,OAAO,gBAAgB;AAC5B,WAAO,QAAQ,EAAE,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,YAAY,GAAG,IAAI;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,WAAW,MAAM,UAAU,CAAC,GAAG;AACpC,UAAM,KAAK,OAAO,IAAI,IAAI,KAAK,QAAQ,IAAI;AAC3C,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,aAAO,UAAS,QAAQ,eAAe;AAAA,IACzC;AAEA,UAAM,YAAY,cAAc,QAAQ,MAAM,SAAS,WAAW;AAClE,QAAI,CAAC,UAAU,SAAS;AACtB,aAAO,UAAS,QAAQ,gBAAgB,SAAS,CAAC;AAAA,IACpD;AAEA,WAAO,IAAI,UAAS;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,MACN,KAAK,OAAO,WAAW,OAAO;AAAA,IAChC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,WAAW,cAAc,UAAU,CAAC,GAAG;AAC5C,QAAI,CAAC,SAAS,YAAY,GAAG;AAC3B,YAAM,IAAI;AAAA,QACR,yDAAyD,OAAO,YAAY,eAAe,YAAY;AAAA,MACzG;AAAA,IACF,WAAW,eAAe,CAAC,YAAY,eAAe,UAAU;AAE9D,aAAO,UAAS,QAAQ,wBAAwB;AAAA,IAClD,OAAO;AACL,aAAO,IAAI,UAAS;AAAA,QAClB,IAAI;AAAA,QACJ,MAAM,cAAc,QAAQ,MAAM,SAAS,WAAW;AAAA,QACtD,KAAK,OAAO,WAAW,OAAO;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,YAAY,SAAS,UAAU,CAAC,GAAG;AACxC,QAAI,CAAC,SAAS,OAAO,GAAG;AACtB,YAAM,IAAI,qBAAqB,wCAAwC;AAAA,IACzE,OAAO;AACL,aAAO,IAAI,UAAS;AAAA,QAClB,IAAI,UAAU;AAAA,QACd,MAAM,cAAc,QAAQ,MAAM,SAAS,WAAW;AAAA,QACtD,KAAK,OAAO,WAAW,OAAO;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmCA,OAAO,WAAW,KAAK,OAAO,CAAC,GAAG;AAChC,UAAM,OAAO,CAAC;AACd,UAAM,YAAY,cAAc,KAAK,MAAM,SAAS,WAAW;AAC/D,QAAI,CAAC,UAAU,SAAS;AACtB,aAAO,UAAS,QAAQ,gBAAgB,SAAS,CAAC;AAAA,IACpD;AAEA,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,UAAM,aAAa,gBAAgB,KAAK,2BAA2B;AACnE,UAAM,EAAE,oBAAoB,YAAY,IAAI,oBAAoB,YAAY,GAAG;AAE/E,UAAM,QAAQ,SAAS,IAAI,GACzB,eAAe,CAAC,YAAY,KAAK,cAAc,IAC3C,KAAK,iBACL,UAAU,OAAO,KAAK,GAC1B,kBAAkB,CAAC,YAAY,WAAW,OAAO,GACjD,qBAAqB,CAAC,YAAY,WAAW,IAAI,GACjD,mBAAmB,CAAC,YAAY,WAAW,KAAK,KAAK,CAAC,YAAY,WAAW,GAAG,GAChF,iBAAiB,sBAAsB,kBACvC,kBAAkB,WAAW,YAAY,WAAW;AAQtD,SAAK,kBAAkB,oBAAoB,iBAAiB;AAC1D,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,oBAAoB,iBAAiB;AACvC,YAAM,IAAI,8BAA8B,wCAAwC;AAAA,IAClF;AAEA,UAAM,cAAc,mBAAoB,WAAW,WAAW,CAAC;AAG/D,QAAI,OACF,eACA,SAAS,QAAQ,OAAO,YAAY;AACtC,QAAI,aAAa;AACf,cAAQ;AACR,sBAAgB;AAChB,eAAS,gBAAgB,QAAQ,oBAAoB,WAAW;AAAA,IAClE,WAAW,iBAAiB;AAC1B,cAAQ;AACR,sBAAgB;AAChB,eAAS,mBAAmB,MAAM;AAAA,IACpC,OAAO;AACL,cAAQA;AACR,sBAAgB;AAAA,IAClB;AAGA,QAAI,aAAa;AACjB,eAAW,KAAK,OAAO;AACrB,YAAM,IAAI,WAAW,CAAC;AACtB,UAAI,CAAC,YAAY,CAAC,GAAG;AACnB,qBAAa;AAAA,MACf,WAAW,YAAY;AACrB,mBAAW,CAAC,IAAI,cAAc,CAAC;AAAA,MACjC,OAAO;AACL,mBAAW,CAAC,IAAI,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF;AAGA,UAAM,qBAAqB,cACrB,mBAAmB,YAAY,oBAAoB,WAAW,IAC9D,kBACA,sBAAsB,UAAU,IAChC,wBAAwB,UAAU,GACtC,UAAU,sBAAsB,mBAAmB,UAAU;AAE/D,QAAI,SAAS;AACX,aAAO,UAAS,QAAQ,OAAO;AAAA,IACjC;AAGA,UAAM,YAAY,cACZ,gBAAgB,YAAY,oBAAoB,WAAW,IAC3D,kBACA,mBAAmB,UAAU,IAC7B,YACJ,CAAC,SAAS,WAAW,IAAI,QAAQ,WAAW,cAAc,SAAS,GACnE,OAAO,IAAI,UAAS;AAAA,MAClB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAGH,QAAI,WAAW,WAAW,kBAAkB,IAAI,YAAY,KAAK,SAAS;AACxE,aAAO,UAAS;AAAA,QACd;AAAA,QACA,uCAAuC,WAAW,OAAO,kBAAkB,KAAK,MAAM,CAAC;AAAA,MACzF;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO,UAAS,QAAQ,KAAK,OAAO;AAAA,IACtC;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,OAAO,QAAQ,MAAM,OAAO,CAAC,GAAG;AAC9B,UAAM,CAAC,MAAM,UAAU,IAAI,aAAa,IAAI;AAC5C,WAAO,oBAAoB,MAAM,YAAY,MAAM,YAAY,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,OAAO,YAAY,MAAM,OAAO,CAAC,GAAG;AAClC,UAAM,CAAC,MAAM,UAAU,IAAI,iBAAiB,IAAI;AAChD,WAAO,oBAAoB,MAAM,YAAY,MAAM,YAAY,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,SAAS,MAAM,OAAO,CAAC,GAAG;AAC/B,UAAM,CAAC,MAAM,UAAU,IAAI,cAAc,IAAI;AAC7C,WAAO,oBAAoB,MAAM,YAAY,MAAM,QAAQ,IAAI;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,GAAG;AACtC,QAAI,YAAY,IAAI,KAAK,YAAY,GAAG,GAAG;AACzC,YAAM,IAAI,qBAAqB,kDAAkD;AAAA,IACnF;AAEA,UAAM,EAAE,SAAS,MAAM,kBAAkB,KAAK,IAAI,MAChD,cAAc,OAAO,SAAS;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf,CAAC,GACD,CAAC,MAAM,YAAY,gBAAgB,OAAO,IAAI,gBAAgB,aAAa,MAAM,GAAG;AACtF,QAAI,SAAS;AACX,aAAO,UAAS,QAAQ,OAAO;AAAA,IACjC,OAAO;AACL,aAAO,oBAAoB,MAAM,YAAY,MAAM,UAAU,GAAG,IAAI,MAAM,cAAc;AAAA,IAC1F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAAW,MAAM,KAAK,OAAO,CAAC,GAAG;AACtC,WAAO,UAAS,WAAW,MAAM,KAAK,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,OAAO,QAAQ,MAAM,OAAO,CAAC,GAAG;AAC9B,UAAM,CAAC,MAAM,UAAU,IAAI,SAAS,IAAI;AACxC,WAAO,oBAAoB,MAAM,YAAY,MAAM,OAAO,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,QAAQ,QAAQ,cAAc,MAAM;AACzC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,qBAAqB,kDAAkD;AAAA,IACnF;AAEA,UAAM,UAAU,kBAAkB,UAAU,SAAS,IAAI,QAAQ,QAAQ,WAAW;AAEpF,QAAI,SAAS,gBAAgB;AAC3B,YAAM,IAAI,qBAAqB,OAAO;AAAA,IACxC,OAAO;AACL,aAAO,IAAI,UAAS,EAAE,QAAQ,CAAC;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,WAAW,GAAG;AACnB,WAAQ,KAAK,EAAE,mBAAoB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,mBAAmB,YAAY,aAAa,CAAC,GAAG;AACrD,UAAM,YAAY,mBAAmB,YAAY,OAAO,WAAW,UAAU,CAAC;AAC9E,WAAO,CAAC,YAAY,OAAO,UAAU,IAAI,CAAC,MAAO,IAAI,EAAE,MAAM,IAAK,EAAE,KAAK,EAAE;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,aAAa,KAAK,aAAa,CAAC,GAAG;AACxC,UAAM,WAAW,kBAAkB,UAAU,YAAY,GAAG,GAAG,OAAO,WAAW,UAAU,CAAC;AAC5F,WAAO,SAAS,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;AAAA,EAC3C;AAAA,EAEA,OAAO,aAAa;AAClB,mBAAe;AACf,yBAAqB,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU,KAAK,QAAQ,cAAc;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,KAAK,IAAI,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,kBAAkB;AACpB,WAAO,KAAK,UAAU,KAAK,IAAI,kBAAkB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,iBAAiB;AACnB,WAAO,KAAK,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,KAAK,KAAK,OAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU,KAAK,EAAE,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,KAAK,KAAK,EAAE,QAAQ,CAAC,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,EAAE,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAM;AACR,WAAO,KAAK,UAAU,KAAK,EAAE,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,WAAO,KAAK,UAAU,KAAK,EAAE,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,KAAK,EAAE,SAAS;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,KAAK,EAAE,SAAS;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,cAAc;AAChB,WAAO,KAAK,UAAU,KAAK,EAAE,cAAc;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,uBAAuB,IAAI,EAAE,WAAW;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,uBAAuB,IAAI,EAAE,aAAa;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,uBAAuB,IAAI,EAAE,UAAU;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACd,WAAO,KAAK,WAAW,KAAK,IAAI,eAAe,EAAE,SAAS,KAAK,OAAO;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU,4BAA4B,IAAI,EAAE,UAAU;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,kBAAkB;AACpB,WAAO,KAAK,UAAU,4BAA4B,IAAI,EAAE,aAAa;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,4BAA4B,IAAI,EAAE,WAAW;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,mBAAmB,KAAK,CAAC,EAAE,UAAU;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,KAAK,OAAO,SAAS,EAAE,QAAQ,KAAK,IAAI,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,YAAY;AACd,WAAO,KAAK,UAAU,KAAK,OAAO,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU,KAAK,SAAS,SAAS,EAAE,QAAQ,KAAK,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,IAAI;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,cAAc;AAChB,WAAO,KAAK,UAAU,KAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,IAAI;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,CAAC,KAAK,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,kBAAkB;AACpB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,KAAK,WAAW,KAAK,IAAI;AAAA,QACnC,QAAQ;AAAA,QACR,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,iBAAiB;AACnB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,KAAK,WAAW,KAAK,IAAI;AAAA,QACnC,QAAQ;AAAA,QACR,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,KAAK,cAAc;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,QAAI,KAAK,eAAe;AACtB,aAAO;AAAA,IACT,OAAO;AACL,aACE,KAAK,SAAS,KAAK,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,CAAC,EAAE,UAC7C,KAAK,SAAS,KAAK,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;AAAA,IAEzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB;AACnB,QAAI,CAAC,KAAK,WAAW,KAAK,eAAe;AACvC,aAAO,CAAC,IAAI;AAAA,IACd;AACA,UAAM,QAAQ;AACd,UAAM,WAAW;AACjB,UAAM,UAAU,aAAa,KAAK,CAAC;AACnC,UAAM,WAAW,KAAK,KAAK,OAAO,UAAU,KAAK;AACjD,UAAM,SAAS,KAAK,KAAK,OAAO,UAAU,KAAK;AAE/C,UAAM,KAAK,KAAK,KAAK,OAAO,UAAU,WAAW,QAAQ;AACzD,UAAM,KAAK,KAAK,KAAK,OAAO,UAAU,SAAS,QAAQ;AACvD,QAAI,OAAO,IAAI;AACb,aAAO,CAAC,IAAI;AAAA,IACd;AACA,UAAM,MAAM,UAAU,KAAK;AAC3B,UAAM,MAAM,UAAU,KAAK;AAC3B,UAAM,KAAK,QAAQ,KAAK,EAAE;AAC1B,UAAM,KAAK,QAAQ,KAAK,EAAE;AAC1B,QACE,GAAG,SAAS,GAAG,QACf,GAAG,WAAW,GAAG,UACjB,GAAG,WAAW,GAAG,UACjB,GAAG,gBAAgB,GAAG,aACtB;AACA,aAAO,CAACF,OAAM,MAAM,EAAE,IAAI,IAAI,CAAC,GAAGA,OAAM,MAAM,EAAE,IAAI,IAAI,CAAC,CAAC;AAAA,IAC5D;AACA,WAAO,CAAC,IAAI;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,eAAe;AACjB,WAAO,WAAW,KAAK,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,cAAc;AAChB,WAAO,YAAY,KAAK,MAAM,KAAK,KAAK;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,WAAW,KAAK,IAAI,IAAI;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,kBAAkB;AACpB,WAAO,KAAK,UAAU,gBAAgB,KAAK,QAAQ,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,uBAAuB;AACzB,WAAO,KAAK,UACR;AAAA,MACE,KAAK;AAAA,MACL,KAAK,IAAI,sBAAsB;AAAA,MAC/B,KAAK,IAAI,eAAe;AAAA,IAC1B,IACA;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,OAAO,CAAC,GAAG;AAC/B,UAAM,EAAE,QAAQ,iBAAiB,SAAS,IAAI,UAAU;AAAA,MACtD,KAAK,IAAI,MAAM,IAAI;AAAA,MACnB;AAAA,IACF,EAAE,gBAAgB,IAAI;AACtB,WAAO,EAAE,QAAQ,iBAAiB,gBAAgB,SAAS;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAMC,UAAS,GAAG,OAAO,CAAC,GAAG;AAC3B,WAAO,KAAK,QAAQ,gBAAgB,SAASA,OAAM,GAAG,IAAI;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,MAAM,EAAE,gBAAgB,OAAO,mBAAmB,MAAM,IAAI,CAAC,GAAG;AACtE,WAAO,cAAc,MAAM,SAAS,WAAW;AAC/C,QAAI,KAAK,OAAO,KAAK,IAAI,GAAG;AAC1B,aAAO;AAAA,IACT,WAAW,CAAC,KAAK,SAAS;AACxB,aAAO,UAAS,QAAQ,gBAAgB,IAAI,CAAC;AAAA,IAC/C,OAAO;AACL,UAAI,QAAQ,KAAK;AACjB,UAAI,iBAAiB,kBAAkB;AACrC,cAAM,cAAc,KAAK,OAAO,KAAK,EAAE;AACvC,cAAM,QAAQ,KAAK,SAAS;AAC5B,SAAC,KAAK,IAAI,QAAQ,OAAO,aAAa,IAAI;AAAA,MAC5C;AACA,aAAOD,OAAM,MAAM,EAAE,IAAI,OAAO,KAAK,CAAC;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,EAAE,QAAQ,iBAAiB,eAAe,IAAI,CAAC,GAAG;AAC5D,UAAM,MAAM,KAAK,IAAI,MAAM,EAAE,QAAQ,iBAAiB,eAAe,CAAC;AACtE,WAAOA,OAAM,MAAM,EAAE,IAAI,CAAC;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,WAAO,KAAK,YAAY,EAAE,OAAO,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,IAAI,QAAQ;AACV,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,aAAa,gBAAgB,QAAQ,2BAA2B;AACtE,UAAM,EAAE,oBAAoB,YAAY,IAAI,oBAAoB,YAAY,KAAK,GAAG;AAEpF,UAAM,mBACF,CAAC,YAAY,WAAW,QAAQ,KAChC,CAAC,YAAY,WAAW,UAAU,KAClC,CAAC,YAAY,WAAW,OAAO,GACjC,kBAAkB,CAAC,YAAY,WAAW,OAAO,GACjD,qBAAqB,CAAC,YAAY,WAAW,IAAI,GACjD,mBAAmB,CAAC,YAAY,WAAW,KAAK,KAAK,CAAC,YAAY,WAAW,GAAG,GAChF,iBAAiB,sBAAsB,kBACvC,kBAAkB,WAAW,YAAY,WAAW;AAEtD,SAAK,kBAAkB,oBAAoB,iBAAiB;AAC1D,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,oBAAoB,iBAAiB;AACvC,YAAM,IAAI,8BAA8B,wCAAwC;AAAA,IAClF;AAEA,QAAI;AACJ,QAAI,kBAAkB;AACpB,cAAQ;AAAA,QACN,kCAAK,gBAAgB,KAAK,GAAG,oBAAoB,WAAW,IAAM;AAAA,QAClE;AAAA,QACA;AAAA,MACF;AAAA,IACF,WAAW,CAAC,YAAY,WAAW,OAAO,GAAG;AAC3C,cAAQ,mBAAmB,kCAAK,mBAAmB,KAAK,CAAC,IAAM,WAAY;AAAA,IAC7E,OAAO;AACL,cAAQ,kCAAK,KAAK,SAAS,IAAM;AAIjC,UAAI,YAAY,WAAW,GAAG,GAAG;AAC/B,cAAM,MAAM,KAAK,IAAI,YAAY,MAAM,MAAM,MAAM,KAAK,GAAG,MAAM,GAAG;AAAA,MACtE;AAAA,IACF;AAEA,UAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,OAAO,KAAK,GAAG,KAAK,IAAI;AAChD,WAAOA,OAAM,MAAM,EAAE,IAAI,EAAE,CAAC;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,KAAK,UAAU;AACb,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,MAAM,SAAS,iBAAiB,QAAQ;AAC9C,WAAOA,OAAM,MAAM,WAAW,MAAM,GAAG,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,UAAU;AACd,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,MAAM,SAAS,iBAAiB,QAAQ,EAAE,OAAO;AACvD,WAAOA,OAAM,MAAM,WAAW,MAAM,GAAG,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,QAAQ,MAAM,EAAE,iBAAiB,MAAM,IAAI,CAAC,GAAG;AAC7C,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,IAAI,CAAC,GACT,iBAAiB,SAAS,cAAc,IAAI;AAC9C,YAAQ,gBAAgB;AAAA,MACtB,KAAK;AACH,UAAE,QAAQ;AAAA,MAEZ,KAAK;AAAA,MACL,KAAK;AACH,UAAE,MAAM;AAAA,MAEV,KAAK;AAAA,MACL,KAAK;AACH,UAAE,OAAO;AAAA,MAEX,KAAK;AACH,UAAE,SAAS;AAAA,MAEb,KAAK;AACH,UAAE,SAAS;AAAA,MAEb,KAAK;AACH,UAAE,cAAc;AAChB;AAAA,MACF,KAAK;AACH;AAAA,IAEJ;AAEA,QAAI,mBAAmB,SAAS;AAC9B,UAAI,gBAAgB;AAClB,cAAM,cAAc,KAAK,IAAI,eAAe;AAC5C,cAAM,EAAE,QAAQ,IAAI;AACpB,YAAI,UAAU,aAAa;AACzB,YAAE,aAAa,KAAK,aAAa;AAAA,QACnC;AACA,UAAE,UAAU;AAAA,MACd,OAAO;AACL,UAAE,UAAU;AAAA,MACd;AAAA,IACF;AAEA,QAAI,mBAAmB,YAAY;AACjC,YAAM,IAAI,KAAK,KAAK,KAAK,QAAQ,CAAC;AAClC,QAAE,SAAS,IAAI,KAAK,IAAI;AAAA,IAC1B;AAEA,WAAO,KAAK,IAAI,CAAC;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,MAAM,MAAM;AAChB,WAAO,KAAK,UACR,KAAK,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EACpB,QAAQ,MAAM,IAAI,EAClB,MAAM,CAAC,IACV;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,SAAS,KAAK,OAAO,CAAC,GAAG;AACvB,WAAO,KAAK,UACR,UAAU,OAAO,KAAK,IAAI,cAAc,IAAI,CAAC,EAAE,yBAAyB,MAAM,GAAG,IACjFD;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,eAAe,aAAqB,YAAY,OAAO,CAAC,GAAG;AACzD,WAAO,KAAK,UACR,UAAU,OAAO,KAAK,IAAI,MAAM,IAAI,GAAG,UAAU,EAAE,eAAe,IAAI,IACtEA;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,cAAc,OAAO,CAAC,GAAG;AACvB,WAAO,KAAK,UACR,UAAU,OAAO,KAAK,IAAI,MAAM,IAAI,GAAG,IAAI,EAAE,oBAAoB,IAAI,IACrE,CAAC;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM;AAAA,IACJ,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,YAAY;AAAA,EACd,IAAI,CAAC,GAAG;AACN,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AAEA,gBAAY,cAAc,SAAS;AACnC,UAAM,MAAM,WAAW;AAEvB,QAAI,IAAI,UAAU,MAAM,KAAK,SAAS;AACtC,QAAIG,cAAa,QAAQ,SAAS,KAAK,EAAG,MAAK;AAC/C,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,EAAE,SAAS,YAAY,YAAY,MAAM,IAAI,CAAC,GAAG;AACzD,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,WAAO,UAAU,MAAM,WAAW,YAAY,cAAc,SAAS,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,aAAa,MAAM,cAAc;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,UAAU;AAAA,IACR,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,YAAY;AAAA,EACd,IAAI,CAAC,GAAG;AACN,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AAEA,gBAAY,cAAc,SAAS;AACnC,QAAI,IAAI,iBAAiBA,cAAa,QAAQ,SAAS,KAAK,IAAI,MAAM;AACtE,WACE,IACA;AAAA,MACE;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,aAAa,MAAM,iCAAiC,KAAK;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SAAS;AACP,WAAO,aAAa,KAAK,MAAM,GAAG,iCAAiC;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,WAAO,UAAU,MAAM,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,UAAU,EAAE,gBAAgB,MAAM,cAAc,OAAO,qBAAqB,KAAK,IAAI,CAAC,GAAG;AACvF,QAAI,MAAM;AAEV,QAAI,eAAe,eAAe;AAChC,UAAI,oBAAoB;AACtB,eAAO;AAAA,MACT;AACA,UAAI,aAAa;AACf,eAAO;AAAA,MACT,WAAW,eAAe;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,aAAa,MAAM,KAAK,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,OAAO,CAAC,GAAG;AACf,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,GAAG,KAAK,UAAU,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK,UAAU,KAAK,MAAM,IAAIH;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,CAAC,OAAO,IAAI,4BAA4B,CAAC,IAAI;AAC3C,QAAI,KAAK,SAAS;AAChB,aAAO,kBAAkB,KAAK,MAAM,CAAC,WAAW,KAAK,KAAK,IAAI,aAAa,KAAK,MAAM;AAAA,IACxF,OAAO;AACL,aAAO,+BAA+B,KAAK,aAAa;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK,UAAU,KAAK,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACV,WAAO,KAAK,UAAU,KAAK,KAAK,MAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,WAAO,KAAK,UAAU,KAAK,MAAM,KAAK,KAAK,GAAI,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,OAAO,CAAC,GAAG;AAClB,QAAI,CAAC,KAAK,QAAS,QAAO,CAAC;AAE3B,UAAM,OAAO,mBAAK,KAAK;AAEvB,QAAI,KAAK,eAAe;AACtB,WAAK,iBAAiB,KAAK;AAC3B,WAAK,kBAAkB,KAAK,IAAI;AAChC,WAAK,SAAS,KAAK,IAAI;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,IAAI,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,KAAK,eAAe,OAAO,gBAAgB,OAAO,CAAC,GAAG;AACpD,QAAI,CAAC,KAAK,WAAW,CAAC,cAAc,SAAS;AAC3C,aAAO,SAAS,QAAQ,wCAAwC;AAAA,IAClE;AAEA,UAAM,UAAU,iBAAE,QAAQ,KAAK,QAAQ,iBAAiB,KAAK,mBAAoB;AAEjF,UAAM,QAAQ,WAAW,IAAI,EAAE,IAAI,SAAS,aAAa,GACvD,eAAe,cAAc,QAAQ,IAAI,KAAK,QAAQ,GACtD,UAAU,eAAe,OAAO,eAChC,QAAQ,eAAe,gBAAgB,MACvC,SAAS,aAAK,SAAS,OAAO,OAAO,OAAO;AAE9C,WAAO,eAAe,OAAO,OAAO,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,OAAO,gBAAgB,OAAO,CAAC,GAAG;AACxC,WAAO,KAAK,KAAK,UAAS,IAAI,GAAG,MAAM,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe;AACnB,WAAO,KAAK,UAAU,SAAS,cAAc,MAAM,aAAa,IAAI;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,QAAQ,eAAe,MAAM,MAAM;AACjC,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,UAAM,UAAU,cAAc,QAAQ;AACtC,UAAM,iBAAiB,KAAK,QAAQ,cAAc,MAAM,EAAE,eAAe,KAAK,CAAC;AAC/E,WACE,eAAe,QAAQ,MAAM,IAAI,KAAK,WAAW,WAAW,eAAe,MAAM,MAAM,IAAI;AAAA,EAE/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,OAAO;AACZ,WACE,KAAK,WACL,MAAM,WACN,KAAK,QAAQ,MAAM,MAAM,QAAQ,KACjC,KAAK,KAAK,OAAO,MAAM,IAAI,KAC3B,KAAK,IAAI,OAAO,MAAM,GAAG;AAAA,EAE7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,WAAW,UAAU,CAAC,GAAG;AACvB,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,UAAM,OAAO,QAAQ,QAAQ,UAAS,WAAW,CAAC,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC,GACtE,UAAU,QAAQ,UAAW,OAAO,OAAO,CAAC,QAAQ,UAAU,QAAQ,UAAW;AACnF,QAAI,QAAQ,CAAC,SAAS,UAAU,QAAQ,SAAS,WAAW,SAAS;AACrE,QAAI,OAAO,QAAQ;AACnB,QAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAC/B,cAAQ,QAAQ;AAChB,aAAO;AAAA,IACT;AACA,WAAO,aAAa,MAAM,KAAK,KAAK,OAAO,GAAG,iCACzC,UADyC;AAAA,MAE5C,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF,EAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,mBAAmB,UAAU,CAAC,GAAG;AAC/B,QAAI,CAAC,KAAK,QAAS,QAAO;AAE1B,WAAO,aAAa,QAAQ,QAAQ,UAAS,WAAW,CAAC,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC,GAAG,MAAM,iCACnF,UADmF;AAAA,MAEtF,SAAS;AAAA,MACT,OAAO,CAAC,SAAS,UAAU,MAAM;AAAA,MACjC,WAAW;AAAA,IACb,EAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,WAAW;AACvB,QAAI,CAAC,UAAU,MAAM,UAAS,UAAU,GAAG;AACzC,YAAM,IAAI,qBAAqB,yCAAyC;AAAA,IAC1E;AACA,WAAO,OAAO,WAAW,CAAC,MAAM,EAAE,QAAQ,GAAG,KAAK,GAAG;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,WAAW;AACvB,QAAI,CAAC,UAAU,MAAM,UAAS,UAAU,GAAG;AACzC,YAAM,IAAI,qBAAqB,yCAAyC;AAAA,IAC1E;AACA,WAAO,OAAO,WAAW,CAAC,MAAM,EAAE,QAAQ,GAAG,KAAK,GAAG;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,kBAAkB,MAAM,KAAK,UAAU,CAAC,GAAG;AAChD,UAAM,EAAE,SAAS,MAAM,kBAAkB,KAAK,IAAI,SAChD,cAAc,OAAO,SAAS;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AACH,WAAO,kBAAkB,aAAa,MAAM,GAAG;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,kBAAkB,MAAM,KAAK,UAAU,CAAC,GAAG;AAChD,WAAO,UAAS,kBAAkB,MAAM,KAAK,OAAO;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,kBAAkB,KAAK,UAAU,CAAC,GAAG;AAC1C,UAAM,EAAE,SAAS,MAAM,kBAAkB,KAAK,IAAI,SAChD,cAAc,OAAO,SAAS;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AACH,WAAO,IAAI,YAAY,aAAa,GAAG;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,iBAAiB,MAAM,cAAc,OAAO,CAAC,GAAG;AACrD,QAAI,YAAY,IAAI,KAAK,YAAY,YAAY,GAAG;AAClD,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,UAAM,EAAE,SAAS,MAAM,kBAAkB,KAAK,IAAI,MAChD,cAAc,OAAO,SAAS;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAEH,QAAI,CAAC,YAAY,OAAO,aAAa,MAAM,GAAG;AAC5C,YAAM,IAAI;AAAA,QACR,4CAA4C,WAAW,2CACZ,aAAa,MAAM;AAAA,MAChE;AAAA,IACF;AAEA,UAAM,EAAE,QAAQ,MAAM,gBAAgB,cAAc,IAAI,aAAa,kBAAkB,IAAI;AAE3F,QAAI,eAAe;AACjB,aAAO,UAAS,QAAQ,aAAa;AAAA,IACvC,OAAO;AACL,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,aAAa,MAAM;AAAA,QAC7B;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,aAAa;AACtB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,WAAW;AACpB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,wBAAwB;AACjC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,YAAY;AACrB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,YAAY;AACrB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,cAAc;AACvB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,oBAAoB;AAC7B,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,yBAAyB;AAClC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,wBAAwB;AACjC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,iBAAiB;AAC1B,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,uBAAuB;AAChC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,4BAA4B;AACrC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,2BAA2B;AACpC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,iBAAiB;AAC1B,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,8BAA8B;AACvC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,eAAe;AACxB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,4BAA4B;AACrC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,4BAA4B;AACrC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,gBAAgB;AACzB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,6BAA6B;AACtC,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,gBAAgB;AACzB,WAAe;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,6BAA6B;AACtC,WAAe;AAAA,EACjB;AACF;AAKO,SAAS,iBAAiB,aAAa;AAC5C,MAAI,SAAS,WAAW,WAAW,GAAG;AACpC,WAAO;AAAA,EACT,WAAW,eAAe,YAAY,WAAW,SAAS,YAAY,QAAQ,CAAC,GAAG;AAChF,WAAO,SAAS,WAAW,WAAW;AAAA,EACxC,WAAW,eAAe,OAAO,gBAAgB,UAAU;AACzD,WAAO,SAAS,WAAW,WAAW;AAAA,EACxC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,8BAA8B,WAAW,aAAa,OAAO,WAAW;AAAA,IAC1E;AAAA,EACF;AACF;;;AC/hFA,IAAM,UAAU;", "names": ["s", "singleton", "offset", "s", "defaultZone", "offset", "n", "n", "offset", "s", "n", "formatOffset", "s", "match", "offset", "l", "n", "s", "INVALID", "s", "e", "s", "INVALID", "clone", "offset", "orderedUnits"]}