{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-skeleton.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-card.mjs"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>orO<PERSON> } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport { InputBoolean, toCssPixel } from 'ng-zorro-antd/core/util';\nimport { __decorate } from 'tslib';\nconst _c0 = [\"nzType\", \"button\"];\nconst _c1 = [\"nzType\", \"avatar\"];\nconst _c2 = [\"nzType\", \"input\"];\nconst _c3 = [\"nzType\", \"image\"];\nconst _c4 = [\"*\"];\nfunction NzSkeletonComponent_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"nz-skeleton-element\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzSize\", ctx_r0.avatar.size || \"default\")(\"nzShape\", ctx_r0.avatar.shape || \"circle\");\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_h3_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"h3\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.toCSSUnit(ctx_r0.title.width));\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_ul_4_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\");\n  }\n  if (rf & 2) {\n    const i_r2 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.toCSSUnit(ctx_r0.widthList[i_r2]));\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 8);\n    i0.ɵɵtemplate(1, NzSkeletonComponent_ng_container_0_ul_4_li_1_Template, 1, 2, \"li\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.rowsList);\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSkeletonComponent_ng_container_0_div_1_Template, 2, 2, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 2);\n    i0.ɵɵtemplate(3, NzSkeletonComponent_ng_container_0_h3_3_Template, 1, 2, \"h3\", 3)(4, NzSkeletonComponent_ng_container_0_ul_4_Template, 2, 1, \"ul\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r0.nzAvatar);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r0.nzTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r0.nzParagraph);\n  }\n}\nfunction NzSkeletonComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nclass NzSkeletonElementDirective {\n  constructor() {\n    this.nzActive = false;\n    this.nzBlock = false;\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementDirective_Factory(t) {\n      return new (t || NzSkeletonElementDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzSkeletonElementDirective,\n      selectors: [[\"nz-skeleton-element\"]],\n      hostAttrs: [1, \"ant-skeleton\", \"ant-skeleton-element\"],\n      hostVars: 4,\n      hostBindings: function NzSkeletonElementDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-active\", ctx.nzActive)(\"ant-skeleton-block\", ctx.nzBlock);\n        }\n      },\n      inputs: {\n        nzActive: \"nzActive\",\n        nzType: \"nzType\",\n        nzBlock: \"nzBlock\"\n      },\n      standalone: true\n    });\n  }\n}\n__decorate([InputBoolean()], NzSkeletonElementDirective.prototype, \"nzBlock\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'nz-skeleton-element',\n      host: {\n        class: 'ant-skeleton ant-skeleton-element',\n        '[class.ant-skeleton-active]': 'nzActive',\n        '[class.ant-skeleton-block]': 'nzBlock'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    nzActive: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzBlock: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementButtonComponent {\n  constructor() {\n    this.nzShape = 'default';\n    this.nzSize = 'default';\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementButtonComponent_Factory(t) {\n      return new (t || NzSkeletonElementButtonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementButtonComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"button\"]],\n      inputs: {\n        nzShape: \"nzShape\",\n        nzSize: \"nzSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 1,\n      vars: 10,\n      consts: [[1, \"ant-skeleton-button\"]],\n      template: function NzSkeletonElementButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-button-square\", ctx.nzShape === \"square\")(\"ant-skeleton-button-round\", ctx.nzShape === \"round\")(\"ant-skeleton-button-circle\", ctx.nzShape === \"circle\")(\"ant-skeleton-button-lg\", ctx.nzSize === \"large\")(\"ant-skeleton-button-sm\", ctx.nzSize === \"small\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementButtonComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"button\"]',\n      template: `\n    <span\n      class=\"ant-skeleton-button\"\n      [class.ant-skeleton-button-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-button-round]=\"nzShape === 'round'\"\n      [class.ant-skeleton-button-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-button-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-button-sm]=\"nzSize === 'small'\"\n    ></span>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    nzShape: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementAvatarComponent {\n  constructor() {\n    this.nzShape = 'circle';\n    this.nzSize = 'default';\n    this.styleMap = {};\n  }\n  ngOnChanges(changes) {\n    if (changes.nzSize && typeof this.nzSize === 'number') {\n      const sideLength = `${this.nzSize}px`;\n      this.styleMap = {\n        width: sideLength,\n        height: sideLength,\n        'line-height': sideLength\n      };\n    } else {\n      this.styleMap = {};\n    }\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementAvatarComponent_Factory(t) {\n      return new (t || NzSkeletonElementAvatarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementAvatarComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"avatar\"]],\n      inputs: {\n        nzShape: \"nzShape\",\n        nzSize: \"nzSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      decls: 1,\n      vars: 9,\n      consts: [[1, \"ant-skeleton-avatar\", 3, \"ngStyle\"]],\n      template: function NzSkeletonElementAvatarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-avatar-square\", ctx.nzShape === \"square\")(\"ant-skeleton-avatar-circle\", ctx.nzShape === \"circle\")(\"ant-skeleton-avatar-lg\", ctx.nzSize === \"large\")(\"ant-skeleton-avatar-sm\", ctx.nzSize === \"small\");\n          i0.ɵɵproperty(\"ngStyle\", ctx.styleMap);\n        }\n      },\n      dependencies: [NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementAvatarComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"avatar\"]',\n      template: `\n    <span\n      class=\"ant-skeleton-avatar\"\n      [class.ant-skeleton-avatar-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-avatar-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-avatar-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-avatar-sm]=\"nzSize === 'small'\"\n      [ngStyle]=\"styleMap\"\n    ></span>\n  `,\n      imports: [NgStyle],\n      standalone: true\n    }]\n  }], null, {\n    nzShape: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementInputComponent {\n  constructor() {\n    this.nzSize = 'default';\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementInputComponent_Factory(t) {\n      return new (t || NzSkeletonElementInputComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementInputComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"input\"]],\n      inputs: {\n        nzSize: \"nzSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 1,\n      vars: 4,\n      consts: [[1, \"ant-skeleton-input\"]],\n      template: function NzSkeletonElementInputComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-input-lg\", ctx.nzSize === \"large\")(\"ant-skeleton-input-sm\", ctx.nzSize === \"small\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementInputComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"input\"]',\n      template: `\n    <span\n      class=\"ant-skeleton-input\"\n      [class.ant-skeleton-input-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-input-sm]=\"nzSize === 'small'\"\n    ></span>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementImageComponent {\n  static {\n    this.ɵfac = function NzSkeletonElementImageComponent_Factory(t) {\n      return new (t || NzSkeletonElementImageComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementImageComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"image\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c3,\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"ant-skeleton-image\"], [\"viewBox\", \"0 0 1098 1024\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"ant-skeleton-image-svg\"], [\"d\", \"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z\", 1, \"ant-skeleton-image-path\"]],\n      template: function NzSkeletonElementImageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1);\n          i0.ɵɵelement(2, \"path\", 2);\n          i0.ɵɵelementEnd()();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementImageComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"image\"]',\n      template: `\n    <span class=\"ant-skeleton-image\">\n      <svg class=\"ant-skeleton-image-svg\" viewBox=\"0 0 1098 1024\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path\n          d=\"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z\"\n          class=\"ant-skeleton-image-path\"\n        />\n      </svg>\n    </span>\n  `,\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSkeletonComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.nzActive = false;\n    this.nzLoading = true;\n    this.nzRound = false;\n    this.nzTitle = true;\n    this.nzAvatar = false;\n    this.nzParagraph = true;\n    this.rowsList = [];\n    this.widthList = [];\n  }\n  toCSSUnit(value = '') {\n    return toCssPixel(value);\n  }\n  getTitleProps() {\n    const hasAvatar = !!this.nzAvatar;\n    const hasParagraph = !!this.nzParagraph;\n    let width = '';\n    if (!hasAvatar && hasParagraph) {\n      width = '38%';\n    } else if (hasAvatar && hasParagraph) {\n      width = '50%';\n    }\n    return {\n      width,\n      ...this.getProps(this.nzTitle)\n    };\n  }\n  getAvatarProps() {\n    const shape = !!this.nzTitle && !this.nzParagraph ? 'square' : 'circle';\n    const size = 'large';\n    return {\n      shape,\n      size,\n      ...this.getProps(this.nzAvatar)\n    };\n  }\n  getParagraphProps() {\n    const hasAvatar = !!this.nzAvatar;\n    const hasTitle = !!this.nzTitle;\n    const basicProps = {};\n    // Width\n    if (!hasAvatar || !hasTitle) {\n      basicProps.width = '61%';\n    }\n    // Rows\n    if (!hasAvatar && hasTitle) {\n      basicProps.rows = 3;\n    } else {\n      basicProps.rows = 2;\n    }\n    return {\n      ...basicProps,\n      ...this.getProps(this.nzParagraph)\n    };\n  }\n  getProps(prop) {\n    return prop && typeof prop === 'object' ? prop : {};\n  }\n  getWidthList() {\n    const {\n      width,\n      rows\n    } = this.paragraph;\n    let widthList = [];\n    if (width && Array.isArray(width)) {\n      widthList = width;\n    } else if (width && !Array.isArray(width)) {\n      widthList = [];\n      widthList[rows - 1] = width;\n    }\n    return widthList;\n  }\n  updateProps() {\n    this.title = this.getTitleProps();\n    this.avatar = this.getAvatarProps();\n    this.paragraph = this.getParagraphProps();\n    this.rowsList = [...Array(this.paragraph.rows)];\n    this.widthList = this.getWidthList();\n    this.cdr.markForCheck();\n  }\n  ngOnInit() {\n    this.updateProps();\n  }\n  ngOnChanges(changes) {\n    if (changes.nzTitle || changes.nzAvatar || changes.nzParagraph) {\n      this.updateProps();\n    }\n  }\n  static {\n    this.ɵfac = function NzSkeletonComponent_Factory(t) {\n      return new (t || NzSkeletonComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonComponent,\n      selectors: [[\"nz-skeleton\"]],\n      hostAttrs: [1, \"ant-skeleton\"],\n      hostVars: 6,\n      hostBindings: function NzSkeletonComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-with-avatar\", !!ctx.nzAvatar)(\"ant-skeleton-active\", ctx.nzActive)(\"ant-skeleton-round\", !!ctx.nzRound);\n        }\n      },\n      inputs: {\n        nzActive: \"nzActive\",\n        nzLoading: \"nzLoading\",\n        nzRound: \"nzRound\",\n        nzTitle: \"nzTitle\",\n        nzAvatar: \"nzAvatar\",\n        nzParagraph: \"nzParagraph\"\n      },\n      exportAs: [\"nzSkeleton\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [\"class\", \"ant-skeleton-header\", 4, \"ngIf\"], [1, \"ant-skeleton-content\"], [\"class\", \"ant-skeleton-title\", 3, \"width\", 4, \"ngIf\"], [\"class\", \"ant-skeleton-paragraph\", 4, \"ngIf\"], [1, \"ant-skeleton-header\"], [\"nzType\", \"avatar\", 3, \"nzSize\", \"nzShape\"], [1, \"ant-skeleton-title\"], [1, \"ant-skeleton-paragraph\"], [3, \"width\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function NzSkeletonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzSkeletonComponent_ng_container_0_Template, 5, 3, \"ng-container\", 0)(1, NzSkeletonComponent_ng_container_1_Template, 2, 0, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.nzLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.nzLoading);\n        }\n      },\n      dependencies: [NzSkeletonElementDirective, NzSkeletonElementAvatarComponent, NgIf, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-skeleton',\n      exportAs: 'nzSkeleton',\n      host: {\n        class: 'ant-skeleton',\n        '[class.ant-skeleton-with-avatar]': '!!nzAvatar',\n        '[class.ant-skeleton-active]': 'nzActive',\n        '[class.ant-skeleton-round]': '!!nzRound'\n      },\n      template: `\n    <ng-container *ngIf=\"nzLoading\">\n      <div class=\"ant-skeleton-header\" *ngIf=\"!!nzAvatar\">\n        <nz-skeleton-element\n          nzType=\"avatar\"\n          [nzSize]=\"avatar.size || 'default'\"\n          [nzShape]=\"avatar.shape || 'circle'\"\n        ></nz-skeleton-element>\n      </div>\n      <div class=\"ant-skeleton-content\">\n        <h3 *ngIf=\"!!nzTitle\" class=\"ant-skeleton-title\" [style.width]=\"toCSSUnit(title.width)\"></h3>\n        <ul *ngIf=\"!!nzParagraph\" class=\"ant-skeleton-paragraph\">\n          <li *ngFor=\"let row of rowsList; let i = index\" [style.width]=\"toCSSUnit(widthList[i])\"></li>\n        </ul>\n      </div>\n    </ng-container>\n    <ng-container *ngIf=\"!nzLoading\">\n      <ng-content></ng-content>\n    </ng-container>\n  `,\n      imports: [NzSkeletonElementDirective, NzSkeletonElementAvatarComponent, NgIf, NgForOf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    nzActive: [{\n      type: Input\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzRound: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzAvatar: [{\n      type: Input\n    }],\n    nzParagraph: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSkeletonModule {\n  static {\n    this.ɵfac = function NzSkeletonModule_Factory(t) {\n      return new (t || NzSkeletonModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSkeletonModule,\n      imports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent],\n      exports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent],\n      exports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSkeletonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementButtonComponent, NzSkeletonElementDirective, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent, NzSkeletonModule };\n", "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, Component, ChangeDetectionStrategy, ViewEncapsulation, TemplateRef, ViewChild, Optional, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport { NgTemplateOutlet, NgIf, NgStyle } from '@angular/common';\nimport * as i1 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1$1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i4 from 'ng-zorro-antd/skeleton';\nimport { NzSkeletonModule } from 'ng-zorro-antd/skeleton';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nfunction NzCardMetaComponent_Conditional_0_ng_template_1_Template(rf, ctx) {}\nfunction NzCardMetaComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵtemplate(1, NzCardMetaComponent_Conditional_0_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzAvatar);\n  }\n}\nfunction NzCardMetaComponent_Conditional_1_Conditional_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzCardMetaComponent_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, NzCardMetaComponent_Conditional_1_Conditional_1_ng_container_1_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzTitle);\n  }\n}\nfunction NzCardMetaComponent_Conditional_1_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzDescription);\n  }\n}\nfunction NzCardMetaComponent_Conditional_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, NzCardMetaComponent_Conditional_1_Conditional_2_ng_container_1_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzDescription);\n  }\n}\nfunction NzCardMetaComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NzCardMetaComponent_Conditional_1_Conditional_1_Template, 2, 1, \"div\", 3)(2, NzCardMetaComponent_Conditional_1_Conditional_2_Template, 2, 1, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.nzTitle ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(2, ctx_r0.nzDescription ? 2 : -1);\n  }\n}\nconst _c0 = [\"*\"];\nfunction NzCardTabComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c1 = () => ({\n  rows: 4\n});\nfunction NzCardComponent_Conditional_0_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzCardComponent_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, NzCardComponent_Conditional_0_Conditional_2_ng_container_1_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzTitle);\n  }\n}\nfunction NzCardComponent_Conditional_0_Conditional_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzExtra);\n  }\n}\nfunction NzCardComponent_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, NzCardComponent_Conditional_0_Conditional_3_ng_container_1_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzExtra);\n  }\n}\nfunction NzCardComponent_Conditional_0_Conditional_4_ng_template_0_Template(rf, ctx) {}\nfunction NzCardComponent_Conditional_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzCardComponent_Conditional_0_Conditional_4_ng_template_0_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.listOfNzCardTabComponent.template);\n  }\n}\nfunction NzCardComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, NzCardComponent_Conditional_0_Conditional_2_Template, 2, 1, \"div\", 6)(3, NzCardComponent_Conditional_0_Conditional_3_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, NzCardComponent_Conditional_0_Conditional_4_Template, 1, 1, null, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, ctx_r0.nzTitle ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, ctx_r0.nzExtra ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, ctx_r0.listOfNzCardTabComponent ? 4 : -1);\n  }\n}\nfunction NzCardComponent_Conditional_1_ng_template_1_Template(rf, ctx) {}\nfunction NzCardComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NzCardComponent_Conditional_1_ng_template_1_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzCover);\n  }\n}\nfunction NzCardComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-skeleton\", 3);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"nzActive\", true)(\"nzTitle\", false)(\"nzParagraph\", i0.ɵɵpureFunction0(3, _c1));\n  }\n}\nfunction NzCardComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzCardComponent_Conditional_5_For_2_ng_template_2_Template(rf, ctx) {}\nfunction NzCardComponent_Conditional_5_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"span\");\n    i0.ɵɵtemplate(2, NzCardComponent_Conditional_5_For_2_ng_template_2_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", 100 / ctx_r0.nzActions.length, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", action_r2);\n  }\n}\nfunction NzCardComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 4);\n    i0.ɵɵrepeaterCreate(1, NzCardComponent_Conditional_5_For_2_Template, 3, 3, \"li\", 10, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.nzActions);\n  }\n}\nclass NzCardGridDirective {\n  constructor() {\n    this.nzHoverable = true;\n  }\n  static {\n    this.ɵfac = function NzCardGridDirective_Factory(t) {\n      return new (t || NzCardGridDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCardGridDirective,\n      selectors: [[\"\", \"nz-card-grid\", \"\"]],\n      hostAttrs: [1, \"ant-card-grid\"],\n      hostVars: 2,\n      hostBindings: function NzCardGridDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-card-hoverable\", ctx.nzHoverable);\n        }\n      },\n      inputs: {\n        nzHoverable: \"nzHoverable\"\n      },\n      exportAs: [\"nzCardGrid\"],\n      standalone: true\n    });\n  }\n}\n__decorate([InputBoolean()], NzCardGridDirective.prototype, \"nzHoverable\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCardGridDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-card-grid]',\n      exportAs: 'nzCardGrid',\n      host: {\n        class: 'ant-card-grid',\n        '[class.ant-card-hoverable]': 'nzHoverable'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    nzHoverable: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCardMetaComponent {\n  constructor() {\n    this.nzTitle = null;\n    this.nzDescription = null;\n    this.nzAvatar = null;\n  }\n  static {\n    this.ɵfac = function NzCardMetaComponent_Factory(t) {\n      return new (t || NzCardMetaComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCardMetaComponent,\n      selectors: [[\"nz-card-meta\"]],\n      hostAttrs: [1, \"ant-card-meta\"],\n      inputs: {\n        nzTitle: \"nzTitle\",\n        nzDescription: \"nzDescription\",\n        nzAvatar: \"nzAvatar\"\n      },\n      exportAs: [\"nzCardMeta\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[1, \"ant-card-meta-avatar\"], [1, \"ant-card-meta-detail\"], [3, \"ngTemplateOutlet\"], [1, \"ant-card-meta-title\"], [1, \"ant-card-meta-description\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzCardMetaComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzCardMetaComponent_Conditional_0_Template, 2, 1, \"div\", 0)(1, NzCardMetaComponent_Conditional_1_Template, 3, 2, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.nzAvatar ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.nzTitle || ctx.nzDescription ? 1 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzOutletModule, i1.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCardMetaComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-card-meta',\n      exportAs: 'nzCardMeta',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @if (nzAvatar) {\n      <div class=\"ant-card-meta-avatar\">\n        <ng-template [ngTemplateOutlet]=\"nzAvatar\" />\n      </div>\n    }\n\n    @if (nzTitle || nzDescription) {\n      <div class=\"ant-card-meta-detail\">\n        @if (nzTitle) {\n          <div class=\"ant-card-meta-title\">\n            <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n          </div>\n        }\n        @if (nzDescription) {\n          <div class=\"ant-card-meta-description\">\n            <ng-container *nzStringTemplateOutlet=\"nzDescription\">{{ nzDescription }}</ng-container>\n          </div>\n        }\n      </div>\n    }\n  `,\n      host: {\n        class: 'ant-card-meta'\n      },\n      imports: [NgIf, NgTemplateOutlet, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [], {\n    nzTitle: [{\n      type: Input\n    }],\n    nzDescription: [{\n      type: Input\n    }],\n    nzAvatar: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCardTabComponent {\n  static {\n    this.ɵfac = function NzCardTabComponent_Factory(t) {\n      return new (t || NzCardTabComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCardTabComponent,\n      selectors: [[\"nz-card-tab\"]],\n      viewQuery: function NzCardTabComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        }\n      },\n      exportAs: [\"nzCardTab\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzCardTabComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzCardTabComponent_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCardTabComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-card-tab',\n      exportAs: 'nzCardTab',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME = 'card';\nclass NzCardComponent {\n  constructor(nzConfigService, cdr, directionality) {\n    this.nzConfigService = nzConfigService;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzBordered = true;\n    this.nzBorderless = false;\n    this.nzLoading = false;\n    this.nzHoverable = false;\n    this.nzBodyStyle = null;\n    this.nzActions = [];\n    this.nzType = null;\n    this.nzSize = 'default';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzCardComponent_Factory(t) {\n      return new (t || NzCardComponent)(i0.ɵɵdirectiveInject(i1$1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCardComponent,\n      selectors: [[\"nz-card\"]],\n      contentQueries: function NzCardComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzCardTabComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzCardGridDirective, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzCardTabComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzCardGridDirective = _t);\n        }\n      },\n      hostAttrs: [1, \"ant-card\"],\n      hostVars: 16,\n      hostBindings: function NzCardComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-card-loading\", ctx.nzLoading)(\"ant-card-bordered\", ctx.nzBorderless === false && ctx.nzBordered)(\"ant-card-hoverable\", ctx.nzHoverable)(\"ant-card-small\", ctx.nzSize === \"small\")(\"ant-card-contain-grid\", ctx.listOfNzCardGridDirective && ctx.listOfNzCardGridDirective.length)(\"ant-card-type-inner\", ctx.nzType === \"inner\")(\"ant-card-contain-tabs\", !!ctx.listOfNzCardTabComponent)(\"ant-card-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzBordered: \"nzBordered\",\n        nzBorderless: \"nzBorderless\",\n        nzLoading: \"nzLoading\",\n        nzHoverable: \"nzHoverable\",\n        nzBodyStyle: \"nzBodyStyle\",\n        nzCover: \"nzCover\",\n        nzActions: \"nzActions\",\n        nzType: \"nzType\",\n        nzSize: \"nzSize\",\n        nzTitle: \"nzTitle\",\n        nzExtra: \"nzExtra\"\n      },\n      exportAs: [\"nzCard\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 5,\n      consts: [[1, \"ant-card-head\"], [1, \"ant-card-cover\"], [1, \"ant-card-body\", 3, \"ngStyle\"], [3, \"nzActive\", \"nzTitle\", \"nzParagraph\"], [1, \"ant-card-actions\"], [1, \"ant-card-head-wrapper\"], [1, \"ant-card-head-title\"], [1, \"ant-card-extra\"], [3, \"ngTemplateOutlet\"], [4, \"nzStringTemplateOutlet\"], [3, \"width\"]],\n      template: function NzCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzCardComponent_Conditional_0_Template, 5, 3, \"div\", 0)(1, NzCardComponent_Conditional_1_Template, 2, 1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, NzCardComponent_Conditional_3_Template, 1, 4, \"nz-skeleton\", 3)(4, NzCardComponent_Conditional_4_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, NzCardComponent_Conditional_5_Template, 3, 0, \"ul\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.nzTitle || ctx.nzExtra || ctx.listOfNzCardTabComponent ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.nzCover ? 1 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", ctx.nzBodyStyle);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx.nzLoading ? 3 : 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(5, ctx.nzActions.length ? 5 : -1);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective, NgTemplateOutlet, NgStyle, NzSkeletonModule, i4.NzSkeletonComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig(), InputBoolean()], NzCardComponent.prototype, \"nzBordered\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzCardComponent.prototype, \"nzBorderless\", void 0);\n__decorate([InputBoolean()], NzCardComponent.prototype, \"nzLoading\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzCardComponent.prototype, \"nzHoverable\", void 0);\n__decorate([WithConfig()], NzCardComponent.prototype, \"nzSize\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCardComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-card',\n      exportAs: 'nzCard',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @if (nzTitle || nzExtra || listOfNzCardTabComponent) {\n      <div class=\"ant-card-head\">\n        <div class=\"ant-card-head-wrapper\">\n          @if (nzTitle) {\n            <div class=\"ant-card-head-title\">\n              <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n            </div>\n          }\n          @if (nzExtra) {\n            <div class=\"ant-card-extra\">\n              <ng-container *nzStringTemplateOutlet=\"nzExtra\">{{ nzExtra }}</ng-container>\n            </div>\n          }\n        </div>\n        @if (listOfNzCardTabComponent) {\n          <ng-template [ngTemplateOutlet]=\"listOfNzCardTabComponent.template\" />\n        }\n      </div>\n    }\n\n    @if (nzCover) {\n      <div class=\"ant-card-cover\">\n        <ng-template [ngTemplateOutlet]=\"nzCover\" />\n      </div>\n    }\n\n    <div class=\"ant-card-body\" [ngStyle]=\"nzBodyStyle\">\n      @if (nzLoading) {\n        <nz-skeleton [nzActive]=\"true\" [nzTitle]=\"false\" [nzParagraph]=\"{ rows: 4 }\"></nz-skeleton>\n      } @else {\n        <ng-content />\n      }\n    </div>\n    @if (nzActions.length) {\n      <ul class=\"ant-card-actions\">\n        @for (action of nzActions; track $index) {\n          <li [style.width.%]=\"100 / nzActions.length\">\n            <span><ng-template [ngTemplateOutlet]=\"action\" /></span>\n          </li>\n        }\n      </ul>\n    }\n  `,\n      host: {\n        class: 'ant-card',\n        '[class.ant-card-loading]': 'nzLoading',\n        '[class.ant-card-bordered]': 'nzBorderless === false && nzBordered',\n        '[class.ant-card-hoverable]': 'nzHoverable',\n        '[class.ant-card-small]': 'nzSize === \"small\"',\n        '[class.ant-card-contain-grid]': 'listOfNzCardGridDirective && listOfNzCardGridDirective.length',\n        '[class.ant-card-type-inner]': 'nzType === \"inner\"',\n        '[class.ant-card-contain-tabs]': '!!listOfNzCardTabComponent',\n        '[class.ant-card-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzOutletModule, NgTemplateOutlet, NgStyle, NzSkeletonModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzBordered: [{\n      type: Input\n    }],\n    nzBorderless: [{\n      type: Input\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzHoverable: [{\n      type: Input\n    }],\n    nzBodyStyle: [{\n      type: Input\n    }],\n    nzCover: [{\n      type: Input\n    }],\n    nzActions: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzExtra: [{\n      type: Input\n    }],\n    listOfNzCardTabComponent: [{\n      type: ContentChild,\n      args: [NzCardTabComponent, {\n        static: false\n      }]\n    }],\n    listOfNzCardGridDirective: [{\n      type: ContentChildren,\n      args: [NzCardGridDirective]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCardModule {\n  static {\n    this.ɵfac = function NzCardModule_Factory(t) {\n      return new (t || NzCardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzCardModule,\n      imports: [NzCardComponent, NzCardGridDirective, NzCardMetaComponent, NzCardTabComponent],\n      exports: [BidiModule, NzCardComponent, NzCardGridDirective, NzCardMetaComponent, NzCardTabComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzCardComponent, NzCardMetaComponent, BidiModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzCardComponent, NzCardGridDirective, NzCardMetaComponent, NzCardTabComponent],\n      exports: [BidiModule, NzCardComponent, NzCardGridDirective, NzCardMetaComponent, NzCardTabComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCardComponent, NzCardGridDirective, NzCardMetaComponent, NzCardModule, NzCardTabComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,MAAM,CAAC,UAAU,QAAQ;AAC/B,IAAM,MAAM,CAAC,UAAU,QAAQ;AAC/B,IAAM,MAAM,CAAC,UAAU,OAAO;AAC9B,IAAM,MAAM,CAAC,UAAU,OAAO;AAC9B,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,uBAAuB,CAAC;AACxC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO,OAAO,QAAQ,SAAS,EAAE,WAAW,OAAO,OAAO,SAAS,QAAQ;AAAA,EACrG;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,CAAC;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,UAAU,OAAO,MAAM,KAAK,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,IAAI;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,OAAO,UAAU,OAAO,UAAU,IAAI,CAAC,CAAC;AAAA,EAClE;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,MAAM,CAAC;AACrF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,QAAQ;AAAA,EAC1C;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,OAAO,CAAC;AAClF,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,kDAAkD,GAAG,GAAG,MAAM,CAAC;AACpJ,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,CAAC,OAAO,QAAQ;AACvC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,CAAC,OAAO,OAAO;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,CAAC,OAAO,WAAW;AAAA,EAC5C;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,aAAa,CAAC;AACjB,IAAG,sBAAsB;AAAA,EAC3B;AACF;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA4B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,WAAW,CAAC,GAAG,gBAAgB,sBAAsB;AAAA,MACrD,UAAU;AAAA,MACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,QAAQ,EAAE,sBAAsB,IAAI,OAAO;AAAA,QACvF;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,2BAA2B,WAAW,WAAW,MAAM;AAAA,CACnF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,+BAA+B;AAAA,QAC/B,8BAA8B;AAAA,MAChC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,GAAG;AAC/D,aAAO,KAAK,KAAK,mCAAkC;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,UAAU,QAAQ,CAAC;AAAA,MACvD,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,qBAAqB,CAAC;AAAA,MACnC,UAAU,SAAS,0CAA0C,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,QAC3B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,8BAA8B,IAAI,YAAY,QAAQ,EAAE,6BAA6B,IAAI,YAAY,OAAO,EAAE,8BAA8B,IAAI,YAAY,QAAQ,EAAE,0BAA0B,IAAI,WAAW,OAAO,EAAE,0BAA0B,IAAI,WAAW,OAAO;AAAA,QACzR;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,WAAW,CAAC;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU,OAAO,KAAK,WAAW,UAAU;AACrD,YAAM,aAAa,GAAG,KAAK,MAAM;AACjC,WAAK,WAAW;AAAA,QACd,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,eAAe;AAAA,MACjB;AAAA,IACF,OAAO;AACL,WAAK,WAAW,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,GAAG;AAC/D,aAAO,KAAK,KAAK,mCAAkC;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,UAAU,QAAQ,CAAC;AAAA,MACvD,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,uBAAuB,GAAG,SAAS,CAAC;AAAA,MACjD,UAAU,SAAS,0CAA0C,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,QAC3B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,8BAA8B,IAAI,YAAY,QAAQ,EAAE,8BAA8B,IAAI,YAAY,QAAQ,EAAE,0BAA0B,IAAI,WAAW,OAAO,EAAE,0BAA0B,IAAI,WAAW,OAAO;AACjO,UAAG,WAAW,WAAW,IAAI,QAAQ;AAAA,QACvC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUV,SAAS,CAAC,OAAO;AAAA,MACjB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC,cAAc;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wCAAwC,GAAG;AAC9D,aAAO,KAAK,KAAK,kCAAiC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,UAAU,OAAO,CAAC;AAAA,MACtD,QAAQ;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,oBAAoB,CAAC;AAAA,MAClC,UAAU,SAAS,yCAAyC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,QAC3B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,WAAW,OAAO,EAAE,yBAAyB,IAAI,WAAW,OAAO;AAAA,QACjH;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC,OAAO;AACL,SAAK,OAAO,SAAS,wCAAwC,GAAG;AAC9D,aAAO,KAAK,KAAK,kCAAiC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,UAAU,OAAO,CAAC;AAAA,MACtD,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,oBAAoB,GAAG,CAAC,WAAW,iBAAiB,SAAS,8BAA8B,GAAG,wBAAwB,GAAG,CAAC,KAAK,m3BAAm3B,GAAG,yBAAyB,CAAC;AAAA,MAC5hC,UAAU,SAAS,yCAAyC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,eAAe;AAClB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa,EAAE;AAAA,QACpB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,WAAW,CAAC;AACjB,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,UAAU,QAAQ,IAAI;AACpB,WAAO,WAAW,KAAK;AAAA,EACzB;AAAA,EACA,gBAAgB;AACd,UAAM,YAAY,CAAC,CAAC,KAAK;AACzB,UAAM,eAAe,CAAC,CAAC,KAAK;AAC5B,QAAI,QAAQ;AACZ,QAAI,CAAC,aAAa,cAAc;AAC9B,cAAQ;AAAA,IACV,WAAW,aAAa,cAAc;AACpC,cAAQ;AAAA,IACV;AACA,WAAO;AAAA,MACL;AAAA,OACG,KAAK,SAAS,KAAK,OAAO;AAAA,EAEjC;AAAA,EACA,iBAAiB;AACf,UAAM,QAAQ,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,cAAc,WAAW;AAC/D,UAAM,OAAO;AACb,WAAO;AAAA,MACL;AAAA,MACA;AAAA,OACG,KAAK,SAAS,KAAK,QAAQ;AAAA,EAElC;AAAA,EACA,oBAAoB;AAClB,UAAM,YAAY,CAAC,CAAC,KAAK;AACzB,UAAM,WAAW,CAAC,CAAC,KAAK;AACxB,UAAM,aAAa,CAAC;AAEpB,QAAI,CAAC,aAAa,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,QAAI,CAAC,aAAa,UAAU;AAC1B,iBAAW,OAAO;AAAA,IACpB,OAAO;AACL,iBAAW,OAAO;AAAA,IACpB;AACA,WAAO,kCACF,aACA,KAAK,SAAS,KAAK,WAAW;AAAA,EAErC;AAAA,EACA,SAAS,MAAM;AACb,WAAO,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;AAAA,EACpD;AAAA,EACA,eAAe;AACb,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,YAAY,CAAC;AACjB,QAAI,SAAS,MAAM,QAAQ,KAAK,GAAG;AACjC,kBAAY;AAAA,IACd,WAAW,SAAS,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzC,kBAAY,CAAC;AACb,gBAAU,OAAO,CAAC,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,KAAK,cAAc;AAChC,SAAK,SAAS,KAAK,eAAe;AAClC,SAAK,YAAY,KAAK,kBAAkB;AACxC,SAAK,WAAW,CAAC,GAAG,MAAM,KAAK,UAAU,IAAI,CAAC;AAC9C,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,WAAW,QAAQ,YAAY,QAAQ,aAAa;AAC9D,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAqB,iBAAiB,CAAC;AAAA,IAClF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,MAC3B,WAAW,CAAC,GAAG,cAAc;AAAA,MAC7B,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,4BAA4B,CAAC,CAAC,IAAI,QAAQ,EAAE,uBAAuB,IAAI,QAAQ,EAAE,sBAAsB,CAAC,CAAC,IAAI,OAAO;AAAA,QACrI;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,uBAAuB,GAAG,MAAM,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,SAAS,sBAAsB,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,0BAA0B,GAAG,MAAM,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,UAAU,UAAU,GAAG,UAAU,SAAS,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS,SAAS,CAAC;AAAA,MAC/W,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,6CAA6C,GAAG,GAAG,gBAAgB,CAAC;AAAA,QAChK;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,SAAS;AACnC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAC,IAAI,SAAS;AAAA,QACtC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,4BAA4B,kCAAkC,MAAM,OAAO;AAAA,MAC1F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,oCAAoC;AAAA,QACpC,+BAA+B;AAAA,QAC/B,8BAA8B;AAAA,MAChC;AAAA,MACA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoBV,SAAS,CAAC,4BAA4B,kCAAkC,MAAM,OAAO;AAAA,MACrF,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,4BAA4B,qBAAqB,kCAAkC,kCAAkC,iCAAiC,+BAA+B;AAAA,MAC/L,SAAS,CAAC,4BAA4B,qBAAqB,kCAAkC,kCAAkC,iCAAiC,+BAA+B;AAAA,IACjM,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,4BAA4B,qBAAqB,kCAAkC,kCAAkC,iCAAiC,+BAA+B;AAAA,MAC/L,SAAS,CAAC,4BAA4B,qBAAqB,kCAAkC,kCAAkC,iCAAiC,+BAA+B;AAAA,IACjM,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC1lBH,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,QAAQ;AAAA,EACnD;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,gBAAgB,CAAC;AACjH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO;AAAA,EACxD;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,aAAa;AAAA,EAC3C;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,gBAAgB,CAAC;AACjH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,aAAa;AAAA,EAC9D;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,0DAA0D,GAAG,GAAG,OAAO,CAAC;AACtK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,UAAU,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,gBAAgB,IAAI,EAAE;AAAA,EACnD;AACF;AACA,IAAMA,OAAM,CAAC,GAAG;AAChB,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,IAAMC,OAAM,OAAO;AAAA,EACjB,MAAM;AACR;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,gBAAgB,CAAC;AAC7G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO;AAAA,EACxD;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,qEAAqE,GAAG,GAAG,gBAAgB,CAAC;AAC7G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO;AAAA,EACxD;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AAAC;AACtF,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oEAAoE,GAAG,GAAG,eAAe,CAAC;AAAA,EAC7G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,yBAAyB,QAAQ;AAAA,EAC5E;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,sDAAsD,GAAG,GAAG,OAAO,CAAC;AAC9J,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,MAAM,CAAC;AACpF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,GAAG,OAAO,UAAU,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,UAAU,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,2BAA2B,IAAI,EAAE;AAAA,EAC9D;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,eAAe,CAAC;AAC7F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,OAAO;AAAA,EAClD;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,CAAC;AAAA,EAClC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,YAAY,IAAI,EAAE,WAAW,KAAK,EAAE,eAAkB,gBAAgB,GAAGA,IAAG,CAAC;AAAA,EAC7F;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAAC;AAC9E,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI,EAAE,GAAG,MAAM;AACpC,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,eAAe,CAAC;AACnG,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,MAAM,OAAO,UAAU,QAAQ,GAAG;AAC1D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,SAAS;AAAA,EAC7C;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,iBAAiB,GAAG,8CAA8C,GAAG,GAAG,MAAM,IAAO,sBAAsB;AAC9G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,SAAS;AAAA,EAChC;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,sBAAsB,IAAI,WAAW;AAAA,QACtD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,eAAe,MAAM;AAAA,CAChF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,8BAA8B;AAAA,MAChC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,eAAe;AAAA,QACf,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACvL,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,4CAA4C,GAAG,GAAG,OAAO,CAAC;AAAA,QAC5I;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,WAAW,IAAI,EAAE;AACzC,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,WAAW,IAAI,gBAAgB,IAAI,EAAE;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,gBAAmB,+BAA+B;AAAA,MACnF,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,MAAM,kBAAkB,cAAc;AAAA,MAChD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,MAC3B,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,CAAC;AAAA,QAC/B;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,QACjE;AAAA,MACF;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoBD;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,aAAa;AAAA,QACjF;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAC9B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,iBAAiB,KAAK,gBAAgB;AAChD,SAAK,kBAAkB;AACvB,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,YAAY,CAAC;AAClB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,gBAAgB,iCAAiC,qBAAqB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1H,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,kBAAuB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACtK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,MACvB,gBAAgB,SAAS,+BAA+B,IAAI,KAAK,UAAU;AACzE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,oBAAoB,CAAC;AACjD,UAAG,eAAe,UAAU,qBAAqB,CAAC;AAAA,QACpD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAC/E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B;AAAA,QAC/E;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,UAAU;AAAA,MACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,oBAAoB,IAAI,SAAS,EAAE,qBAAqB,IAAI,iBAAiB,SAAS,IAAI,UAAU,EAAE,sBAAsB,IAAI,WAAW,EAAE,kBAAkB,IAAI,WAAW,OAAO,EAAE,yBAAyB,IAAI,6BAA6B,IAAI,0BAA0B,MAAM,EAAE,uBAAuB,IAAI,WAAW,OAAO,EAAE,yBAAyB,CAAC,CAAC,IAAI,wBAAwB,EAAE,gBAAgB,IAAI,QAAQ,KAAK;AAAA,QACjb;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,QACb,SAAS;AAAA,QACT,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,QAAQ;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoBA;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,iBAAiB,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,WAAW,aAAa,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,OAAO,CAAC;AAAA,MACnT,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC;AAClI,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,wCAAwC,GAAG,CAAC;AAChI,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,MAAM,CAAC;AAAA,QACxE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,WAAW,IAAI,WAAW,IAAI,2BAA2B,IAAI,EAAE;AACvF,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,UAAU,IAAI,EAAE;AACxC,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,WAAW;AACxC,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,YAAY,IAAI,CAAC;AACzC,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,UAAU,SAAS,IAAI,EAAE;AAAA,QACnD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,iCAAiC,kBAAkB,SAAS,kBAAqB,mBAAmB;AAAA,MACtI,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,gBAAgB,WAAW,cAAc,MAAM;AAC1F,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,gBAAgB,WAAW,gBAAgB,MAAM;AAC5F,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,aAAa,MAAM;AAC3E,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,gBAAgB,WAAW,eAAe,MAAM;AAC3F,WAAW,CAAC,WAAW,CAAC,GAAG,gBAAgB,WAAW,UAAU,MAAM;AAAA,CACrE,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4CV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,4BAA4B;AAAA,QAC5B,6BAA6B;AAAA,QAC7B,8BAA8B;AAAA,QAC9B,0BAA0B;AAAA,QAC1B,iCAAiC;AAAA,QACjC,+BAA+B;AAAA,QAC/B,iCAAiC;AAAA,QACjC,wBAAwB;AAAA,MAC1B;AAAA,MACA,SAAS,CAAC,gBAAgB,kBAAkB,SAAS,gBAAgB;AAAA,MACrE,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,qBAAqB,qBAAqB,kBAAkB;AAAA,MACvF,SAAS,CAAC,YAAY,iBAAiB,qBAAqB,qBAAqB,kBAAkB;AAAA,IACrG,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,qBAAqB,UAAU;AAAA,IAC5D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,qBAAqB,qBAAqB,kBAAkB;AAAA,MACvF,SAAS,CAAC,YAAY,iBAAiB,qBAAqB,qBAAqB,kBAAkB;AAAA,IACrG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["_c0", "_c1"]}