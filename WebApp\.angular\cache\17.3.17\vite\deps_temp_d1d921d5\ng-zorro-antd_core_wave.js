import {
  NZ_WAVE_GLOBAL_CONFIG,
  NZ_WAVE_GLOBAL_DEFAULT_CONFIG,
  NzWaveDirective,
  NzWaveModule,
  Nz<PERSON><PERSON><PERSON><PERSON><PERSON>,
  provideNzWave
} from "./chunk-FCNXPFGK.js";
import "./chunk-VXBOBZVR.js";
import "./chunk-X43V2MJH.js";
import "./chunk-YM7A35BP.js";
import "./chunk-GTP7WLQD.js";
import "./chunk-QFU5VVIG.js";
import "./chunk-LXN5S2GR.js";
import "./chunk-H3UNIER7.js";
import "./chunk-7VXZRWVL.js";
import "./chunk-DARGOXGJ.js";
import "./chunk-HQARRG7I.js";
import "./chunk-4A64JP2N.js";
import "./chunk-EIB7IA3J.js";
export {
  NZ_WAVE_GLOBAL_CONFIG,
  NZ_WAVE_GLOBAL_DEFAULT_CONFIG,
  <PERSON>zWaveDirective,
  NzWaveModule,
  Nz<PERSON><PERSON><PERSON><PERSON><PERSON>,
  provideNz<PERSON><PERSON>
};
//# sourceMappingURL=ng-zorro-antd_core_wave.js.map
