import {
  DATE_HELPER_SERVICE_FACTORY,
  DateHelperByDateFns,
  DateHelperByDatePipe,
  DateHelperService,
  NZ_DATE_CONFIG,
  NZ_DATE_CONFIG_DEFAULT,
  NZ_DATE_LOCALE,
  NZ_I18N,
  NzI18nModule,
  NzI18nPipe,
  NzI18nService,
  ar_EG,
  az_AZ,
  bg_BG,
  bn_BD,
  by_BY,
  ca_ES,
  cs_CZ,
  da_DK,
  de_DE,
  el_GR,
  en_GB,
  en_US,
  es_ES,
  et_EE,
  fa_IR,
  fi_FI,
  fr_BE,
  fr_CA,
  fr_FR,
  ga_IE,
  gl_ES,
  he_IL,
  hi_IN,
  hr_HR,
  hu_HU,
  hy_AM,
  id_ID,
  is_IS,
  it_IT,
  ja_JP,
  ka_GE,
  kk_KZ,
  km_KH,
  kmr_IQ,
  kn_IN,
  ko_KR,
  ku_IQ,
  lt_LT,
  lv_LV,
  mergeDateConfig,
  mk_MK,
  ml_IN,
  mn_MN,
  ms_MY,
  nb_NO,
  ne_NP,
  nl_BE,
  nl_NL,
  pl_PL,
  provideNzI18n,
  pt_BR,
  pt_PT,
  ro_RO,
  ru_RU,
  sk_SK,
  sl_SI,
  sr_RS,
  sv_SE,
  ta_IN,
  th_TH,
  tr_TR,
  uk_UA,
  ur_PK,
  vi_VN,
  zh_CN,
  zh_HK,
  zh_TW
} from "./chunk-AEAFDSWJ.js";
import "./chunk-72XRV7AY.js";
import "./chunk-5BZR2V47.js";
import "./chunk-LN22JCXK.js";
import "./chunk-H3UNIER7.js";
import "./chunk-7VXZRWVL.js";
import "./chunk-DARGOXGJ.js";
import "./chunk-HQARRG7I.js";
import "./chunk-4A64JP2N.js";
import "./chunk-EIB7IA3J.js";
export {
  DATE_HELPER_SERVICE_FACTORY,
  DateHelperByDateFns,
  DateHelperByDatePipe,
  DateHelperService,
  NZ_DATE_CONFIG,
  NZ_DATE_CONFIG_DEFAULT,
  NZ_DATE_LOCALE,
  NZ_I18N,
  NzI18nModule,
  NzI18nPipe,
  NzI18nService,
  ar_EG,
  az_AZ,
  bg_BG,
  bn_BD,
  by_BY,
  ca_ES,
  cs_CZ,
  da_DK,
  de_DE,
  el_GR,
  en_GB,
  en_US,
  es_ES,
  et_EE,
  fa_IR,
  fi_FI,
  fr_BE,
  fr_CA,
  fr_FR,
  ga_IE,
  gl_ES,
  he_IL,
  hi_IN,
  hr_HR,
  hu_HU,
  hy_AM,
  id_ID,
  is_IS,
  it_IT,
  ja_JP,
  ka_GE,
  kk_KZ,
  km_KH,
  kmr_IQ,
  kn_IN,
  ko_KR,
  ku_IQ,
  lt_LT,
  lv_LV,
  mergeDateConfig,
  mk_MK,
  ml_IN,
  mn_MN,
  ms_MY,
  nb_NO,
  ne_NP,
  nl_BE,
  nl_NL,
  pl_PL,
  provideNzI18n,
  pt_BR,
  pt_PT,
  ro_RO,
  ru_RU,
  sk_SK,
  sl_SI,
  sr_RS,
  sv_SE,
  ta_IN,
  th_TH,
  tr_TR,
  uk_UA,
  ur_PK,
  vi_VN,
  zh_CN,
  zh_HK,
  zh_TW
};
//# sourceMappingURL=ng-zorro-antd_i18n.js.map
