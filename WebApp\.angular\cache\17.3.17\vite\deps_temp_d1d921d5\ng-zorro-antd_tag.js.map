{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-tag.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, Output, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { presetColors, statusColors, isPresetColor, isStatusColor } from 'ng-zorro-antd/core/color';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i1 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nfunction NzTagComponent_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵlistener(\"click\", function NzTagComponent_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTag($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nclass NzTagComponent {\n  constructor(cdr, renderer, elementRef, directionality) {\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    this.isPresetColor = false;\n    this.nzMode = 'default';\n    this.nzChecked = false;\n    this.nzBordered = true;\n    this.nzOnClose = new EventEmitter();\n    this.nzCheckedChange = new EventEmitter();\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  updateCheckedStatus() {\n    if (this.nzMode === 'checkable') {\n      this.nzChecked = !this.nzChecked;\n      this.nzCheckedChange.emit(this.nzChecked);\n    }\n  }\n  closeTag(e) {\n    this.nzOnClose.emit(e);\n    if (!e.defaultPrevented) {\n      this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n    }\n  }\n  clearPresetColor() {\n    const hostElement = this.elementRef.nativeElement;\n    // /(ant-tag-(?:pink|red|...))/g\n    const regexp = new RegExp(`(ant-tag-(?:${[...presetColors, ...statusColors].join('|')}))`, 'g');\n    const classname = hostElement.classList.toString();\n    const matches = [];\n    let match = regexp.exec(classname);\n    while (match !== null) {\n      matches.push(match[1]);\n      match = regexp.exec(classname);\n    }\n    hostElement.classList.remove(...matches);\n  }\n  setPresetColor() {\n    const hostElement = this.elementRef.nativeElement;\n    this.clearPresetColor();\n    if (!this.nzColor) {\n      this.isPresetColor = false;\n    } else {\n      this.isPresetColor = isPresetColor(this.nzColor) || isStatusColor(this.nzColor);\n    }\n    if (this.isPresetColor) {\n      hostElement.classList.add(`ant-tag-${this.nzColor}`);\n    }\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzColor\n    } = changes;\n    if (nzColor) {\n      this.setPresetColor();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTagComponent_Factory(t) {\n      return new (t || NzTagComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTagComponent,\n      selectors: [[\"nz-tag\"]],\n      hostAttrs: [1, \"ant-tag\"],\n      hostVars: 12,\n      hostBindings: function NzTagComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzTagComponent_click_HostBindingHandler() {\n            return ctx.updateCheckedStatus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"background-color\", ctx.isPresetColor ? \"\" : ctx.nzColor);\n          i0.ɵɵclassProp(\"ant-tag-has-color\", ctx.nzColor && !ctx.isPresetColor)(\"ant-tag-checkable\", ctx.nzMode === \"checkable\")(\"ant-tag-checkable-checked\", ctx.nzChecked)(\"ant-tag-rtl\", ctx.dir === \"rtl\")(\"ant-tag-borderless\", !ctx.nzBordered);\n        }\n      },\n      inputs: {\n        nzMode: \"nzMode\",\n        nzColor: \"nzColor\",\n        nzChecked: \"nzChecked\",\n        nzBordered: \"nzBordered\"\n      },\n      outputs: {\n        nzOnClose: \"nzOnClose\",\n        nzCheckedChange: \"nzCheckedChange\"\n      },\n      exportAs: [\"nzTag\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[\"nz-icon\", \"\", \"nzType\", \"close\", \"class\", \"ant-tag-close-icon\", \"tabindex\", \"-1\", 3, \"click\", 4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"close\", \"tabindex\", \"-1\", 1, \"ant-tag-close-icon\", 3, \"click\"]],\n      template: function NzTagComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, NzTagComponent_span_1_Template, 1, 0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzMode === \"closeable\");\n        }\n      },\n      dependencies: [NzIconModule, i2.NzIconDirective, NgIf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzTagComponent.prototype, \"nzChecked\", void 0);\n__decorate([InputBoolean()], NzTagComponent.prototype, \"nzBordered\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTagComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tag',\n      exportAs: 'nzTag',\n      preserveWhitespaces: false,\n      template: `\n    <ng-content></ng-content>\n    <span\n      nz-icon\n      nzType=\"close\"\n      class=\"ant-tag-close-icon\"\n      *ngIf=\"nzMode === 'closeable'\"\n      tabindex=\"-1\"\n      (click)=\"closeTag($event)\"\n    ></span>\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'ant-tag',\n        '[style.background-color]': `isPresetColor ? '' : nzColor`,\n        '[class.ant-tag-has-color]': `nzColor && !isPresetColor`,\n        '[class.ant-tag-checkable]': `nzMode === 'checkable'`,\n        '[class.ant-tag-checkable-checked]': `nzChecked`,\n        '[class.ant-tag-rtl]': `dir === 'rtl'`,\n        '[class.ant-tag-borderless]': `!nzBordered`,\n        '(click)': 'updateCheckedStatus()'\n      },\n      imports: [NzIconModule, NgIf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzMode: [{\n      type: Input\n    }],\n    nzColor: [{\n      type: Input\n    }],\n    nzChecked: [{\n      type: Input\n    }],\n    nzBordered: [{\n      type: Input\n    }],\n    nzOnClose: [{\n      type: Output\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTagModule {\n  static {\n    this.ɵfac = function NzTagModule_Factory(t) {\n      return new (t || NzTagModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzTagModule,\n      imports: [NzTagComponent],\n      exports: [NzTagComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzTagComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTagModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTagComponent],\n      exports: [NzTagComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzTagComponent, NzTagModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,SAAS,SAAS,qDAAqD,QAAQ;AAC3F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,KAAK,UAAU,YAAY,gBAAgB;AACrD,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,WAAW,aAAa;AAC/B,WAAK,YAAY,CAAC,KAAK;AACvB,WAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,SAAS,GAAG;AACV,SAAK,UAAU,KAAK,CAAC;AACrB,QAAI,CAAC,EAAE,kBAAkB;AACvB,WAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,KAAK,WAAW,aAAa;AAAA,IAClH;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,cAAc,KAAK,WAAW;AAEpC,UAAM,SAAS,IAAI,OAAO,eAAe,CAAC,GAAG,cAAc,GAAG,YAAY,EAAE,KAAK,GAAG,CAAC,MAAM,GAAG;AAC9F,UAAM,YAAY,YAAY,UAAU,SAAS;AACjD,UAAM,UAAU,CAAC;AACjB,QAAI,QAAQ,OAAO,KAAK,SAAS;AACjC,WAAO,UAAU,MAAM;AACrB,cAAQ,KAAK,MAAM,CAAC,CAAC;AACrB,cAAQ,OAAO,KAAK,SAAS;AAAA,IAC/B;AACA,gBAAY,UAAU,OAAO,GAAG,OAAO;AAAA,EACzC;AAAA,EACA,iBAAiB;AACf,UAAM,cAAc,KAAK,WAAW;AACpC,SAAK,iBAAiB;AACtB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,gBAAgB,cAAc,KAAK,OAAO,KAAK,cAAc,KAAK,OAAO;AAAA,IAChF;AACA,QAAI,KAAK,eAAe;AACtB,kBAAY,UAAU,IAAI,WAAW,KAAK,OAAO,EAAE;AAAA,IACrD;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAClM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,MACtB,WAAW,CAAC,GAAG,SAAS;AAAA,MACxB,UAAU;AAAA,MACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,0CAA0C;AACxE,mBAAO,IAAI,oBAAoB;AAAA,UACjC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,oBAAoB,IAAI,gBAAgB,KAAK,IAAI,OAAO;AACvE,UAAG,YAAY,qBAAqB,IAAI,WAAW,CAAC,IAAI,aAAa,EAAE,qBAAqB,IAAI,WAAW,WAAW,EAAE,6BAA6B,IAAI,SAAS,EAAE,eAAe,IAAI,QAAQ,KAAK,EAAE,sBAAsB,CAAC,IAAI,UAAU;AAAA,QAC7O;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,QACX,YAAY;AAAA,MACd;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,QACX,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,OAAO;AAAA,MAClB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,IAAI,UAAU,SAAS,SAAS,sBAAsB,YAAY,MAAM,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,YAAY,MAAM,GAAG,sBAAsB,GAAG,OAAO,CAAC;AAAA,MAC9M,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,QAAQ,CAAC;AAAA,QAClE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,WAAW,WAAW;AAAA,QAClD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,IAAI;AAAA,MACrD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,eAAe,WAAW,aAAa,MAAM;AAC1E,WAAW,CAAC,aAAa,CAAC,GAAG,eAAe,WAAW,cAAc,MAAM;AAAA,CAC1E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,4BAA4B;AAAA,QAC5B,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,uBAAuB;AAAA,QACvB,8BAA8B;AAAA,QAC9B,WAAW;AAAA,MACb;AAAA,MACA,SAAS,CAAC,cAAc,IAAI;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAa;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,cAAc;AAAA,MACxB,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc;AAAA,MACxB,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}